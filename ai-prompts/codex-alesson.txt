----------------------------------------

[Date] 2025-09-10
[Target Summary]
- 調整說明
    - 位置: PartnerProfileService.convertToRespPartnerProfileQuery(...)
    - 行為: 以 fieldMaskingUtils.canViewAmountFields(PermissionName.PARTNER) 檢查「可見金額」權限
    - 有權限 → 回傳 `retentionPercentage`、`commissionPercentage`
    - 無權限 → 兩欄位回傳 `null`
- 影響範圍
    - 後台查詢單筆供應商與清單 API 都會透過上述方法轉換，因此一併生效
    - 匯出供應商 Excel 目前未包含這兩欄位，無影響
- 相關檔案
    - 更新: api-server/src/main/java/com/jgallop/api/service/PartnerProfileService.java
[Prompt]
下一個問題。

以 asus_02 下的員工 ASUS007 登入後台操作
檢視供應商時，需要有「可見金額」的權限才可以看到「給予佣金%」、「給予保留%」。

----------------------------------------

[Date] 2025-09-10
[Target Summary]
- 承辦人員為空白: Fixed fallback mapping
    - Where: TransactionAgreementHelper.buildTransactionAgreementData(...)
    - Change: If partnerInspector does not resolve to a user zhName via systemAccountHelper.findUserNameByUserId(...), it now falls back to the raw value stored in the inspection form. This covers cases where the field was saved as a
free-text name instead of a userId.
    - Files:
    - `api-server/src/main/java/com/jgallop/api/service/TransactionAgreementHelper.java`

- 侵權必究 Ⓒ 年份統一為現年
    - Behavior: Footer now shows “創宇數位科技 版權所有，侵權必究 Ⓒ ” using the current year dynamically (e.g., 2025).
    - Where: TransactionAgreementHelper#createCopyrightCell
    - Implementation: Build the string dynamically instead of using a hard-coded year. Also switched to the circled C character “Ⓒ” as requested.
    - Files:
    - `api-server/src/main/java/com/jgallop/api/service/TransactionAgreementHelper.java`
    - `api-server/src/main/java/com/jgallop/api/service/TransactionAgreementConstants.java` (kept a neutral base message; actual year comes from helper)

- 申報單位下方「地址」旁欄位的「。」位置
    - Current rendering: The “申報單位” block under the seller table uses LegalTermsConstants.DeclarationTexts.USAGE_RESTRICTION and is placed in a 6-column cell to the right of 賣方地址:
    - Code: `addSellerAddressAndNote(...)` places `DeclarationTexts.USAGE_RESTRICTION` into a 6-col cell next to the 3-col 地址值 cell.
[Prompt]
下一個問題。幫我查看買賣切結書顯示的欄位：

1. 承辦人員目前為空白
2. 申報單位下方地址旁欄位 "。"位置需調整
3. 侵權必究Ⓒ後面年份與現年(2025)統一

----------------------------------------

[Date] 2025-09-10
[Target Summary]
What’s happening

- Direct store path: Uses PartNumber.view_count, not PartNumberRecoveryDetailEntity’s supplier_view_count / external_view_count.
- Detail API: When a direct-store user views a product, it increments PartNumber.view_count and the detail response returns totalViewCount from that field.
- List API: Previously, the list/summary builder was hardcoded to 0 for direct-store (it constructed a dummy recovery detail), so totalInquiryCount stayed 0.

Change I made

- RecycleWebsiteMapper now sets list totalInquiryCount to PartNumber.view_count for direct store:
    - If recoveryDetailMap == null (direct store), supplierInquiryCount=0, externalInquiryCount=0, totalInquiryCount=partNumber.viewCount.
    - Supplier path remains unchanged (still sums supplier_view_count + external_view_count).
- Files updated:
    - api-server/src/main/java/com/jgallop/api/service/mapper/RecycleWebsiteMapper.java
[Prompt]
下一個問題。
創宇回收網站 smartmobile 查詢商品後 totalInquiryCount 沒有被更新
創宇回收網站 smartmobile 的 totalInquiryCount(已被查詢 x 次) 的更新機制 好像沒作用，目前不管查詢幾次都是 0
目前的處理邏輯是，如果是創宇直營門市就不會紀錄or回傳實際 totalInquiryCount 嗎？

----------------------------------------

[Date] 2025-09-10
[Target Summary]
- What I changed: In EmployeeService.getEmployeeModificationHistory(...), the creator now prefers systemAccountHelper.findUserNameByUserId(...) (employee zhName). If blank, it falls back to account, then userId.
- Files updated:
    - api-server/src/main/java/com/jgallop/api/service/EmployeeService.java: map creator to zhName with safe fallback.
    - api-server/src/main/java/com/jgallop/api/model/resp/RespEmployeeModificationHistory.java: updated comment to reflect new behavior.
[Prompt]
004 GET /api/employees/{employeeId}/modification-history
 res body 的 creator 欄位目前好像是 帳號名，可以改成回 user 的 zhName 嗎

----------------------------------------