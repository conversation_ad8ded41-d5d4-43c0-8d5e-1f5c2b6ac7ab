package com.jgallop.api.controller;

import com.jgallop.api.model.resp.RespBankBranchDropdown;
import com.jgallop.api.model.resp.RespBankCodeDropdown;
import com.jgallop.api.model.resp.RespBrandDropdown;
import com.jgallop.api.model.resp.RespCommunicationTypeDropdown;
import com.jgallop.api.model.resp.RespCompanyDropdown;
import com.jgallop.api.model.resp.RespCountryCodeDropdown;
import com.jgallop.api.model.resp.RespCurrencyDropdown;
import com.jgallop.api.model.resp.RespEmployeeDropdown;
import com.jgallop.api.model.resp.RespInspectorDropdown;
import com.jgallop.api.model.resp.RespPartnerProfileDropdown;
import com.jgallop.api.model.resp.RespPartnerTypeDropdown;
import com.jgallop.api.model.resp.RespPartNumberDropdown;
import com.jgallop.api.model.resp.RespProductSeriesTreeDropdown;
import com.jgallop.api.model.resp.RespLevel0Category;
import com.jgallop.api.model.resp.RespLevel1Category;
import com.jgallop.api.model.resp.RespLevel2Category;
import com.jgallop.api.model.resp.RespUserDropdown;
import com.jgallop.api.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 下拉選單控制器
 */
@Tag(name = "000-Dropdown", description = "下拉選單")
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping("/api/dropdown")
@RequiredArgsConstructor
@Validated
public class DropdownController {

    private final CompanyService companyService;
    private final EmployeeService employeeService;
    private final CurrencyService currencyService;
    private final PartnerProfileService partnerProfileService;
    private final CountryCodeService countryCodeService;
    private final BankCodeService bankCodeService;
    private final BankBranchService bankBranchService;
    private final BrandService brandService;
    private final ProductSeriesService productSeriesService;
    private final PartNumberService partNumberService;
    private final PartnerTypeService partnerTypeService;
    private final InspectorService inspectorService;
    private final UserService userService;
    private final ProductInfoService productInfoService;
    private final PartNumberCategoryService partNumberCategoryService;

    /**
     * 取得所有在職且啟用的員工（下拉選單）
     *
     * @return 在職且啟用的員工列表
     */
    @Operation(summary = "取得所有在職且啟用的員工（下拉選單）", description = "取得所有在職且啟用的員工，用於下拉選單")
    @GetMapping("/employees")
    public ResponseEntity<List<RespEmployeeDropdown>> getAllActiveEmployeesForDropdown() {
        var result = employeeService.getAllActiveEmployeesForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的公司別（下拉選單）
     *
     * @return 啟用的公司別列表
     */
    @Operation(summary = "取得所有啟用的公司別（下拉選單）", description = "取得所有啟用的公司別，用於下拉選單")
    @GetMapping("/companies")
    public ResponseEntity<List<RespCompanyDropdown>> getAllEnabledCompaniesForDropdown() {
        var result = companyService.getAllEnabledCompaniesForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的幣別（下拉選單）
     *
     * @return 啟用的幣別列表
     */
    @Operation(summary = "取得所有啟用的幣別（下拉選單）", description = "取得所有啟用的幣別，用於下拉選單")
    @GetMapping("/currencies")
    public ResponseEntity<List<RespCurrencyDropdown>> getAllEnabledCurrenciesForDropdown() {
        var result = currencyService.getAllEnabledCurrenciesForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的供應商（下拉選單）
     *
     * @return 啟用的供應商列表
     */
    @Operation(summary = "取得所有啟用的供應商（下拉選單）", description = "取得所有啟用的供應商，用於下拉選單")
    @GetMapping("/partners")
    public ResponseEntity<List<RespPartnerProfileDropdown>> getAllEnabledPartnersForDropdown() {
        var result = partnerProfileService.getAllEnabledPartnersForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的國家代碼（下拉選單）
     *
     * @return 啟用的國家代碼列表
     */
    @Operation(summary = "取得所有啟用的國家代碼（下拉選單）", description = "取得所有啟用的國家代碼，用於下拉選單")
    @GetMapping("/country-codes")
    public ResponseEntity<List<RespCountryCodeDropdown>> getAllEnabledCountryCodesForDropdown() {
        var result = countryCodeService.getAllEnabledCountryCodesForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 根據國家代碼取得啟用的銀行代碼（下拉選單）
     *
     * @param countryCodeId 國家代碼ID
     * @return 啟用的銀行代碼列表
     */
    @Operation(summary = "根據國家代碼取得啟用的銀行代碼（下拉選單）", description = "根據國家代碼取得啟用的銀行代碼，用於下拉選單")
    @GetMapping("/bank-codes")
    public ResponseEntity<List<RespBankCodeDropdown>> getAllEnabledBankCodesForDropdown(
            @Parameter(description = "國家代碼ID") @RequestParam String countryCodeId) {
        var result = bankCodeService.getEnabledBankCodesByCountryCodeForDropdown(countryCodeId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根據銀行代碼取得啟用的分行代碼（下拉選單）
     *
     * @param bankCodeId 銀行代碼ID
     * @return 啟用的分行代碼列表
     */
    @Operation(summary = "根據銀行代碼取得啟用的分行代碼（下拉選單）", description = "根據銀行代碼取得啟用的分行代碼，用於下拉選單")
    @GetMapping("/bank-branches")
    public ResponseEntity<List<RespBankBranchDropdown>> getEnabledBankBranchesByBankCodeForDropdown(
            @Parameter(description = "銀行代碼ID") @RequestParam String bankCodeId) {
        var result = bankBranchService.getEnabledBankBranchesByBankCodeForDropdown(bankCodeId);
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的品牌（下拉選單）
     *
     * @return 啟用的品牌列表
     */
    @Operation(summary = "取得所有啟用的品牌（下拉選單）", description = "取得所有啟用的品牌，用於下拉選單")
    @GetMapping("/brands")
    public ResponseEntity<List<RespBrandDropdown>> getAllEnabledBrandsForDropdown() {
        var result = brandService.getAllEnabledBrandsForDropdown();
        return ResponseEntity.ok(result);
    }

    // ===== Part Number Categories (Enabled only) =====

    /**
     * 取得啟用的大分類（下拉選單）
     */
    @Operation(summary = "取得啟用的大分類（下拉選單）", description = "取得所有啟用的大分類（level 0），用於新增料件時過濾")
    @GetMapping("/part-number-categories/level0")
    public ResponseEntity<List<RespLevel0Category>> getEnabledLevel0CategoriesForDropdown() {
        var result = partNumberCategoryService.getEnabledLevel0ForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得啟用的中分類（下拉選單）
     */
    @Operation(summary = "取得啟用的中分類（下拉選單）", description = "可指定大分類ID，僅回傳啟用的中分類（level 1）")
    @GetMapping("/part-number-categories/level1")
    public ResponseEntity<List<RespLevel1Category>> getEnabledLevel1CategoriesForDropdown(
            @Parameter(description = "大分類ID") @RequestParam(required = false) String level0Id) {
        var result = partNumberCategoryService.getEnabledLevel1ForDropdown(level0Id);
        return ResponseEntity.ok(result);
    }

    /**
     * 取得啟用的小分類（下拉選單）
     */
    @Operation(summary = "取得啟用的小分類（下拉選單）", description = "可指定大分類/中分類ID，僅回傳啟用的小分類（level 2）")
    @GetMapping("/part-number-categories/level2")
    public ResponseEntity<List<RespLevel2Category>> getEnabledLevel2CategoriesForDropdown(
            @Parameter(description = "大分類ID") @RequestParam(required = false) String level0Id,
            @Parameter(description = "中分類ID") @RequestParam(required = false) String level1Id) {
        var result = partNumberCategoryService.getEnabledLevel2ForDropdown(level0Id, level1Id);
        return ResponseEntity.ok(result);
    }

    /**
     * 根據品牌ID取得所有啟用的產品系列（樹狀下拉選單）
     *
     * @param brandId 品牌ID
     * @return 啟用的產品系列樹狀結構
     */
    @Operation(summary = "根據品牌ID取得所有啟用的產品系列（樹狀下拉選單）", description = "根據品牌ID取得所有啟用的產品系列，以樹狀結構返回，用於下拉選單")
    @GetMapping("/product-series")
    public ResponseEntity<List<RespProductSeriesTreeDropdown>> getEnabledProductSeriesByBrandForDropdown(
            @Parameter(description = "品牌ID") @RequestParam String brandId) {
        var result = productSeriesService.getEnabledProductSeriesByBrandForDropdown(brandId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根據產品系列ID取得所有料件（下拉選單）
     *
     * @param productSeriesId 產品系列ID
     * @return 料件列表
     */
    @Operation(summary = "根據產品系列ID取得所有料件（下拉選單）", description = "根據產品系列ID取得所有料件，用於下拉選單")
    @GetMapping("/part-numbers")
    public ResponseEntity<List<RespPartNumberDropdown>> getPartNumbersByProductSeriesForDropdown(
            @Parameter(description = "產品系列ID") @RequestParam String productSeriesId) {
        var result = partNumberService.getPartNumbersByProductSeriesForDropdown(productSeriesId);
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用的供應商類型（下拉選單）
     *
     * @return 啟用的供應商類型列表
     */
    @Operation(summary = "取得所有啟用的供應商類型（下拉選單）", description = "取得所有啟用的供應商類型，用於下拉選單")
    @GetMapping("/partner-types")
    public ResponseEntity<List<RespPartnerTypeDropdown>> getAllEnabledPartnerTypesForDropdown() {
        var result = partnerTypeService.getAllEnabledPartnerTypesForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 查詢檢查員下拉選單
     *
     * @param partnerCode 供應商代碼（來自 Header X-Partner-Code）
     * @return 檢查員下拉選單列表
     */
    @Operation(summary = "查詢檢查員下拉選單", description = "根據供應商代碼查詢可見的檢查員清單：供應商用戶看到該供應商底下人員，創宇用戶看到直營門市人員")
    @GetMapping("/inspector")
    public ResponseEntity<List<RespInspectorDropdown>> getInspectorForDropdown(
            @Parameter(description = "供應商代碼") @RequestHeader(value = "X-Partner-Code", required = false) String partnerCode) {
        var result = inspectorService.getInspectorDropdown(partnerCode);
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有啟用中用戶（下拉選單）
     *
     * @return 啟用中用戶列表
     */
    @Operation(summary = "取得所有啟用中用戶（下拉選單）", description = "取得所有啟用且在職的用戶（包含員工和供應商人員），用於下拉選單")
    @GetMapping("/users")
    public ResponseEntity<List<RespUserDropdown>> getAllEnabledUsersForDropdown() {
        var result = userService.getAllEnabledUsersForDropdown();
        return ResponseEntity.ok(result);
    }

    /**
     * 取得所有通訊類型（下拉選單）
     *
     * @return 通訊類型列表
     */
    @Operation(summary = "取得所有通訊類型（下拉選單）", description = "取得所有通訊類型，用於下拉選單")
    @GetMapping("/communication-types")
    public ResponseEntity<List<RespCommunicationTypeDropdown>> getAllCommunicationTypesForDropdown() {
        var result = productInfoService.getCommunicationTypeDropdown();
        return ResponseEntity.ok(result);
    }
}
