package com.jgallop.api.controller;

import com.jgallop.api.entity.enums.ProbationStatus;
import com.jgallop.api.model.req.*;
import com.jgallop.api.model.resp.*;
import com.jgallop.api.model.resp.RespEmployeeModificationHistory;
import com.jgallop.api.model.req.ReqEmployeeCreateModel;
import com.jgallop.api.service.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 員工管理 API
 */
@Tag(name = "004-Employee", description = "員工基本資料")
@SecurityRequirement(name = "bearerAuth")
@RestController
@Validated
@RequiredArgsConstructor
@RequestMapping("/api/employees")

public class EmployeeController {

    private final EmployeeService employeeService;
    private final BankAccountService bankAccountService;
    private final EmployeeUnitService employeeUnitService;
    private final EmployeeRoleService employeeRoleService;
    private final EmergencyContactService emergencyContactService;
    private final EmployeeDocumentService employeeDocumentService;

    /**
     * 建立員工基本資料
     *
     * @param reqModel 員工創建模型，包含員工基本資訊
     * @return 成功創建後返回 200 OK 及員工ID
     * @throws IOException 當文件處理出錯時拋出
     */
    @Operation(summary = "建立員工基本資料", description = "建立員工基本資料，同時創建系統帳號")
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority('EMPLOYEE_CREATE')")
    public ResponseEntity<RespEmployeeId> createEmployee(@Valid @ModelAttribute ReqEmployeeCreateModel reqModel) throws IOException {
        String employeeId = employeeService.createEmployeeBasicInfo(reqModel);
        return ResponseEntity.ok(RespEmployeeId.builder().employeeId(employeeId).build());
    }

    /**
     * 從指定 employeeId 獲取員工基本資訊
     *
     * @param employeeId 員工的 employee ID
     * @return 員工基本資訊
     */
    @Operation(summary = "查詢單一員工基本資訊", description = "查詢單一員工基本資訊")
    @GetMapping("/{employeeId}")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<RespEmployeeDetailModel> getEmployeeByProfileId(@PathVariable("employeeId") String employeeId) {
        var result = employeeService.getEmployeeByEmployeeId(employeeId);
        return ResponseEntity.ok(result);
    }

    /**
     * 更新指定 employeeId 的基本資訊
     *
     * @param employeeId 員工的 employee ID
     * @param reqModel   請求內容
     * @return 成功創建後返回 200 OK
     * @throws IOException 當文件處理出錯時拋出
     */
    @Operation(summary = "更新員工詳細資訊", description = "更新特定員工的基本資訊")
    @PutMapping(value = "/{employeeId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateEmployee(@PathVariable("employeeId") String employeeId, @Valid @ModelAttribute ReqEmployeeUpdateModel reqModel) throws IOException {
        employeeService.updateEmployeeBasicInfo(employeeId, reqModel);
        return ResponseEntity.ok().build();
    }

    /**
     * 使用各種過濾條件查詢員工
     *
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @param page                       頁碼
     * @param size                       每頁大小
     * @return 符合條件的員工列表
     */
    @Operation(summary = "查詢員工", description = "使用各種過濾條件搜索員工")
    @GetMapping
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<RespEmployeeQueryModel> queryEmployees(
            @RequestParam(required = false) String companyId,
            @RequestParam(required = false) String unitId,
            @RequestParam(required = false) String employeeNumber,
            @RequestParam(required = false) String zhName,
            @RequestParam(required = false) String enName,
            @RequestParam(required = false) Long onboardStartDate,
            @RequestParam(required = false) Long onboardEndDate,
            @RequestParam(required = false) Long resignStartDate,
            @RequestParam(required = false) Long resignEndDate,
            @RequestParam(required = false) Long accountActivationStartDate,
            @RequestParam(required = false) Long accountActivationEndDate,
            @RequestParam(required = false) Long accountSuspensionStartDate,
            @RequestParam(required = false) Long accountSuspensionEndDate,
            @RequestParam(required = false) ProbationStatus probationStatus,
            @RequestParam(required = false) Boolean isOnboarded,
            @RequestParam(required = false) int page,
            @RequestParam(required = false) int size) {

        RespEmployeeQueryModel response = employeeService.queryEmployee(
                companyId, unitId, employeeNumber, zhName, enName,
                onboardStartDate, onboardEndDate, resignStartDate, resignEndDate,
                accountActivationStartDate, accountActivationEndDate,
                accountSuspensionStartDate, accountSuspensionEndDate,
                probationStatus, isOnboarded,
                page, size);
        return ResponseEntity.ok(response);
    }

    // 銀行帳戶 API

    /**
     * 獲取與員工關聯的所有銀行帳戶列表
     *
     * @param employeeId 員工的 employee ID
     * @return 銀行帳戶詳細資訊列表
     */
    @Operation(summary = "獲取員工銀行帳戶", description = "獲取與員工關聯的所有銀行帳戶列表")
    @GetMapping("/{employeeId}/bank-accounts")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<List<RespBankAccountModel>> getBankAccounts(@PathVariable("employeeId") String employeeId) {
        List<RespBankAccountModel> accounts = bankAccountService.getBankAccountsByEmployeeId(employeeId);
        return ResponseEntity.ok(accounts);
    }

    /**
     * 查詢員工近1個月的修改記錄
     *
     * @param employeeId 員工ID
     * @return 修改記錄列表
     */
    @Operation(summary = "查詢員工修改記錄", description = "顯示近1個月該員工的修改記錄，按異動日期新至舊排序")
    @GetMapping("/{employeeId}/modification-history")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<RespEmployeeModificationHistory> getEmployeeModificationHistory(@PathVariable("employeeId") String employeeId) {
        var result = employeeService.getEmployeeModificationHistory(employeeId);
        return ResponseEntity.ok(result);
    }

    /**
     * 對銀行帳戶執行批次更新（新增、更新或停用）
     *
     * @param employeeId 員工的 employee ID
     * @param accounts   帶有操作標誌的銀行帳戶條目列表
     */
    @Operation(summary = "更新銀行帳戶", description = "對銀行帳戶執行批次更新（新增、更新或停用）")
    @PutMapping(value = "/{employeeId}/bank-accounts", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateBankAccounts(
            @PathVariable("employeeId") String employeeId,
            @Valid @ModelAttribute ReqBankAccountUpdateModel accounts) {
        bankAccountService.updateBankAccounts(employeeId, accounts);
        return ResponseEntity.ok().build();
    }

    // 緊急聯絡人 API

    /**
     * 獲取與員工關聯的所有緊急聯絡人列表
     *
     * @param employeeId 員工的 employee ID
     * @return 緊急聯絡人詳細資訊列表
     */
    @Operation(summary = "獲取員工緊急聯絡人", description = "獲取與員工關聯的所有緊急聯絡人列表")
    @GetMapping("/{employeeId}/emergency-contacts")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<List<RespEmergencyContactModel>> getEmergencyContacts(@PathVariable("employeeId") String employeeId) {
        List<RespEmergencyContactModel> contacts = emergencyContactService.getEmergencyContactsByProfileId(employeeId);
        return ResponseEntity.ok(contacts);
    }

    /**
     * 對緊急聯絡人執行批次更新（新增、更新或刪除）
     *
     * @param employeeId 員工的 employee ID
     * @param contacts   帶有操作標誌的緊急聯絡人條目列表
     */
    @Operation(summary = "更新緊急聯絡人", description = "對緊急聯絡人執行批次更新（新增、更新或刪除）")
    @PutMapping("/{employeeId}/emergency-contacts")
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateEmergencyContacts(
            @PathVariable("employeeId") String employeeId,
            @Valid @RequestBody List<ReqEmergencyContactUpdateModel> contacts) {
        emergencyContactService.updateEmergencyContacts(employeeId, contacts);
        return ResponseEntity.ok().build();
    }

    // 單位分配 API

    /**
     * 獲取員工的所有單位分配列表
     *
     * @param employeeId 員工的 employee ID
     * @return 單位分配詳細資訊列表
     */
    @Operation(summary = "獲取員工單位分配", description = "獲取員工的所有單位分配列表")
    @GetMapping("/{employeeId}/units")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<List<RespUnitModel>> getEmployeeUnitsByEmployeeId(@PathVariable("employeeId") String employeeId) {
        List<RespUnitModel> units = employeeUnitService.getEmployeeUnitsByEmployeeId(employeeId);
        return ResponseEntity.ok(units);
    }

    /**
     * 對單位分配執行批次更新（新增、更新或移除）
     *
     * @param employeeId 員工的 employee ID
     * @param units      帶有操作標誌的單位分配條目列表
     */
    @Operation(summary = "更新單位分配", description = "對單位分配執行批次更新（新增、更新或移除）")
    @PutMapping("/{employeeId}/units")
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateEmployeeUnits(
            @PathVariable("employeeId") String employeeId,
            @Valid @RequestBody List<ReqUnitUpdateModel> units) {
        employeeUnitService.updateEmployeeUnits(employeeId, units);
        return ResponseEntity.ok().build();
    }

    // 系統角色 API

    /**
     * 獲取分配給員工的所有員工角色列表
     *
     * @param employeeId 員工的 employee ID
     * @return 員工角色詳細資訊列表
     */
    @Operation(summary = "獲取員工角色", description = "獲取分配給員工的所有員工角色列表")
    @GetMapping("/{employeeId}/employee-roles")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<List<RespEmployeeRoleModel>> getEmployeeRoles(@PathVariable("employeeId") String employeeId) {
        List<RespEmployeeRoleModel> roles = employeeRoleService.getEmployeeRolesByProfileId(employeeId);
        return ResponseEntity.ok(roles);
    }

    /**
     * 對員工角色執行批次更新（新增、更新或停用）
     *
     * @param employeeId 員工的 employee ID
     * @param roles      帶有操作標誌的員工角色條目列表
     * @return 每個操作的結果摘要
     */
    @Operation(summary = "更新員工角色", description = "對員工角色執行批次更新（新增、更新或停用）")
    @PutMapping("/{employeeId}/employee-roles")
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateEmployeeRoles(
            @PathVariable("employeeId") String employeeId,
            @Valid @RequestBody List<ReqEmployeeRoleUpdateModel> roles) {
        employeeRoleService.updateEmployeeRoles(employeeId, roles);
        return ResponseEntity.ok().build();
    }

    // 員工文件 API

    /**
     * 獲取員工文件列表
     *
     * @param employeeId 員工ID
     * @return 文件列表
     */
    @Operation(summary = "獲取員工文件列表", description = "根據員工ID獲取文件列表，可選擇按文件類型過濾")
    @GetMapping("/{employeeId}/documents")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<List<RespEmployeeDocumentModel>> getEmployeeDocuments(@PathVariable("employeeId") String employeeId) {
        List<RespEmployeeDocumentModel> documents = employeeDocumentService.getEmployeeDocuments(employeeId);
        return ResponseEntity.ok(documents);
    }

    /**
     * 上傳員工文件
     *
     * @param employeeId     員工ID
     * @param documentTypeId 文件類型ID
     * @param document       文件
     * @param description    描述（可選）
     * @return 上傳的文件資訊
     */
    @Operation(summary = "上傳員工文件", description = "上傳員工文件，包括身分證、第二證件、護照、工作證等")
    @PostMapping(value = "/{employeeId}/documents", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> uploadEmployeeDocument(
            @PathVariable("employeeId") String employeeId,
            @RequestParam String documentTypeId,
            @RequestParam MultipartFile document,
            @RequestParam(required = false) String description) throws IOException {
        employeeDocumentService.uploadEmployeeDocument(employeeId, documentTypeId, document, description);
        return ResponseEntity.ok().build();
    }

    /**
     * 更新員工文件
     *
     * @param documentId 文件ID
     * @param request    更新請求
     * @return 無內容
     */
    @Operation(summary = "更新員工文件", description = "更新員工文件信息")
    @PutMapping("/documents/{documentId}")
    @PreAuthorize("hasAuthority('EMPLOYEE_UPDATE')")
    public ResponseEntity<Void> updateEmployeeDocument(
            @PathVariable String documentId,
            @Valid @RequestBody ReqEmployeeDocumentUpdate request) {
        employeeDocumentService.updateEmployeeDocument(documentId, request);
        return ResponseEntity.ok().build();
    }

    /**
     * 刪除員工文件
     *
     * @param documentId 文件ID
     * @return 無內容
     */
    @Operation(summary = "刪除員工文件", description = "根據文件ID刪除員工文件")
    @DeleteMapping("/documents/{documentId}")
    @PreAuthorize("hasAuthority('EMPLOYEE_DELETE')")
    public ResponseEntity<Void> deleteEmployeeDocument(@PathVariable String documentId) {
        employeeDocumentService.deleteEmployeeDocument(documentId);
        return ResponseEntity.ok().build();
    }

    /**
     * 查詢有公司電子郵件的員工
     * @return 符合條件的員工列表
     */
    @Operation(summary = "查詢有公司電子郵件的員工", description = "查詢有公司電子郵件的員工，過濾掉電子郵件為空或null的員工")
    @GetMapping("/with-company-email")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<RespEmployeeQueryModel> queryEmployeesWithCompanyEmail() {

        RespEmployeeQueryModel response = employeeService.queryEmployeesWithCompanyEmail();
        return ResponseEntity.ok(response);
    }

    /**
     * 導出員工資料為Excel，支援動態表頭
     *
     * @param headers                    動態表頭列表
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @return Excel文件
     */
    @Operation(summary = "導出員工資料為Excel", description = "將查詢到的員工資料導出為Excel檔案，支援動態表頭")
    @PostMapping(value = "/export", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<byte[]> exportEmployeesToExcel(
            @RequestBody(required = false) List<String> headers,
            @RequestParam(required = false) String companyId,
            @RequestParam(required = false) String unitId,
            @RequestParam(required = false) String employeeNumber,
            @RequestParam(required = false) String zhName,
            @RequestParam(required = false) String enName,
            @RequestParam(required = false) Long onboardStartDate,
            @RequestParam(required = false) Long onboardEndDate,
            @RequestParam(required = false) Long resignStartDate,
            @RequestParam(required = false) Long resignEndDate,
            @RequestParam(required = false) Long accountActivationStartDate,
            @RequestParam(required = false) Long accountActivationEndDate,
            @RequestParam(required = false) Long accountSuspensionStartDate,
            @RequestParam(required = false) Long accountSuspensionEndDate,
            @RequestParam(required = false) ProbationStatus probationStatus,
            @RequestParam(required = false) Boolean isOnboarded,
            HttpServletRequest request) {

        byte[] excelBytes = employeeService.exportEmployeesToExcel(
                headers, companyId, unitId, employeeNumber, zhName, enName,
                onboardStartDate, onboardEndDate, resignStartDate, resignEndDate,
                accountActivationStartDate, accountActivationEndDate,
                accountSuspensionStartDate, accountSuspensionEndDate,
                probationStatus, isOnboarded,
                request);

        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + URLEncoder.encode(ExcelGenerator.ExcelInfo.EMPLOYEE_LIST.getFileNameWithTimestamp(), StandardCharsets.UTF_8))
                .header(HttpHeaders.CONTENT_TYPE, ExcelGenerator.EXCEL_XLSX.toString())
                .body(excelBytes);
    }

    /**
     * 導出員工基本資料為Excel，支援動態表頭
     *
     * @param headers                    動態表頭列表
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @return Excel文件
     */
    @Operation(summary = "導出員工基本資料為Excel", description = "將查詢到的員工基本資料導出為Excel檔案，支援動態表頭")
    @PostMapping(value = "/export-basic-info", produces = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    @PreAuthorize("hasAuthority('EMPLOYEE_READ')")
    public ResponseEntity<byte[]> exportEmployeesBasicInfoToExcel(
            @RequestBody(required = false) List<String> headers,
            @RequestParam(required = false) String companyId,
            @RequestParam(required = false) String unitId,
            @RequestParam(required = false) String employeeNumber,
            @RequestParam(required = false) String zhName,
            @RequestParam(required = false) String enName,
            @RequestParam(required = false) Long onboardStartDate,
            @RequestParam(required = false) Long onboardEndDate,
            @RequestParam(required = false) Long resignStartDate,
            @RequestParam(required = false) Long resignEndDate,
            @RequestParam(required = false) Long accountActivationStartDate,
            @RequestParam(required = false) Long accountActivationEndDate,
            @RequestParam(required = false) Long accountSuspensionStartDate,
            @RequestParam(required = false) Long accountSuspensionEndDate,
            @RequestParam(required = false) ProbationStatus probationStatus,
            @RequestParam(required = false) Boolean isOnboarded,
            HttpServletRequest request) {

        byte[] excelBytes = employeeService.exportEmployeesBasicInfoToExcel(
                headers, companyId, unitId, employeeNumber, zhName, enName,
                onboardStartDate, onboardEndDate, resignStartDate, resignEndDate,
                accountActivationStartDate, accountActivationEndDate,
                accountSuspensionStartDate, accountSuspensionEndDate,
                probationStatus, isOnboarded,
                request);

        return ResponseEntity
                .ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + URLEncoder.encode(ExcelGenerator.ExcelInfo.EMPLOYEE_BASIC_INFO.getFileNameWithTimestamp(), StandardCharsets.UTF_8))
                .header(HttpHeaders.CONTENT_TYPE, ExcelGenerator.EXCEL_XLSX.toString())
                .body(excelBytes);
    }
}
