package com.jgallop.api.entity;

import com.jgallop.api.entity.enums.Gender;
import com.jgallop.api.entity.enums.EmploymentStatus;
import com.jgallop.api.entity.enums.ProbationStatus;
import com.jgallop.common.model.audit.EntityChineseName;
import com.jgallop.common.service.audit.AuditListener;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 員工基本資料實體，對應資料庫表 sm_employee。
 */
@Getter
@Setter
@Entity
@EntityListeners(AuditListener.class)
@EntityChineseName("員工")
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "sm_employee")
public class EmployeeEntity {

    /**
     * 員工ID。
     */
    @Id
    @Column(name = "employee_id", nullable = false)
    @GeneratedValue(strategy = GenerationType.UUID)
    private String employeeId;

    /**
     * 公司ID。
     */
    @Column(name = "company_id", nullable = false)
    private String companyId;

    /**
     * 員工編號。
     */
    @Column(name = "employee_number", nullable = false)
    private String employeeNumber;

    /**
     * 中文姓名。
     */
    @Column(name = "zh_name")
    private String zhName;

    /**
     * 英文姓名。
     */
    @Column(name = "en_name")
    private String enName;

    /**
     * 性別。
     */
    @Column(name = "gender")
    @Convert(converter = Gender.GenderConverter.class)
    private Gender gender;

    /**
     * 出生日期。
     */
    @Column(name = "birth_date")
    private Long birthDate;

    /**
     * 職等ID。
     */
    @Column(name = "job_grade_id")
    private String jobGradeId;

    /**
     * 職稱ID。
     */
    @Column(name = "job_title_id")
    private String jobTitleId;

    /**
     * 身分證字號。
     */
    @Column(name = "id_number")
    private String idNumber;

    /**
     * 工作許可證號碼。
     */
    @Column(name = "work_permit_number")
    private String workPermitNumber;

    /**
     * 工作許可證開始日期。
     */
    @Column(name = "work_permit_start_date")
    private Long workPermitStartDate;

    /**
     * 工作許可證結束日期。
     */
    @Column(name = "work_permit_end_date")
    private Long workPermitEndDate;

    /**
     * 電話號碼。
     */
    @Column(name = "phone_number")
    private String phoneNumber;

    /**
     * 手機號碼。
     */
    @Column(name = "mobile_number")
    private String mobileNumber;

    /**
     * 居住城市。
     */
    @Column(name = "residence_city")
    private String residenceCity;

    /**
     * 居住地址。
     */
    @Column(name = "residence_address")
    private String residenceAddress;

    /**
     * 公司Email。
     */
    @Column(name = "company_email")
    private String companyEmail;

    /**
     * 個人Email。
     */
    @Column(name = "personal_email")
    private String personalEmail;

    /**
     * 門禁卡號碼。
     */
    @Column(name = "access_card_number")
    private String accessCardNumber;

    /**
     * 個人資料照片ID。
     */
    @Column(name = "profile_photo_id")
    private String profilePhotoId;

    /**
     * 試用期天數。
     */
    @Column(name = "probation_period_days")
    private Long probationPeriodDays;

    /**
     * 試用期狀態。
     */
    @Column(name = "probation_status", nullable = false)
    @Convert(converter = ProbationStatus.ProbationStatusConverter.class)
    private ProbationStatus probationStatus;

    /**
     * 面試日期。
     */
    @Column(name = "interview_date")
    private Long interviewDate;

    /**
     * 面試結果。
     */
    @Column(name = "interview_result")
    private String interviewResult;

    /**
     * 到職日期。
     */
    @Column(name = "onboard_date")
    private Long onboardDate;

    /**
     * 是否已到職。
     */
    @Column(name = "is_onboarded", nullable = false) //到職、未到職
    private Boolean isOnboarded;

    /**
     * 離職日期。
     */
    @Column(name = "resignation_date")
    private Long resignationDate;

    /**
     * 僱用狀態。
     */
    @Column(name = "employment_status", nullable = false)
    @Convert(converter = EmploymentStatus.EmploymentStatusConverter.class)
    private EmploymentStatus employmentStatus;

    /**
     * 最後登入日期時間。
     */
    @Column(name = "last_login_datetime")
    private Long lastLoginDatetime;

    /**
     * 帳號啟用日期。
     */
    @Column(name = "account_activation_date")
    private Long accountActivationDate;

    /**
     * 帳號停用日期。
     */
    @Column(name = "account_suspension_date")
    private Long accountSuspensionDate;

    /**
     * 是否啟用。
     */
    @Column(name = "enabled")
    private Boolean enabled = Boolean.TRUE;

    /**
     * 建立者ID。
     */
    @Column(name = "creator")
    private String creator;

    /**
     * 建立日期時間。
     */
    @Column(name = "create_datetime")
    private Long createDatetime;

    /**
     * 最後修改者ID。
     */
    @Column(name = "last_modifier")
    private String lastModifier;

    /**
     * 最後修改日期時間。
     */
    @Column(name = "last_modify_datetime")
    private Long lastModifyDatetime;
}
