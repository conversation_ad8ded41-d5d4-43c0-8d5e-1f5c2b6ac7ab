package com.jgallop.api.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 員工修改記錄回應
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RespEmployeeModificationHistory {

    /**
     * 修改記錄列表
     */
    private List<ModificationRecord> modifications;

    /**
     * 修改記錄詳細資訊
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModificationRecord {

        /** 修改記錄ID */
        private String modificationId;

        /** 修改欄位名稱（中文或欄位名） */
        private String modificationField;

        /** 修改前值 */
        private String beforeModification;

        /** 修改後值 */
        private String afterModification;

        /** 修改者（user.zhName；若無則帳號或 userId） */
        private String creator;

        /** 修改時間 (timestamp, ms) */
        private Long createDatetime;
    }
}
