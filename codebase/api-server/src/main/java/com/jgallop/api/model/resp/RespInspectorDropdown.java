package com.jgallop.api.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 檢查員下拉選單回應模型
 */
@Data
public class RespInspectorDropdown {

    @Schema(description = "使用者ID (userId)")
    @JsonProperty(value = "inspectorId")
    private String inspectorId;

    @Schema(description = "人員編號")
    @JsonProperty(value = "inspectorNumber")
    private String inspectorNumber;

    @Schema(description = "人員中文名稱")
    @JsonProperty(value = "zhName")
    private String zhName;

    @Schema(description = "人員英文名稱")
    @JsonProperty(value = "enName")
    private String enName;

    @Schema(description = "人員類型")
    @JsonProperty(value = "inspectorType")
    private String inspectorType; // "EMPLOYEE" 或 "PARTNER"
}
