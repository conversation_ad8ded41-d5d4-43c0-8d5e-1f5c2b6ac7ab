package com.jgallop.api.repository;

import com.jgallop.api.entity.BankCodeEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 存取財會銀行代碼
 */
@Repository
public interface BankCodeRepository extends JpaRepository<BankCodeEntity, String> {

    /**
     * 根據國家代碼查詢啟用的銀行代碼
     *
     * @param countryCodeId 國家代碼ID
     * @return 啟用的銀行代碼列表
     */
    List<BankCodeEntity> findByCountryCodeIdAndEnabledTrue(String countryCodeId);

    /**
     * 根據國家代碼查詢啟用的銀行代碼，並依銀行代碼字串升冪排序
     */
    List<BankCodeEntity> findByCountryCodeIdAndEnabledTrueOrderByBankCodeAsc(String countryCodeId);
}
