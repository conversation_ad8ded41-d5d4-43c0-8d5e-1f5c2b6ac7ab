package com.jgallop.api.service;

import com.jgallop.api.entity.BankCodeEntity;
import com.jgallop.api.model.resp.RespBankCodeDropdown;
import com.jgallop.api.repository.BankCodeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 銀行代碼服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BankCodeService {

    private final BankCodeRepository bankCodeRepository;

    /**
     * 根據國家代碼取得所有啟用的銀行代碼（下拉選單）
     *
     * @param countryCodeId 國家代碼ID
     * @return 啟用的銀行代碼列表
     */
    public List<RespBankCodeDropdown> getEnabledBankCodesByCountryCodeForDropdown(String countryCodeId) {
        // 依 bank_code 字串升冪排序，確保以 0 開頭的代碼不會被丟到最後
        List<BankCodeEntity> bankCodes = bankCodeRepository
                .findByCountryCodeIdAndEnabledTrueOrderByBankCodeAsc(countryCodeId);
        return bankCodes.stream()
                .map(this::convertToRespBankCodeDropdown)
                .toList();
    }

    /**
     * 將銀行代碼實體轉換為下拉選單響應對象
     *
     * @param entity 銀行代碼實體
     * @return 下拉選單響應對象
     */
    private RespBankCodeDropdown convertToRespBankCodeDropdown(BankCodeEntity entity) {
        if (entity == null) {
            return null;
        }

        RespBankCodeDropdown dropdown = new RespBankCodeDropdown();
        dropdown.setBankCodeId(entity.getBankCodeId());
        dropdown.setBankCode(entity.getBankCode());
        dropdown.setBankName(entity.getBankName());
        return dropdown;
    }

    /**
     * 根據ID查詢銀行代碼
     *
     * @param bankCodeId 銀行代碼ID
     * @return 銀行代碼實體
     */
    public BankCodeEntity findBankCodeById(String bankCodeId) {
        return bankCodeRepository.findById(bankCodeId).orElse(null);
    }
}
