package com.jgallop.api.service;

import com.jgallop.api.entity.PartNumberSeriesSpecDetailEntity;
import com.jgallop.api.entity.PartNumberSeriesSpecDetailId;
import com.jgallop.api.entity.PartNumberSpecFieldEntity;
import com.jgallop.api.entity.PartNumberSpecFieldOptionEntity;
import com.jgallop.api.model.req.ReqBrandSpec;
import com.jgallop.api.entity.PartNumberProductSeriesEntity;
import com.jgallop.api.model.resp.RespProductSeriesSpecField;
import com.jgallop.api.repository.PartNumberSeriesSpecDetailRepository;
import com.jgallop.api.repository.PartNumberSpecFieldOptionRepository;
import com.jgallop.api.repository.PartNumberSpecFieldRepository;
import com.jgallop.user.service.UserHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 品牌與規格操作服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BrandSpecService {

    private final UserHelper userHelper;
    private final BrandSpecHelper brandSpecHelper;
    private final ProductSeriesHelper productSeriesHelper;

    private final PartNumberSpecFieldRepository partNumberSpecFieldRepository;
    private final PartNumberSpecFieldOptionRepository partNumberSpecFieldOptionRepository;
    private final PartNumberSeriesSpecDetailRepository partNumberSeriesspecDetailRepository;

    /**
     * 查詢產品系列規格項目
     *
     * @param productSeriesId 產品系列ID
     * @return 規格項目列表
     */
    public List<RespProductSeriesSpecField> getProductSeriesSpecFields(String productSeriesId) {
        log.info("查詢產品系列ID: {} 的規格項目", productSeriesId);

        // 檢查產品系列是否存在
        brandSpecHelper.findProductSeriesByIdOrElseThrow(productSeriesId);

        // 獲取所有規格欄位
        List<PartNumberSpecFieldEntity> allSpecFields = partNumberSpecFieldRepository.findAll();

        // 獲取與此產品系列關聯的規格欄位
        List<PartNumberSeriesSpecDetailEntity> seriesSpecDetails = partNumberSeriesspecDetailRepository.findById_ProductSeriesId(productSeriesId);

        // 創建一個包含已選用規格欄位ID的集合
        Set<String> selectedSpecFieldIds = seriesSpecDetails.stream()
                .map(detail -> detail.getId().getSpecFieldId())
                .collect(Collectors.toSet());

        // 獲取所有規格欄位ID
        List<String> allSpecFieldIds = allSpecFields.stream()
                .map(PartNumberSpecFieldEntity::getSpecFieldId)
                .toList();

        // 獲取所有規格欄位的選項值
        List<PartNumberSpecFieldOptionEntity> allOptions = partNumberSpecFieldOptionRepository.findBySpecFieldIdIn(allSpecFieldIds);

        // 將選項按規格欄位ID分組
        Map<String, List<String>> optionsBySpecFieldId = allOptions.stream()
                .collect(Collectors.groupingBy(
                        PartNumberSpecFieldOptionEntity::getSpecFieldId,
                        Collectors.mapping(PartNumberSpecFieldOptionEntity::getValue, Collectors.toList())
                ));

        // 將所有規格欄位映射到響應對象，為已選用的設置selected=true，並添加選項值
        return allSpecFields.stream()
                .map(specField -> {
                    String specFieldId = specField.getSpecFieldId();
                    return RespProductSeriesSpecField.builder()
                            .specFieldId(specFieldId)
                            .selected(selectedSpecFieldIds.contains(specFieldId))
                            .value(optionsBySpecFieldId.getOrDefault(specFieldId, List.of()))
                            .build();
                })
                .toList();
    }

    /**
     * 更新品牌與規格關係
     *
     * @param request 包含品牌與規格資料的請求
     */
    @Transactional
    public void updateBrandSpec(ReqBrandSpec request) {
        log.info("更新產品系列ID的品牌和規格關係: {}", request.getProductSeriesId());

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查產品系列是否存在且為啟用狀態
        PartNumberProductSeriesEntity productSeries = productSeriesHelper.findProductSeriesByIdOrElseThrow(request.getProductSeriesId());
        productSeriesHelper.checkProductSeriesEnabledOrElseThrow(productSeries, request.getProductSeriesId());

        // 先刪除該產品系列的所有現有規格關聯
        List<PartNumberSeriesSpecDetailEntity> existingDetails = 
            partNumberSeriesspecDetailRepository.findById_ProductSeriesId(request.getProductSeriesId());
        partNumberSeriesspecDetailRepository.deleteAll(existingDetails);

        // 處理每個規格欄位
        for (ReqBrandSpec.SpecColumn specField : request.getSpecFields()) {
            // 檢查規格欄位是否存在
            brandSpecHelper.findSpecColumnByIdOrElseThrow(specField.getSpecFieldId());

            // 創建複合鍵
            PartNumberSeriesSpecDetailId id = new PartNumberSeriesSpecDetailId(
                request.getProductSeriesId(), specField.getSpecFieldId());

            // 創建新實體
            PartNumberSeriesSpecDetailEntity entity = new PartNumberSeriesSpecDetailEntity();
            entity.setId(id);
            entity.setMustFillIn(specField.getMustFillIn());
            entity.setCreator(loginUserId);
            entity.setCreateDatetime(System.currentTimeMillis());

            // 保存到資料庫
            partNumberSeriesspecDetailRepository.saveAndFlush(entity);
            log.info("創建產品系列ID: {} 和規格欄位ID: {} 的品牌和規格關係", 
                request.getProductSeriesId(), specField.getSpecFieldId());
        }
    }
}
