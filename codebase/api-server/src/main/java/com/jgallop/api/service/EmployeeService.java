package com.jgallop.api.service;

import com.jgallop.api.entity.EmployeeEntity;
import com.jgallop.api.entity.EmployeeUnitEntity;
import com.jgallop.api.entity.enums.EmploymentStatus;
import com.jgallop.api.entity.enums.Gender;
import com.jgallop.api.entity.enums.ProbationStatus;
import com.jgallop.hfs.entity.postgres.FileEntity;
import com.jgallop.hfs.service.FileManagementHelper;
import com.jgallop.api.model.req.ReqEmployeeCreateModel;
import com.jgallop.api.model.req.ReqEmployeeUpdateModel;
import com.jgallop.api.model.resp.*;
import com.jgallop.api.model.resp.RespEmployeeDropdown;
import com.jgallop.api.repository.EmployeeRepository;
import com.jgallop.api.util.ExcelDataConverter;
import com.jgallop.common.model.audit.AuditLogEntity;
import com.jgallop.common.service.audit.AuditLogService;
import com.jgallop.common.service.MessageHelper;
import com.jgallop.user.entity.DataScope;
import com.jgallop.user.entity.UserEntity;
import com.jgallop.user.enums.PermissionName;
import com.jgallop.user.service.UserHelper;
import com.jgallop.user.util.FieldMaskingUtils;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * 員工基本資料服務
 */
@Service
@RequiredArgsConstructor
public class EmployeeService {

    private final UserHelper userHelper;
    private final FileHelper fileHelper;
    private final CompanyHelper companyHelper;
    private final EmployeeHelper employeeHelper;
    private final ExcelGenerator excelGenerator;
    private final EmployeeUnitHelper employeeUnitHelper;
    private final SystemAccountHelper systemAccountHelper;
    private final FileManagementHelper fileManagementHelper;

    private final EmployeeRepository employeeRepository;
    private final MessageHelper messageHelper;
    private final FieldMaskingUtils fieldMaskingUtils;
    private final AuditLogService auditLogService;

    /**
     * 使用各種過濾條件查詢員工
     *
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @param page                       頁碼
     * @param size                       每頁大小
     * @return 符合條件的員工列表
     */
    public RespEmployeeQueryModel queryEmployee(String companyId,
                                                String unitId,
                                                String employeeNumber,
                                                String zhName,
                                                String enName,
                                                Long onboardStartDate,
                                                Long onboardEndDate,
                                                Long resignStartDate,
                                                Long resignEndDate,
                                                Long accountActivationStartDate,
                                                Long accountActivationEndDate,
                                                Long accountSuspensionStartDate,
                                                Long accountSuspensionEndDate,
                                                ProbationStatus probationStatus,
                                                Boolean isOnboarded,
                                                int page,
                                                int size) {

        // 初始化 Specification，預設 null
        Specification<EmployeeEntity> spec = Specification.where(null);
        //欄位搜尋 -> 依照不同型別，使用不同搜尋方法
        spec = SpecificationHelper.addEqualSpecificationForString(spec, "companyId", companyId);


        if (StringUtils.isNotBlank(unitId)) {
            List<String> unitIds = new ArrayList<>();
            unitIds.add(unitId);
            employeeUnitHelper.getChildUnitIds(unitId, unitIds);
            spec = spec.and((root, query, cb) -> {
                assert query != null;
                Subquery<EmployeeUnitEntity> sq = query.subquery(EmployeeUnitEntity.class);
                Root<EmployeeUnitEntity> sub = sq.from(EmployeeUnitEntity.class);
                sq.select(sub);
                sq.where(
                        cb.equal(sub.get("id").get("employeeId"), root.get("employeeId")),
                        sub.get("id").get("unitId").in(unitIds),
                        cb.isTrue(sub.get("enabled"))
                );
                return cb.exists(sq);
            });
        }

        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "employeeNumber", employeeNumber);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "zhName", zhName);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "enName", enName);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "onboardDate", onboardStartDate, onboardEndDate);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "resignationDate", resignStartDate, resignEndDate);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "onboardDate", onboardStartDate, onboardEndDate);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "resignationDate", resignStartDate, resignEndDate);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "accountActivationDate", accountActivationStartDate, accountActivationEndDate);
        spec = SpecificationHelper.addBetweenSpecificationForLong(spec, "accountSuspensionDate", accountSuspensionStartDate, accountSuspensionEndDate);
        spec = SpecificationHelper.addEqualSpecificationForString(spec, "probationStatus", Objects.nonNull(probationStatus) ? probationStatus.toString() : null);
        spec = SpecificationHelper.addEqualSpecificationForBoolean(spec, "isOnboarded", isOnboarded);

        // 添加 dataScope 過濾
        spec = applyEmployeeDataScope(spec);

        Pageable pageable = PageHelper.getPageableOrderByParam(page, size, "lastModifyDatetime", Sort.Direction.DESC);
        Page<EmployeeEntity> resultPage = employeeRepository.findAll(spec, pageable);

        List<RespEmployeeDetailModel> respEmployeeDetailModels = resultPage.getContent().stream().map(this::convertToRespEmployeeDetailModel).toList();

        return new RespEmployeeQueryModel(resultPage.getTotalElements(), respEmployeeDetailModels);
    }

    /**
     * 查詢有公司電子郵件的員工
     * @return 符合條件的員工列表
     */
    public RespEmployeeQueryModel queryEmployeesWithCompanyEmail() {

        // 初始化 Specification，預設 null
        Specification<EmployeeEntity> spec = Specification.where(null);

        // 過濾公司電子郵件不為 null 且不為空字串
        spec = spec.and((root, query, cb) -> cb.and(
                cb.isNotNull(root.get("companyEmail")),
                cb.notEqual(root.get("companyEmail"), "")
        ));

        // 添加 dataScope 過濾
        spec = applyEmployeeDataScope(spec);

        // 使用默認排序，不分頁（使用最大值獲取所有記錄）
        Pageable pageable = PageHelper.getPageable(0, Integer.MAX_VALUE);
        Page<EmployeeEntity> resultPage = employeeRepository.findAll(spec, pageable);

        List<RespEmployeeDetailModel> respEmployeeDetailModels = resultPage.getContent().stream()
                .map(this::convertToRespEmployeeDetailModel)
                .toList();

        return new RespEmployeeQueryModel(resultPage.getTotalElements(), respEmployeeDetailModels);
    }

    public RespEmployeeDetailModel convertToRespEmployeeDetailModel(EmployeeEntity entity) {
        RespEmployeeDetailModel model = new RespEmployeeDetailModel();

        model.setEmployeeId(entity.getEmployeeId());
        model.setCompanyId(entity.getCompanyId());
        model.setCompanyName(companyHelper.getCompanyZhName(entity.getCompanyId()));
        model.setUnitName(employeeUnitHelper.getUnitNameListByEmployeeId(entity.getEmployeeId()));
        model.setEmployeeNumber(entity.getEmployeeNumber());
        model.setZhName(entity.getZhName());
        model.setEnName(entity.getEnName());
        model.setGender(entity.getGender());
        model.setBirthDate(entity.getBirthDate());
        model.setPhone(entity.getPhoneNumber());
        model.setMobile(entity.getMobileNumber());
        model.setCity(entity.getResidenceCity());
        model.setAddress(entity.getResidenceAddress());
        model.setCompanyEmail(entity.getCompanyEmail());
        model.setPersonalEmail(entity.getPersonalEmail());
        model.setProfilePhotoId(entity.getProfilePhotoId());
        model.setProfilePhotoPath(fileHelper.getFilePath(entity.getProfilePhotoId()));
        model.setProfilePhotoName(fileHelper.getFileName(entity.getProfilePhotoId()));
        model.setIdNumber(entity.getIdNumber());
        model.setWorkPermitNumber(entity.getWorkPermitNumber());
        model.setAccessCardNumber(entity.getAccessCardNumber());
        model.setEmploymentStatus(entity.getEmploymentStatus());
        model.setProbationStatus(entity.getProbationStatus());
        model.setOnboardDate(entity.getOnboardDate());
        model.setIsOnboarded(entity.getIsOnboarded());
        model.setProbationPeriod(entity.getProbationPeriodDays());
        model.setWorkPermitStartDate(entity.getWorkPermitStartDate());
        model.setWorkPermitEndDate(entity.getWorkPermitEndDate());
        model.setAccountActivationDate(entity.getAccountActivationDate());
        model.setResignationDate(entity.getResignationDate());
        model.setCreator(systemAccountHelper.findUserNameByUserId(entity.getCreator()));
        model.setCreateDatetime(entity.getCreateDatetime());
        model.setLastModifier(systemAccountHelper.findUserNameByUserId(entity.getLastModifier()));
        model.setLastModifyDatetime(entity.getLastModifyDatetime());

        return model;
    }

    /**
     * 查詢員工近1個月的修改記錄
     *
     * @param employeeId 員工ID
     * @return 修改記錄回應
     */
    public com.jgallop.api.model.resp.RespEmployeeModificationHistory getEmployeeModificationHistory(String employeeId) {
        // 計算近1個月的時間範圍
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMonths(1);

        // 查詢 audit 記錄（只查 EmployeeEntity）
        List<AuditLogEntity> auditLogs = auditLogService.getEntityModificationHistory(
                "EmployeeEntity", employeeId, startTime, endTime);

        // 過濾掉系統欄位
        Set<String> systemFields = Set.of(
                "creator",
                "createDatetime",
                "lastModifier",
                "lastModifyDatetime",
                "ENTITY_CREATED",
                "ENTITY_DELETED"
        );

        List<AuditLogEntity> filtered = auditLogs.stream()
                .filter(log -> !systemFields.contains(log.getColumnName()))
                .toList();

        // 轉換為回應 DTO
        List<com.jgallop.api.model.resp.RespEmployeeModificationHistory.ModificationRecord> mods = filtered.stream()
                .map(a -> {
                    // Prefer user's zhName; fallback to account, then userId
                    String creator = systemAccountHelper.findUserNameByUserId(a.getUserId());
                    if (StringUtils.isBlank(creator)) {
                        creator = userHelper.findUserAccountByUserIdOrElseNull(a.getUserId());
                        if (creator == null) creator = a.getUserId();
                    }
                    return com.jgallop.api.model.resp.RespEmployeeModificationHistory.ModificationRecord.builder()
                            .modificationId(a.getId().toString())
                            .modificationField(a.getFieldName())
                            .beforeModification(a.getBeforeValue())
                            .afterModification(a.getAfterValue())
                            .creator(creator)
                            .createDatetime(a.getChangedAt().toEpochSecond(ZoneOffset.UTC) * 1000)
                            .build();
                })
                .toList();

        return com.jgallop.api.model.resp.RespEmployeeModificationHistory.builder()
                .modifications(mods)
                .build();
    }

    /**
     * 創建具有基本資訊的新員工
     *
     * @param basicInfo 員工基本資訊
     * @return 新創建的員工ID
     */
    @Transactional
    public String createEmployeeBasicInfo(ReqEmployeeCreateModel basicInfo) throws IOException {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 創建並保存員工實體
        EmployeeEntity entity = new EmployeeEntity();

        entity.setCompanyId(basicInfo.getCompanyId());
        entity.setEmployeeNumber(basicInfo.getEmployeeNumber());
        entity.setZhName(basicInfo.getZhName());
        entity.setEnName(basicInfo.getEnName());
        entity.setGender(basicInfo.getGender());
        entity.setBirthDate(basicInfo.getBirthDate());
        entity.setJobGradeId(basicInfo.getJobGradeId());
        entity.setJobTitleId(basicInfo.getJobTitleId());
        entity.setIdNumber(basicInfo.getIdNumber());
        entity.setWorkPermitNumber(basicInfo.getWorkPermitNumber());
        entity.setWorkPermitStartDate(basicInfo.getWorkPermitStartDate());
        entity.setWorkPermitEndDate(basicInfo.getWorkPermitEndDate());
        entity.setPhoneNumber(basicInfo.getPhone());
        entity.setMobileNumber(basicInfo.getMobile());
        entity.setResidenceCity(basicInfo.getCity());
        entity.setResidenceAddress(basicInfo.getAddress());
        entity.setCompanyEmail(basicInfo.getCompanyEmail());
        entity.setPersonalEmail(basicInfo.getPersonalEmail());
        entity.setAccessCardNumber(basicInfo.getAccessCardNumber());

        // Handle profile photo if provided
        if (basicInfo.getProfilePhoto() != null && !basicInfo.getProfilePhoto().isEmpty()) {
            FileEntity fileEntity = fileManagementHelper.createNewFile(basicInfo.getProfilePhoto(), loginUserId);
            entity.setProfilePhotoId(fileEntity.getFileId());
        }

        entity.setIsOnboarded(basicInfo.getIsOnboarded());
        entity.setOnboardDate(basicInfo.getOnboardDate());
        entity.setProbationPeriodDays(basicInfo.getProbationPeriod());
        entity.setProbationStatus(basicInfo.getProbationStatus());
        entity.setEmploymentStatus(basicInfo.getEmploymentStatus());

        entity.setAccountActivationDate(System.currentTimeMillis());
        entity.setCreator(loginUserId);
        entity.setCreateDatetime(System.currentTimeMillis());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        EmployeeEntity savedEntity = employeeRepository.saveAndFlush(entity);
        userHelper.createUserByEmployee(savedEntity.getEmployeeId(), savedEntity.getEmployeeNumber(), basicInfo.getCompanyEmail(), loginUserId);

        return savedEntity.getEmployeeId();
    }

    /**
     * 更新特定員工的基本資訊
     *
     * @param employeeId     員工的檔案ID
     * @param basicInfo      更新的基本資訊
     */
    @Transactional
    public void updateEmployeeBasicInfo(String employeeId, ReqEmployeeUpdateModel basicInfo) throws IOException {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查找員工實體
        EmployeeEntity entity = employeeHelper.findEmployeeByEmployeeIdAndEnabledOrThrow(employeeId);

        // 更新基本資訊
        entity.setCompanyId(basicInfo.getCompanyId());
        entity.setZhName(basicInfo.getZhName());
        entity.setEnName(basicInfo.getEnName());
        entity.setGender(basicInfo.getGender());
        entity.setBirthDate(basicInfo.getBirthDate());
        entity.setJobGradeId(basicInfo.getJobGradeId());
        entity.setJobTitleId(basicInfo.getJobTitleId());
        entity.setIdNumber(basicInfo.getIdNumber());
        entity.setWorkPermitNumber(basicInfo.getWorkPermitNumber());
        entity.setWorkPermitStartDate(basicInfo.getWorkPermitStartDate());
        entity.setWorkPermitEndDate(basicInfo.getWorkPermitEndDate());
        entity.setPhoneNumber(basicInfo.getPhone());
        entity.setMobileNumber(basicInfo.getMobile());
        entity.setResidenceCity(basicInfo.getCity());
        entity.setResidenceAddress(basicInfo.getAddress());
        entity.setCompanyEmail(basicInfo.getCompanyEmail());
        entity.setPersonalEmail(basicInfo.getPersonalEmail());
        entity.setAccessCardNumber(basicInfo.getAccessCardNumber());


        // Handle profile photo if provided
        if (basicInfo.getProfilePhoto() != null && !basicInfo.getProfilePhoto().isEmpty()) {
            // Delete old photos if exists
            if (entity.getProfilePhotoId() != null) {
                fileManagementHelper.deleteFileByFileId(entity.getProfilePhotoId());
            }

            // Upload new photos
            FileEntity fileEntity = fileManagementHelper.createNewFile(basicInfo.getProfilePhoto(), loginUserId);
            entity.setProfilePhotoId(fileEntity.getFileId());
        }
        entity.setIsOnboarded(basicInfo.getIsOnboarded());
        entity.setOnboardDate(basicInfo.getOnboardDate());
        entity.setProbationPeriodDays(basicInfo.getProbationPeriod());
        entity.setProbationStatus(basicInfo.getProbationStatus());
        entity.setEmploymentStatus(basicInfo.getEmploymentStatus());

        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        // 保存更新後的實體
        employeeRepository.saveAndFlush(entity);
        userHelper.updateUserEmailByEmployeeId(employeeId, basicInfo.getCompanyEmail(), loginUserId);
    }

    /**
     * 查詢單一員工基本資料
     *
     * @param employeeId 員工ID
     * @return 員工基本資料
     */
    public RespEmployeeDetailModel getEmployeeByEmployeeId(String employeeId) {
        EmployeeEntity employeeEntity = employeeHelper.findEmployeeByEmployeeIdAndEnabledOrThrow(employeeId);

        // 檢查 dataScope 權限
        validateEmployeeDataScopeAccess(employeeEntity);

        return this.convertToRespEmployeeDetailModel(employeeEntity);
    }


    /**
     * 導出員工資料為Excel，支援動態表頭
     *
     * @param headers                    動態表頭列表
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @return Excel文件的字節數組
     */
    public byte[] exportEmployeesToExcel(List<String> headers,
                                         String companyId,
                                         String unitId,
                                         String employeeNumber,
                                         String zhName,
                                         String enName,
                                         Long onboardStartDate,
                                         Long onboardEndDate,
                                         Long resignStartDate,
                                         Long resignEndDate,
                                         Long accountActivationStartDate,
                                         Long accountActivationEndDate,
                                         Long accountSuspensionStartDate,
                                         Long accountSuspensionEndDate,
                                         ProbationStatus probationStatus,
                                         Boolean isOnboarded,
                                         HttpServletRequest request) {

        // 使用現有的查詢方法獲取所有數據（不分頁）
        RespEmployeeQueryModel wrapper = queryEmployee(companyId, unitId, employeeNumber, zhName, enName,
                                                       onboardStartDate, onboardEndDate, resignStartDate, resignEndDate,
                                                       accountActivationStartDate, accountActivationEndDate,
                                                       accountSuspensionStartDate, accountSuspensionEndDate,
                                                       probationStatus, isOnboarded,
                                                       0, Integer.MAX_VALUE);

        List<RespEmployeeDetailModel> employees = wrapper.getRespEmployeeDetailModels();

        // 如果前端沒有提供表頭，使用預設表頭
        if (headers == null || headers.isEmpty()) {
            headers = ExcelGenerator.ExcelInfo.EMPLOYEE_LIST.getColumnNames();
        }

        // 獲取時區
        ZoneId zoneId = TimeHelper.resolveZoneId(request);

        // 創建一個最終的表頭列表（使其成為effectively final）
        final List<String> finalHeaders = new ArrayList<>(headers);

        // 將數據轉換為Map列表，根據動態表頭
        List<Map<String, Object>> data = employees.stream()
                .map(employee -> {
                    Map<String, Object> row = new HashMap<>();

                    // 遍歷表頭，根據表頭名稱設置對應的值
                    for (String header : finalHeaders) {
                        switch (header) {
                            case "公司別" -> row.put(header, ExcelDataConverter.safeString(employee.getCompanyName()));
                            case "單位" -> row.put(header, employee.getUnitName() != null ? String.join(", ", employee.getUnitName()) : "");
                            case "員工編號" -> row.put(header, ExcelDataConverter.safeString(employee.getEmployeeNumber()));
                            case "中文姓名" -> row.put(header, ExcelDataConverter.safeString(employee.getZhName()));
                            case "英文姓名" -> row.put(header, ExcelDataConverter.safeString(employee.getEnName()));
                            case "性別" -> row.put(header, convertGenderToString(employee.getGender()));
                            case "生日" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getBirthDate(), zoneId));
                            case "年齡" -> row.put(header, calculateAge(employee.getBirthDate()));
                            case "電話" -> row.put(header, ExcelDataConverter.safeString(employee.getPhone()));
                            case "手機" -> row.put(header, ExcelDataConverter.safeString(employee.getMobile()));
                            case "縣市" -> row.put(header, ExcelDataConverter.safeString(employee.getCity()));
                            case "居住地址" -> row.put(header, ExcelDataConverter.safeString(employee.getAddress()));
                            case "公司信箱" -> row.put(header, ExcelDataConverter.safeString(employee.getCompanyEmail()));
                            case "個人信箱" -> row.put(header, ExcelDataConverter.safeString(employee.getPersonalEmail()));
                            case "身份證字號" -> row.put(header, ExcelDataConverter.safeString(employee.getIdNumber()));
                            case "工作證號碼" -> row.put(header, ExcelDataConverter.safeString(employee.getWorkPermitNumber()));
                            case "門禁卡號碼" -> row.put(header, ExcelDataConverter.safeString(employee.getAccessCardNumber()));
                            case "在職狀態" -> row.put(header, convertEmploymentStatusToString(employee.getEmploymentStatus()));
                            case "試用期狀態" -> row.put(header, convertProbationStatusToString(employee.getProbationStatus()));
                            case "到職日期" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getOnboardDate(), zoneId));
                            case "在職" -> row.put(header, ExcelDataConverter.convertBooleanToYesNo(employee.getIsOnboarded()));
                            case "試用期天數" -> row.put(header, employee.getProbationPeriod() != null ? employee.getProbationPeriod().toString() : "");
                            case "工作證開始日期" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getWorkPermitStartDate(), zoneId));
                            case "工作證結束日期" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getWorkPermitEndDate(), zoneId));
                            case "帳號啟用日" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getAccountActivationDate(), zoneId));
                            case "離職日期" -> row.put(header, ExcelDataConverter.formatTimestampToDate(employee.getResignationDate(), zoneId));
                            case "建檔人員" -> row.put(header, ExcelDataConverter.safeString(employee.getCreator()));
                            case "建檔日期時間" -> row.put(header, ExcelDataConverter.formatTimestampToDatetime(employee.getCreateDatetime(), zoneId));
                            case "最後修改人員" -> row.put(header, ExcelDataConverter.safeString(employee.getLastModifier()));
                            case "最後修改時間" -> row.put(header, ExcelDataConverter.formatTimestampToDatetime(employee.getLastModifyDatetime(), zoneId));
                            default -> row.put(header, ""); // 對於未知的表頭，設置空值
                        }
                    }
                    return row;
                })
                .toList();

        // 使用ExcelGenerator生成Excel，傳入動態表頭
        return excelGenerator.generateDynamicHeaderExcel(
                ExcelGenerator.SheetTitle.EMPLOYEE_LIST.getTitle(),
                headers,
                data,
                ExcelGenerator.ExcelInfo.EMPLOYEE_LIST
        );
    }

    /**
     * 轉換性別枚舉為中文字串
     */
    private String convertGenderToString(Gender gender) {
        if (gender == null) {
            return "";
        }
        return switch (gender) {
            case M -> "男";
            case F -> "女";
        };
    }

    /**
     * 轉換在職狀態枚舉為中文字串
     */
    private String convertEmploymentStatusToString(EmploymentStatus employmentStatus) {
        if (employmentStatus == null) {
            return "";
        }
        return switch (employmentStatus) {
            case EMPLOYED -> "在職";
            case RESIGNED -> "離職";
            case NOT_ONBOARDED -> "未到職";
        };
    }

    /**
     * 轉換試用期狀態枚舉為中文字串
     */
    private String convertProbationStatusToString(ProbationStatus probationStatus) {
        if (probationStatus == null) {
            return "";
        }
        return switch (probationStatus) {
            case NOT_STARTED -> "未開始";
            case IN_PROBATION -> "試用期中";
            case PROBATION_EXPIRED -> "試用到期";
            case FAILED -> "未通過";
            case PASSED -> "已通過";
        };
    }

    /**
     * 取得所有在職且啟用的員工（下拉選單）
     *
     * @return 在職且啟用的員工列表
     */
    public List<RespEmployeeDropdown> getAllActiveEmployeesForDropdown() {
        // 直接使用 Repository 的 abstract method 查詢在職且啟用的員工
        List<EmployeeEntity> activeEmployees = employeeRepository.findByEmploymentStatusAndEnabledTrue(EmploymentStatus.EMPLOYED);

        // 轉換實體為下拉選單回應模型
        return activeEmployees.stream()
                .map(this::convertToRespEmployeeDropdown)
                .toList();
    }

    /**
     * 將員工實體轉換為下拉選單回應模型
     */
    private RespEmployeeDropdown convertToRespEmployeeDropdown(EmployeeEntity entity) {
        RespEmployeeDropdown model = new RespEmployeeDropdown();
        model.setEmployeeId(entity.getEmployeeId());
        model.setZhName(entity.getZhName());
        model.setEnName(entity.getEnName());
        return model;
    }

    /**
     * 根據出生日期計算年齡
     * 
     * @param birthDateMillis 出生日期（毫秒）
     * @return 年齡，如果出生日期為空則返回空字串
     */
    private String calculateAge(Long birthDateMillis) {
        if (birthDateMillis == null) {
            return "";
        }

        // 將毫秒轉換為LocalDate
        LocalDate birthDate = Instant.ofEpochMilli(birthDateMillis)
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        // 計算到當前日期的年數差
        int age = LocalDate.now().getYear() - birthDate.getYear();

        // 檢查是否已經過了今年的生日
        if (LocalDate.now().getMonthValue() < birthDate.getMonthValue() || 
            (LocalDate.now().getMonthValue() == birthDate.getMonthValue() && 
             LocalDate.now().getDayOfMonth() < birthDate.getDayOfMonth())) {
            age--;
        }

        return String.valueOf(age);
    }

    /**
     * 導出員工基本資料為Excel
     *
     * @param headers                    動態表頭列表
     * @param companyId                  公司 ID
     * @param unitId                     單位 ID
     * @param employeeNumber             員工編號
     * @param zhName                     中文姓名
     * @param enName                     英文姓名
     * @param onboardStartDate           到職日期起
     * @param onboardEndDate             到職日期訖
     * @param resignStartDate            離職日期起
     * @param resignEndDate              離職日期訖
     * @param accountActivationStartDate 帳戶開始日起
     * @param accountActivationEndDate   帳戶結束日訖
     * @param accountSuspensionStartDate 帳戶開始日起
     * @param accountSuspensionEndDate   帳戶結束日訖
     * @param probationStatus            試用期狀態
     * @param isOnboarded                在職狀態
     * @param request                    HTTP請求
     * @return Excel文件的字節數組
     */
    public byte[] exportEmployeesBasicInfoToExcel(List<String> headers,
                                         String companyId,
                                         String unitId,
                                         String employeeNumber,
                                         String zhName,
                                         String enName,
                                         Long onboardStartDate,
                                         Long onboardEndDate,
                                         Long resignStartDate,
                                         Long resignEndDate,
                                         Long accountActivationStartDate,
                                         Long accountActivationEndDate,
                                         Long accountSuspensionStartDate,
                                         Long accountSuspensionEndDate,
                                         ProbationStatus probationStatus,
                                         Boolean isOnboarded,
                                         HttpServletRequest request) {

        // 使用現有的查詢方法獲取所有數據（不分頁）
        RespEmployeeQueryModel wrapper = queryEmployee(companyId, unitId, employeeNumber, zhName, enName,
                                                       onboardStartDate, onboardEndDate, resignStartDate, resignEndDate,
                                                       accountActivationStartDate, accountActivationEndDate,
                                                       accountSuspensionStartDate, accountSuspensionEndDate,
                                                       probationStatus, isOnboarded,
                                                       0, Integer.MAX_VALUE);

        List<RespEmployeeDetailModel> employees = wrapper.getRespEmployeeDetailModels();

        // 如果前端沒有提供表頭，使用預設表頭
        if (headers == null || headers.isEmpty()) {
            headers = ExcelGenerator.ExcelInfo.EMPLOYEE_BASIC_INFO.getColumnNames();
        }

        // 獲取時區
        ZoneId zoneId = TimeHelper.resolveZoneId(request);

        // 創建一個最終的表頭列表（使其成為effectively final）
        final List<String> finalHeaders = new ArrayList<>(headers);

        // 將數據轉換為Map列表，根據動態表頭
        List<Map<String, Object>> data = employees.stream()
                .map(employee -> {
                    Map<String, Object> row = new HashMap<>();

                    // 遍歷表頭，根據表頭名稱設置對應的值
                    for (String header : finalHeaders) {
                        switch (header) {
                            case "員工ID" -> row.put(header, employee.getEmployeeId());
                            case "公司ID" -> row.put(header, employee.getCompanyId());
                            case "公司別" -> row.put(header, companyHelper.getCompanyZhName(employee.getCompanyId()));
                            case "單位" -> row.put(header, employeeUnitHelper.getUnitNameListByEmployeeId(employee.getEmployeeId()) != null ? String.join(", ", employeeUnitHelper.getUnitNameListByEmployeeId(employee.getEmployeeId())) : "");
                            case "員工編號" -> row.put(header, employee.getEmployeeNumber());
                            case "中文姓名" -> row.put(header, employee.getZhName());
                            case "英文姓名" -> row.put(header, employee.getEnName());
                            case "性別" -> {
                                String genderStr = "";
                                if (employee.getGender() != null) {
                                    genderStr = employee.getGender().getDescription();
                                }
                                row.put(header, genderStr);
                            }
                            case "生日" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getBirthDate(), zoneId));
                            case "年齡" -> row.put(header, calculateAge(employee.getBirthDate()));
                            case "電話" -> row.put(header, employee.getPhone());
                            case "手機" -> row.put(header, employee.getMobile());
                            case "縣市" -> row.put(header, employee.getCity());
                            case "居住地址" -> row.put(header, employee.getAddress());
                            case "公司信箱" -> row.put(header, employee.getCompanyEmail());
                            case "個人信箱" -> row.put(header, employee.getPersonalEmail());
                            case "身份證字號" -> row.put(header, employee.getIdNumber());
                            case "工作證號碼" -> row.put(header, employee.getWorkPermitNumber());
                            case "工作證狀態" -> {
                                String workPermitStatus = "";
                                if (employee.getWorkPermitEndDate() != null) {
                                    workPermitStatus = employee.getWorkPermitEndDate() >= System.currentTimeMillis() ? "未過期" : "已過期";
                                }
                                row.put(header, workPermitStatus);
                            }
                            case "門禁卡號碼" -> row.put(header, employee.getAccessCardNumber());
                            case "在職狀態" -> row.put(header, employee.getEmploymentStatus() != null ? employee.getEmploymentStatus().getDescription() : "");
                            case "試用期狀態" -> row.put(header, employee.getProbationStatus() != null ? employee.getProbationStatus().getDescription() : "");
                            case "到職日期" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getOnboardDate(), zoneId));
                            case "在職" -> row.put(header, employee.getIsOnboarded() != null ? (employee.getIsOnboarded() ? "是" : "否") : "");
                            case "試用期天數" -> row.put(header, employee.getProbationPeriod());
                            case "工作證開始日期" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getWorkPermitStartDate(), zoneId));
                            case "工作證結束日期" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getWorkPermitEndDate(), zoneId));
                            case "帳號啟用日" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getAccountActivationDate(), zoneId));
                            case "離職日期" -> row.put(header, TimeHelper.formatTimestampToDate(employee.getResignationDate(), zoneId));
                            case "建檔人員" -> row.put(header, systemAccountHelper.findUserNameByUserId(employee.getCreator()));
                            case "建檔日期時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(employee.getCreateDatetime(), zoneId));
                            case "最後修改人員" -> row.put(header, systemAccountHelper.findUserNameByUserId(employee.getLastModifier()));
                            case "最後修改時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(employee.getLastModifyDatetime(), zoneId));
                            default -> row.put(header, ""); // 對於未知的表頭，設置空值
                        }
                    }
                    return row;
                })
                .toList();

        // 使用ExcelGenerator生成Excel，傳入動態表頭
        return excelGenerator.generateDynamicHeaderExcel(
                ExcelGenerator.SheetTitle.EMPLOYEE_BASIC_INFO.getTitle(),
                headers,
                data,
                ExcelGenerator.ExcelInfo.EMPLOYEE_BASIC_INFO
        );
    }

    /**
     * 根據用戶的 dataScope 權限過濾員工資料
     *
     * @param spec 原始查詢規範
     * @return 添加了 dataScope 過濾的查詢規範
     */
    private Specification<EmployeeEntity> applyEmployeeDataScope(Specification<EmployeeEntity> spec) {
        // 獲取當前用戶
        final UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        // 獲取用戶的員工資訊
        final String employeeId = userEntity.getEmployeeId();

        // 如果 employeeId 為 null，返回系統錯誤
        if (employeeId == null) {
            throw new IllegalStateException(messageHelper.localizedMessage("error.user.no.employee.association"));
        }

        // 獲取員工實體
        EmployeeEntity employeeEntity = employeeHelper.findEmployeeByEmployeeIdOrThrow(employeeId);

        // 獲取使用者的 EMPLOYEE 範圍
        DataScope scope = fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE);
        if (scope == null) {
            throw new IllegalArgumentException(messageHelper.localizedMessage("error.data.scope.null"));
        }

        // 根據不同範圍套用不同的 Specification
        switch (scope) {
            case SELF:
                // 只能看自己的員工資料
                return SpecificationHelper.addEqualSpecificationForString(spec, "employeeId", employeeId);

            case DEPT:
                // 查詢自己部門及以下子部門的所有員工
                List<String> unitIds = new ArrayList<>();

                // 獲取用戶所屬的所有部門
                List<EmployeeUnitEntity> userUnits = employeeUnitHelper.getEmployeeUnitByEmployeeId(employeeId);

                // 對每個部門，獲取其及其所有子部門
                for (EmployeeUnitEntity userUnit : userUnits) {
                    String userUnitId = userUnit.getId().getUnitId();
                    unitIds.add(userUnitId);

                    // 遞迴獲取所有子部門ID
                    employeeUnitHelper.getChildUnitIds(userUnitId, unitIds);
                }

                // 添加部門過濾條件
                if (!unitIds.isEmpty()) {
                    spec = spec.and((root, query, cb) -> {
                        assert query != null;
                        Subquery<EmployeeUnitEntity> sq = query.subquery(EmployeeUnitEntity.class);
                        Root<EmployeeUnitEntity> sub = sq.from(EmployeeUnitEntity.class);
                        sq.select(sub);
                        sq.where(
                                cb.equal(sub.get("id").get("employeeId"), root.get("employeeId")),
                                sub.get("id").get("unitId").in(unitIds),
                                cb.isTrue(sub.get("enabled"))
                        );
                        return cb.exists(sq);
                    });
                }

                return spec;

            default:
                // ALL 或其他情況，不添加額外過濾
                return spec;
        }
    }

    /**
     * 驗證當前用戶是否有權限存取指定的員工資料
     *
     * @param targetEmployee 目標員工實體
     * @throws IllegalArgumentException 如果沒有權限存取
     */
    private void validateEmployeeDataScopeAccess(EmployeeEntity targetEmployee) {
        // 獲取當前用戶
        final UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        // 獲取用戶的員工資訊
        final String currentEmployeeId = userEntity.getEmployeeId();

        // 如果 employeeId 為 null，返回系統錯誤
        if (currentEmployeeId == null) {
            throw new IllegalStateException(messageHelper.localizedMessage("error.user.no.employee.association"));
        }

        // 獲取使用者的 EMPLOYEE 範圍
        DataScope scope = fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE);
        if (scope == null) {
            throw new IllegalArgumentException(messageHelper.localizedMessage("error.data.scope.null"));
        }

        // 根據不同範圍進行權限檢查
        switch (scope) {
            case SELF:
                // 只能看自己的員工資料
                if (!currentEmployeeId.equals(targetEmployee.getEmployeeId())) {
                    throw new IllegalArgumentException(messageHelper.localizedMessage("error.employee.access.denied"));
                }
                break;

            case DEPT:
                // 檢查目標員工是否在當前用戶的部門或子部門中
                List<String> unitIds = new ArrayList<>();

                // 獲取用戶所屬的所有部門
                List<EmployeeUnitEntity> userUnits = employeeUnitHelper.getEmployeeUnitByEmployeeId(currentEmployeeId);

                // 對每個部門，獲取其及其所有子部門
                for (EmployeeUnitEntity userUnit : userUnits) {
                    String userUnitId = userUnit.getId().getUnitId();
                    unitIds.add(userUnitId);

                    // 遞迴獲取所有子部門ID
                    employeeUnitHelper.getChildUnitIds(userUnitId, unitIds);
                }

                // 檢查目標員工是否在這些部門中
                List<EmployeeUnitEntity> targetUnits = employeeUnitHelper.getEmployeeUnitByEmployeeId(targetEmployee.getEmployeeId());
                boolean hasAccess = targetUnits.stream()
                        .anyMatch(targetUnit -> unitIds.contains(targetUnit.getId().getUnitId()) && Boolean.TRUE.equals(targetUnit.getEnabled()));

                if (!hasAccess) {
                    throw new IllegalArgumentException(messageHelper.localizedMessage("error.employee.access.denied"));
                }
                break;

            case ALL:
                // 可以存取所有員工資料，不需要額外檢查
                break;

            default:
                throw new IllegalArgumentException(messageHelper.localizedMessage("error.data.scope.invalid"));
        }
    }
}
