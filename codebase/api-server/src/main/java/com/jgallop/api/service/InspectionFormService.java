package com.jgallop.api.service;

import com.jgallop.api.entity.*;
import com.jgallop.api.entity.enums.InspectionFormType;
import com.jgallop.api.entity.enums.InspectionStatus;
import com.jgallop.api.entity.enums.QuestionType;
import com.jgallop.api.exception.AccessDeniedException;
import com.jgallop.api.exception.BadRequestException;
import com.jgallop.api.model.req.ReqBindParentInspectionFormModel;
import com.jgallop.api.model.req.ReqRetestResultModel;
import com.jgallop.api.model.req.ReqUpdateInspectionFormInspectorModel;
import com.jgallop.api.model.req.ReqUpdateInspectionFormModel;
import com.jgallop.api.model.resp.RespInspectionFormListModel;
import com.jgallop.api.model.resp.RespInspectionFormModel;
import com.jgallop.user.entity.UserEntity;
import com.jgallop.api.model.resp.RespInspectionFormSummaryModel;
import com.jgallop.api.model.resp.RespRecyclePriceModel;
import com.jgallop.api.model.resp.RespRetestQuestionWrapper;
import com.jgallop.api.model.resp.RespRetestQuestion;
import com.jgallop.api.model.resp.RespRetestQuestionOption;
import com.jgallop.api.model.resp.RespSampleImageModel;
import com.jgallop.api.repository.*;
import com.jgallop.api.repository.InspectionDetailRepository;
import com.jgallop.api.repository.InspectionResultRepository;
import com.jgallop.api.repository.InspectionFormRepository;
import com.jgallop.api.repository.InspectionQuestionOptionRepository;
import com.jgallop.api.util.ExcelDataConverter;
import com.jgallop.common.service.MessageHelper;
import com.jgallop.user.service.UserHelper;
import jakarta.persistence.criteria.JoinType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 檢測單服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InspectionFormService {

    private final CompanyHelper companyHelper;
    private final PartnerHelper partnerHelper;
    private final EmployeeHelper employeeHelper;
    private final EmployeeUnitHelper employeeUnitHelper;
    private final PartnerPersonnelHelper partnerPersonnelHelper;
    private final PartnerAccessControlHelper partnerAccessControlHelper;

    private final FileHelper fileHelper;
    private final UserHelper userHelper;
    private final MessageHelper messageHelper;
    private final ExcelGenerator excelGenerator;
    private final PartNumberHelper partNumberHelper;
    private final RecycleOrderHelper recycleOrderHelper;
    private final SystemAccountHelper systemAccountHelper;
    private final InspectionFormHelper inspectionFormHelper;

    private final InspectionFormRepository inspectionFormRepository;
    private final InspectionDetailRepository inspectionDetailRepository;
    private final InspectionResultRepository inspectionResultRepository;
    private final InspectionQuestionRepository inspectionQuestionRepository;
    private final InspectionGroupQuestionRepository inspectionGroupQuestionRepository;
    private final InspectionQuestionOptionRepository inspectionQuestionOptionRepository;

    /**
     * 查詢檢測單列表
     *
     * @param page                   頁碼
     * @param size                   每頁筆數
     * @param inspectionFormCode     檢測單單號
     * @param inspectionFormDate     檢測日期
     * @param transactionAgreementCode 買賣契約書代碼
     * @param partnerCode            供應商代碼
     * @param partnerName            供應商名稱
     * @param partnerInspector       供應商檢測人員
     * @param retestUnit             複測單位
     * @param retestPersonnel        複測人員
     * @param inspectionStatus       檢測狀態
     * @param customerId             客戶ID
     * @param customer3c91Id         客戶3c91ID
     * @param customerIposId         客戶IposID
     * @param sellerName             賣家名稱
     * @param sellerContactPhone     賣家聯絡電話
     * @param imei                   IMEI號碼
     * @param serialNumber           序號
     * @param partNumberCode         料件編號
     * @param brand                  品牌
     * @param productSeries          產品系列
     * @param productName            產品名稱
     * @return 檢測單列表
     */
    public RespInspectionFormListModel queryInspectionForms(
            int page, int size, String inspectionFormCode, Long inspectionFormDate, String transactionAgreementCode,
            String partnerCode, String partnerName, String partnerInspector, String retestUnit, String retestPersonnel,
            InspectionStatus inspectionStatus, String customerId, String customer3c91Id, String customerIposId,
            String sellerName, String sellerContactPhone, String imei, String serialNumber,
            String partNumberCode, String brand, String productSeries, String productName) {

        // 初始化 Specification，預設 null
        Specification<InspectionFormEntity> spec = Specification.where(null);

        spec = applyDataScope(spec);

        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "inspectionFormCode", inspectionFormCode);
        spec = SpecificationHelper.addDateRangeSpecificationForLong(spec, "inspectionDate", inspectionFormDate, ZoneId.systemDefault());
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "transactionAgreementCode", transactionAgreementCode);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partnerCode", partnerCode);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partnerName", partnerName);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partnerInspector", partnerInspector);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "retestUnit", retestUnit);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "retestPersonnel", retestPersonnel);
        spec = SpecificationHelper.addEqualSpecificationForShort(spec, "inspectionStatus", inspectionStatus != null ? inspectionStatus.getCode() : null);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "customerId", customerId);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "customer3c91Id", customer3c91Id);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "customerIposId", customerIposId);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "sellerName", sellerName);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "sellerContactPhone", sellerContactPhone);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "imei", imei);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "serialNumber", serialNumber);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partNumberCode", partNumberCode);

        // 品牌、產品系列、產品名稱 - 這三個欄位都來自partNumberId去做查詢
        if (StringUtils.hasText(brand) || StringUtils.hasText(productSeries) || StringUtils.hasText(productName)) {
            spec = spec.and((root, query, cb) -> {
                // 使用 JOIN 查詢關聯表
                assert query != null;
                query.distinct(true);

                // 關聯 PartNumberEntity
                var partNumberJoin = root.join("partNumberId", JoinType.LEFT);

                // 建立條件
                List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

                // 產品名稱
                if (StringUtils.hasText(productName)) {
                    predicates.add(cb.like(partNumberJoin.get("productName"), "%" + productName + "%"));
                }

                // 產品系列
                if (StringUtils.hasText(productSeries)) {
                    var productSeriesJoin = partNumberJoin.join("productSeriesId", JoinType.LEFT);
                    predicates.add(cb.like(productSeriesJoin.get("productSeriesName"), "%" + productSeries + "%"));
                }

                // 品牌
                if (StringUtils.hasText(brand)) {
                    var productSeriesJoin = partNumberJoin.join("productSeriesId", JoinType.LEFT);
                    var brandJoin = productSeriesJoin.join("brandId", JoinType.LEFT);
                    predicates.add(cb.like(brandJoin.get("brandName"), "%" + brand + "%"));
                }

                return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
            });
        }

        Pageable pageable = PageHelper.getPageableOrderByParam(page, size, "lastModifyDatetime", Sort.Direction.DESC);
        Page<InspectionFormEntity> resultPage = inspectionFormRepository.findAll(spec, pageable);

        // 使用摘要模型轉換，提高效能
        List<RespInspectionFormSummaryModel> models = resultPage.getContent().stream()
                .map(this::convertToRespInspectionFormSummaryModel)
                .toList();

        return new RespInspectionFormListModel(resultPage.getTotalElements(), models);
    }

    /**
     * 查詢檢測單詳情
     *
     * @param inspectionFormId 檢測單編號
     * @return 檢測單詳情
     */
    public RespInspectionFormModel getInspectionFormById(String inspectionFormId) {
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);
        return convertToRespInspectionFormModel(entity);
    }

    /**
     * 查詢檢測單回收價格
     *
     * @param inspectionFormId 檢測單編號
     * @return 回收價格
     */
    public RespRecyclePriceModel getRecyclePriceById(String inspectionFormId) {
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);
        return RespRecyclePriceModel.builder()
                .recyclePrice(entity.getRecyclePrice())
                .build();
    }

    /**
     * 更新檢測單
     *
     * @param inspectionFormId 檢測單ID
     * @param reqModel         更新檢測單請求
     */
    @Transactional
    public void updateInspectionForm(String inspectionFormId, ReqUpdateInspectionFormModel reqModel) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 PROCESSING (4)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.PROCESSING.getCode())) {
            inspectionFormHelper.checkValidOperation("更新檢測單內容", InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription());
        }

        // 檢查是否已綁定回收訂單
        if (StringUtils.hasText(entity.getRecycleOrderId())) {
            throw new IllegalStateException(
                    messageHelper.localizedMessage("error.inspection.form.already.bound.to.recycle.order",
                                                   inspectionFormId));
        }

        entity.setPartnerInspector(reqModel.getPartnerInspector());
        entity.setSerialNumberType(reqModel.getSerialNumberType());
        entity.setImei(reqModel.getImei());
        entity.setSerialNumber(reqModel.getSerialNumber());
        if (reqModel.isVirtualSerialNumber()) {
            entity.setVirtualSerialNumber(entity.getInspectionFormCode());
        }
        entity.setBlanccoUuid(reqModel.getBlanccoUuid());
        entity.setNote(reqModel.getNote());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 更新檢測單的門市檢測人員
     *
     * @param inspectionFormId 檢測單ID
     * @param reqModel         更新檢測人員請求模型
     */
    @Transactional
    public void updateInspectionFormInspector(String inspectionFormId, ReqUpdateInspectionFormInspectorModel reqModel) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 更新檢測人員
        entity.setPartnerInspector(reqModel.getInspectorId());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 進入修改中狀態 (狀態 1 -> 2)
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void startEdit(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 NEW (1)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.NEW.getCode())) {
            inspectionFormHelper.checkValidStatusTransition(InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription(), InspectionStatus.PROCESSING.getDescription());
        }

        // 檢查是否已綁定回收訂單
        if (StringUtils.hasText(entity.getRecycleOrderId())) {
            throw new IllegalStateException(
                    messageHelper.localizedMessage("error.inspection.form.already.bound.to.recycle.order",
                                                   inspectionFormId));
        }

        // 更新狀態為 PROCESSING (4)
        entity.setInspectionStatus(InspectionStatus.PROCESSING.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 取消檢測單 (狀態 1 -> -1)
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void cancelInspectionForm(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 NEW (1)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.NEW.getCode())) {
            inspectionFormHelper.checkValidStatusTransition(InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription(), InspectionStatus.CANCELED.getDescription());
        }

        // 檢查是否已綁定回收訂單
        if (StringUtils.hasText(entity.getRecycleOrderId())) {
            throw new IllegalStateException(
                    messageHelper.localizedMessage("error.inspection.form.already.bound.to.recycle.order",
                                                   inspectionFormId));
        }

        // 更新狀態為 CANCELED (-1)
        entity.setInspectionStatus(InspectionStatus.CANCELED.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 綁定父類檢測單，帶入賣家資訊
     *
     * @param inspectionFormId 檢測單編號
     * @param reqModel         綁定父類檢測單請求
     */
    @Transactional
    public void bindParentInspectionForm(String inspectionFormId, ReqBindParentInspectionFormModel reqModel) {

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 NEW (1)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.NEW.getCode())) {
            inspectionFormHelper.checkValidOperation("綁定父類檢測單", InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription());
        }

        // 檢查是否已綁定回收訂單
        if (StringUtils.hasText(entity.getRecycleOrderId())) {
            throw new IllegalStateException(
                    messageHelper.localizedMessage("error.inspection.form.already.bound.to.recycle.order",
                                                   inspectionFormId));
        }

        // 查詢父類檢測單
        InspectionFormEntity parentEntity = inspectionFormHelper.findInspectionFormByCode(reqModel.getParentInspectionFormCode());
        validateDataScopeAccess(parentEntity);

        // 驗證父檢測單與子檢測單的料件編號是否一致
        inspectionFormHelper.validatePartNumberConsistency(entity, parentEntity);

        // 設置父類檢測單資訊
        entity.setSuperFormType(parentEntity.getInspectionFormType().name());
        entity.setSuperFormCode(parentEntity.getInspectionFormCode());

        // 帶入賣家資訊
        entity.setSellerName(parentEntity.getSellerName());
        entity.setSellerIdNumber(parentEntity.getSellerIdNumber());
        entity.setSellerContactPhone(parentEntity.getSellerContactPhone());
        entity.setSellerContactAddress(parentEntity.getSellerContactAddress());
        entity.setReturnRecipientName(parentEntity.getReturnRecipientName());
        entity.setReturnRecipientContactPhone(parentEntity.getReturnRecipientContactPhone());
        entity.setReturnRecipientContactAddress(parentEntity.getReturnRecipientContactAddress());

        // 更新修改者和修改時間
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 存檔完成修改 (狀態 2 -> 1)
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void saveEdit(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 PROCESSING (4)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.PROCESSING.getCode())) {
            inspectionFormHelper.checkValidStatusTransition(InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription(), InspectionStatus.NEW.getDescription());
        }

        // 更新狀態為 NEW (1)
        entity.setInspectionStatus(InspectionStatus.NEW.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 查詢複測題目
     *
     * @param inspectionFormId 檢測單ID
     * @return 複測題目包裝器，包含檢測群組和題目選項的完整層次結構
     */
    @Transactional(readOnly = true)
    public RespRetestQuestionWrapper queryRetestQuestions(String inspectionFormId) {
        // 1. 查詢檢測單
        InspectionFormEntity inspectionForm = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(inspectionForm);

        // 2. 獲取檢測群組ID
        String groupId = inspectionForm.getInspectionGroupId();
        String groupCode = inspectionForm.getInspectionGroupCode();
        String groupName = inspectionForm.getInspectionGroupName();

        if (groupId == null) {
            return RespRetestQuestionWrapper.builder()
                    .groupId(null)
                    .groupCode(null)
                    .groupName(null)
                    .questions(new ArrayList<>())
                    .build();
        }

        // 3. 獲取檢測群組中的所有題目ID
        List<InspectionGroupQuestionEntity> groupQuestions = inspectionGroupQuestionRepository
                .findByIdGroupId(groupId);

        if (groupQuestions.isEmpty()) {
            return RespRetestQuestionWrapper.builder()
                    .groupId(groupId)
                    .groupCode(groupCode)
                    .groupName(groupName)
                    .questions(new ArrayList<>())
                    .build();
        }

        // 4. 獲取題目ID列表
        List<String> questionIds = groupQuestions.stream()
                .map(q -> q.getId().getQuestionId())
                .toList();

        // 5. 查詢所有啟用的SMARTMOBILE題目
        List<InspectionQuestionEntity> questions = inspectionQuestionRepository.findByQuestionIdInAndQuestionTypeAndEnabledIsTrue(questionIds, QuestionType.SMARTMOBILE);

        // 6. 構建題目回應模型列表
        List<RespRetestQuestion> questionModels = buildRetestQuestions(questions);

        // 7. 構建並返回最終回應
        return RespRetestQuestionWrapper.builder()
                .groupId(groupId)
                .groupCode(groupCode)
                .groupName(groupName)
                .questions(questionModels)
                .build();
    }

    /**
     * 構建複測題目列表，包含選項和範例圖片
     *
     * @param questions 檢測題目實體列表
     * @return 複測題目回應模型列表
     */
    private List<RespRetestQuestion> buildRetestQuestions(List<InspectionQuestionEntity> questions) {
        List<RespRetestQuestion> questionModels = new ArrayList<>();

        for (InspectionQuestionEntity question : questions) {
            // 查詢題目的選項
            List<InspectionQuestionOptionEntity> options = inspectionQuestionOptionRepository
                    .findByQuestionIdAndEnabled(question.getQuestionId(), true);

            if (options.isEmpty()) {
                continue; // 跳過沒有選項的問題
            }

            // 構建選項模型
            List<RespRetestQuestionOption> optionModels = buildRetestQuestionOptions(options);

            // 構建題目模型
            RespRetestQuestion questionModel = RespRetestQuestion.builder()
                    .questionId(question.getQuestionId())
                    .questionType(question.getQuestionType())
                    .questionCode(question.getQuestionCode())
                    .questionName(question.getQuestionName())
                    .isRequired(question.getIsRequired())
                    .isMultiple(question.getIsMultiple())
                    .options(optionModels)
                    .build();

            questionModels.add(questionModel);
        }

        return questionModels;
    }

    /**
     * 構建複測題目選項列表，包含範例圖片
     *
     * @param options 檢測題目選項實體列表
     * @return 複測題目選項回應模型列表
     */
    private List<RespRetestQuestionOption> buildRetestQuestionOptions(List<InspectionQuestionOptionEntity> options) {
        List<RespRetestQuestionOption> optionModels = new ArrayList<>();

        for (InspectionQuestionOptionEntity option : options) {
            // 處理範例圖片
            List<RespSampleImageModel> sampleImages = new ArrayList<>();
            if (option.getSampleImages() != null && !option.getSampleImages().isEmpty()) {
                sampleImages = option.getSampleImages().stream()
                        .filter(image -> Boolean.TRUE.equals(image.getEnabled()))
                        .map(image -> {
                            RespSampleImageModel sampleImage = new RespSampleImageModel();
                            sampleImage.setSampleImageId(image.getSampleImageId());
                            sampleImage.setImageId(image.getImageId());
                            sampleImage.setSequence(image.getSequence());
                            sampleImage.setImagePath(fileHelper.getFilePath(image.getImageId()));
                            sampleImage.setImageName(fileHelper.getFileName(image.getImageId()));
                            return sampleImage;
                        })
                        .sorted(Comparator.comparing(RespSampleImageModel::getSequence))
                        .collect(Collectors.toList());
            }

            // TODO: 需要配合檢測題目選項結構調整
            // InspectionQuestionOptionEntity 已移除金額相關欄位，需要從子選項獲取金額資訊
            RespRetestQuestionOption optionModel = RespRetestQuestionOption.builder()
                    .optionId(option.getOptionId())
                    .sequence(option.getSequence())
                    .content(option.getContent())
//                    .calculationType(option.getCalculationType()) // TODO: 需要從子選項獲取
//                    .bonusAmount(option.getBonusAmount()) // TODO: 需要從子選項獲取
//                    .deductionAmount(option.getDeductionAmount()) // TODO: 需要從子選項獲取
                    .isRequired(option.getIsRequired())
                    .coverImagePath(option.getCoverImageId() != null ? fileHelper.getFilePath(option.getCoverImageId()) : null)
                    .coverImageName(option.getCoverImageId() != null ? fileHelper.getFileName(option.getCoverImageId()) : null)
                    .sampleImages(sampleImages)
                    .hint(option.getHint())
                    .build();

            optionModels.add(optionModel);
        }

        // 按照 sequence 排序選項
        optionModels.sort(Comparator.comparing(RespRetestQuestionOption::getSequence));

        return optionModels;
    }

    /**
     * 完成複測並標記為完成訂單
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void completeRetestAndOrder(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 RETEST_COMPLETED
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.RETEST_COMPLETED.getCode())) {
            throw new BadRequestException(
                    messageHelper.localizedMessage("error.inspection.form.invalid.status.for.complete.order",
                                                   entity.getInspectionFormId(),
                                                   InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription()));
        }

        // 更新狀態為 ORDER_COMPLETED
        entity.setInspectionStatus(InspectionStatus.ORDER_COMPLETED.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.save(entity);
    }

    /**
     * 完成複測並標記為已退件
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void completeRetestAndReturn(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 RETEST_COMPLETED
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.RETEST_COMPLETED.getCode())) {
            throw new BadRequestException(
                    messageHelper.localizedMessage("error.inspection.form.invalid.status.for.return",
                                                   entity.getInspectionFormId(),
                                                   InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription()));
        }

        // 更新狀態為 RETURNED
        entity.setInspectionStatus(InspectionStatus.RETURNED.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.save(entity);
    }

    /**
     * 開始複測 (狀態 4 -> 10)
     *
     * @param inspectionFormId 檢測單編號
     */
    @Transactional
    public void startRetest(String inspectionFormId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢檢測單
        InspectionFormEntity entity = inspectionFormHelper.findInspectionFormById(inspectionFormId);
        validateDataScopeAccess(entity);

        // 檢查狀態是否為 RECEIVED (4)
        if (!Objects.equals(entity.getInspectionStatus(), InspectionStatus.RECEIVED.getCode())) {
            throw new BadRequestException(
                    messageHelper.localizedMessage("error.inspection.form.invalid.status.for.start.retest",
                                                   entity.getInspectionFormId(),
                                                   InspectionStatus.fromCode(entity.getInspectionStatus()).getDescription()));
        }

        // 更新狀態為 RETESTING (10)
        entity.setInspectionStatus(InspectionStatus.RETESTING.getCode());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        inspectionFormRepository.saveAndFlush(entity);
    }

    /**
     * 將檢測單實體轉換為摘要回應模型（用於列表顯示，不包含詳細資訊）
     *
     * @param entity 檢測單實體
     * @return 檢測單摘要回應模型
     */
    private RespInspectionFormSummaryModel convertToRespInspectionFormSummaryModel(InspectionFormEntity entity) {
        // 獲取料號名稱
        String partNumberName = null;
        if (StringUtils.hasText(entity.getPartNumberId())) {
            try {
                PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(entity.getPartNumberId());
                partNumberName = partNumber.getPartNumberName();
            } catch (Exception e) {
                // 如果找不到料號，記錄警告但不影響其他資料
                log.warn("無法找到料號ID: {}", entity.getPartNumberId());
            }
        }

        return RespInspectionFormSummaryModel.builder()
                .inspectionFormId(entity.getInspectionFormId())
                .inspectionFormCode(entity.getInspectionFormCode())
                .superFormType(entity.getSuperFormType())
                .superFormCode(entity.getSuperFormCode())
                .inspectionDate(entity.getInspectionDate())
                .inspectionStatus(InspectionStatus.fromCode(entity.getInspectionStatus()))
                .inspectionFormType(entity.getInspectionFormType())
                .recycleOrderId(entity.getRecycleOrderId())
                .recyclePrice(entity.getRecyclePrice())
                .recycleQuote(entity.getRecycleQuote())
                .partnerCode(entity.getPartnerCode())
                .partnerName(entity.getPartnerName())
                .serialNumber(entity.getSerialNumber())
                .imei(entity.getImei())
                .partNumberId(entity.getPartNumberId())
                .partNumberCode(entity.getPartNumberCode())
                .partNumberName(partNumberName)
                .transactionAgreementCode(entity.getTransactionAgreementCode())
                .creator(systemAccountHelper.findUserNameByUserId(entity.getCreator()))
                .createDatetime(entity.getCreateDatetime())
                .lastModifier(systemAccountHelper.findUserNameByUserId(entity.getLastModifier()))
                .lastModifyDatetime(entity.getLastModifyDatetime())
                .build();
    }

    /**
     * 將檢測單實體轉換為回應模型
     *
     * @param entity 檢測單實體
     * @return 檢測單回應模型
     */
    private RespInspectionFormModel convertToRespInspectionFormModel(InspectionFormEntity entity) {
        return convertToRespInspectionFormModel(entity, new HashSet<>());
    }

    /**
     * 上傳複測結果
     *
     * @param reqModel 複測結果請求模型
     * @return 檢測單回應模型
     */
    @Transactional
    public RespInspectionFormModel uploadRetestResult(ReqRetestResultModel reqModel) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 查詢父檢測單
        InspectionFormEntity parentForm = inspectionFormHelper.findInspectionFormById(reqModel.getParentInspectionFormId());
        validateDataScopeAccess(parentForm);

        // 檢查父檢測單狀態是否為 RETESTING
        if (!Objects.equals(parentForm.getInspectionStatus(), InspectionStatus.RETESTING.getCode())) {
            throw new BadRequestException(
                    messageHelper.localizedMessage("error.inspection.form.invalid.status.for.retest",
                                                   parentForm.getInspectionFormId(),
                                                   InspectionStatus.fromCode(parentForm.getInspectionStatus()).getDescription()));
        }

        // 創建新的複測檢測單
        InspectionFormEntity retestForm = new InspectionFormEntity();

        // 設置基本資訊
        retestForm.setInspectionFormCode(inspectionFormHelper.generateInspectionFormCode());
        retestForm.setInspectionFormType(InspectionFormType.MANUAL_RETEST);
        retestForm.setInspectionStatus(InspectionStatus.RETEST_COMPLETED.getCode()); // 直接設為複測完成狀態
        retestForm.setInspectionDate(System.currentTimeMillis());

        // 設置父檢測單資訊
        retestForm.setSuperFormType(parentForm.getInspectionFormType().name());
        retestForm.setSuperFormCode(parentForm.getInspectionFormCode());

        // 複製父檢測單的基本資料
        retestForm.setPartnerCode(parentForm.getPartnerCode());
        retestForm.setPartnerName(parentForm.getPartnerName());
        retestForm.setPartnerStore(parentForm.getPartnerStore());
        retestForm.setPartnerInspector(parentForm.getPartnerInspector());
        retestForm.setTransactionAgreementId(parentForm.getTransactionAgreementId());
        retestForm.setTransactionAgreementCode(parentForm.getTransactionAgreementCode());
        retestForm.setSerialNumberType(parentForm.getSerialNumberType());
        retestForm.setVirtualSerialNumber(parentForm.getVirtualSerialNumber());
        retestForm.setSerialNumber(parentForm.getSerialNumber());
        retestForm.setImei(parentForm.getImei());
        retestForm.setBlanccoUuid(parentForm.getBlanccoUuid());
        retestForm.setNote(parentForm.getNote());
        retestForm.setPartNumberId(parentForm.getPartNumberId());
        retestForm.setPartNumberCode(parentForm.getPartNumberCode());
        retestForm.setInspectionGroupId(parentForm.getInspectionGroupId());
        retestForm.setInspectionGroupCode(parentForm.getInspectionGroupCode());
        retestForm.setInspectionGroupName(parentForm.getInspectionGroupName());

        // 設置複測相關資訊（從當前登入使用者獲取）
        String[] retestInfo = getRetestInfoFromCurrentUser();
        retestForm.setRetestPersonnel(retestInfo[0]);
        retestForm.setRetestUnit(retestInfo[1]);
        retestForm.setRetestCompany(retestInfo[2]);
        retestForm.setRecyclePrice(reqModel.getRecyclePrice());
        retestForm.setRecycleQuote(reqModel.getRecycleQuote());

        // 複製賣家資訊
        retestForm.setSellerName(parentForm.getSellerName());
        retestForm.setSellerIdNumber(parentForm.getSellerIdNumber());
        retestForm.setSellerContactPhone(parentForm.getSellerContactPhone());
        retestForm.setSellerContactAddress(parentForm.getSellerContactAddress());
        retestForm.setReturnRecipientName(parentForm.getReturnRecipientName());
        retestForm.setReturnRecipientContactPhone(parentForm.getReturnRecipientContactPhone());
        retestForm.setReturnRecipientContactAddress(parentForm.getReturnRecipientContactAddress());

        // 設置創建者資訊
        retestForm.setCreator(loginUserId);
        retestForm.setCreateDatetime(System.currentTimeMillis());
        retestForm.setLastModifier(loginUserId);
        retestForm.setLastModifyDatetime(System.currentTimeMillis());

        // 保存複測檢測單
        InspectionFormEntity savedForm = inspectionFormRepository.save(retestForm);

        // 創建檢測明細和結果
        for (ReqRetestResultModel.TestResult testResult : reqModel.getTestResults()) {
            // 創建檢測明細
            InspectionDetailEntity detail = new InspectionDetailEntity();
            detail.setInspectionFormId(savedForm.getInspectionFormId());
            detail.setInspectionType("RETEST"); // 設置為複測類型
            detail.setInspectionCode(testResult.getQuestionCode());
            detail.setInspectionQuestion(testResult.getQuestionName());
            detail.setCreator(loginUserId);
            detail.setCreateDatetime(System.currentTimeMillis());
            detail.setLastModifier(loginUserId);
            detail.setLastModifyDatetime(System.currentTimeMillis());

            InspectionDetailEntity savedDetail = inspectionDetailRepository.save(detail);

            // 創建檢測結果
            for (ReqRetestResultModel.Option option : testResult.getOptions()) {
                InspectionResultEntity resultEntity = new InspectionResultEntity();
                resultEntity.setInspectionDetailId(savedDetail.getInspectionDetailId());
                resultEntity.setInspectionOption(option.getContent());
                resultEntity.setIsSelected(option.getSelected());
                resultEntity.setCalculationType(option.getCalculationType());
                resultEntity.setBonusAmount(option.getBonusAmount());
                resultEntity.setDeductionAmount(option.getDeductionAmount());
                resultEntity.setCreator(loginUserId);
                resultEntity.setCreateDatetime(System.currentTimeMillis());
                resultEntity.setLastModifier(loginUserId);
                resultEntity.setLastModifyDatetime(System.currentTimeMillis());

                inspectionResultRepository.save(resultEntity);
            }
        }

        // 更新父檢測單狀態為複測完成
        parentForm.setInspectionStatus(InspectionStatus.RETEST_COMPLETED.getCode());
        parentForm.setLastModifier(loginUserId);
        parentForm.setLastModifyDatetime(System.currentTimeMillis());
        inspectionFormRepository.save(parentForm);

        // 返回完整的檢測單模型（包含檢測明細和結果）
        return convertToRespInspectionFormModel(savedForm);
    }

    /**
     * 將檢測單實體轉換為回應模型（帶追蹤集合）
     *
     * @param entity 檢測單實體
     * @param processedIds 已處理的檢測單ID集合，用於防止遞迴
     * @return 檢測單回應模型
     */
    private RespInspectionFormModel convertToRespInspectionFormModel(InspectionFormEntity entity, Set<String> processedIds) {
        // 如果這個檢測單已經處理過，直接返回 null 防止遞迴
        if (!processedIds.add(entity.getInspectionFormId())) {
            return null;
        }

        // 查詢檢測明細
        List<InspectionDetailEntity> detailEntities = inspectionDetailRepository.findByInspectionFormId(entity.getInspectionFormId());

        // 轉換檢測明細及結果
        List<RespInspectionFormModel.InspectionDetailModel> detailModels = detailEntities.stream()
                .map(detail -> {
                    // 查詢檢測結果
                    List<InspectionResultEntity> resultEntities = inspectionResultRepository.findByInspectionDetailId(detail.getInspectionDetailId());

                    // 轉換檢測結果
                    List<RespInspectionFormModel.InspectionResultModel> resultModels = resultEntities.stream()
                            .map(result -> RespInspectionFormModel.InspectionResultModel.builder()
                                    .inspectionResultId(result.getInspectionResultId())
                                    .inspectionOption(result.getInspectionOption())
                                    .isSelected(result.getIsSelected())
                                    .calculationType(result.getCalculationType())
                                    .bonusAmount(result.getBonusAmount())
                                    .deductionAmount(result.getDeductionAmount())
                                    .build())
                            .toList();

                    // 建立檢測明細模型
                    return RespInspectionFormModel.InspectionDetailModel.builder()
                            .inspectionDetailId(detail.getInspectionDetailId())
                            .inspectionType(detail.getInspectionType())
                            .inspectionCode(detail.getInspectionCode())
                            .inspectionQuestion(detail.getInspectionQuestion())
                            .inspectionResults(resultModels)
                            .build();
                })
                .toList();

        // 獲取上層檢測單 ID
        String superFormId = null;
        if (StringUtils.hasText(entity.getSuperFormCode())) {
            try {
                InspectionFormEntity superForm = inspectionFormHelper.findInspectionFormByCode(entity.getSuperFormCode());
                superFormId = superForm.getInspectionFormId();
            } catch (Exception e) {
                // 如果找不到上層檢測單，記錄警告但不影響其他資料
                log.warn("無法找到上層檢測單代碼: {}", entity.getSuperFormCode());
            }
        }

        // 獲取回收單號
        String recycleOrderCode = null;
        if (StringUtils.hasText(entity.getRecycleOrderId())) {
            try {
                RecycleOrderEntity recycleOrder = recycleOrderHelper.findRecycleOrderByIdOrElseThrow(entity.getRecycleOrderId());
                recycleOrderCode = recycleOrder.getRecycleOrderCode();
            } catch (Exception e) {
                // 如果找不到回收訂單，記錄警告但不影響其他資料
                log.warn("無法找到回收訂單ID: {}", entity.getRecycleOrderId());
            }
        }

        // 獲取料號名稱
        String partNumberName = null;
        if (StringUtils.hasText(entity.getPartNumberId())) {
            try {
                PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(entity.getPartNumberId());
                partNumberName = partNumber.getPartNumberName();
            } catch (Exception e) {
                // 如果找不到料號，記錄警告但不影響其他資料
                log.warn("無法找到料號ID: {}", entity.getPartNumberId());
            }
        }

        // 獲取買賣切結書檔案資訊
        String transactionAgreementFilePath = getFilePathSafely(entity.getTransactionAgreementId());
        String transactionAgreementFileName = getFileNameSafely(entity.getTransactionAgreementId());

        RespInspectionFormModel model = RespInspectionFormModel.builder()
                .inspectionFormId(entity.getInspectionFormId())
                .inspectionFormCode(entity.getInspectionFormCode())
                .superFormType(entity.getSuperFormType())
                .superFormCode(entity.getSuperFormCode())
                .superFormId(superFormId)
                .inspectionDate(entity.getInspectionDate())
                .inspectionStatus(InspectionStatus.fromCode(entity.getInspectionStatus()))
                .inspectionFormType(entity.getInspectionFormType())
                .recycleOrderId(entity.getRecycleOrderId())
                .recycleOrderCode(recycleOrderCode)
                .recyclePrice(entity.getRecyclePrice())
                .recycleQuote(entity.getRecycleQuote())
                .partnerCode(entity.getPartnerCode())
                .partnerName(entity.getPartnerName())
                .partnerInspector(systemAccountHelper.findUserNameByUserId(entity.getPartnerInspector()))
                .transactionAgreementId(entity.getTransactionAgreementId())
                .transactionAgreementCode(entity.getTransactionAgreementCode())
                .transactionAgreementFilePath(transactionAgreementFilePath)
                .transactionAgreementFileName(transactionAgreementFileName)
                .serialNumberType(entity.getSerialNumberType())
                .serialNumber(entity.getSerialNumber())
                .imei(entity.getImei())
                .blanccoUuid(entity.getBlanccoUuid())
                .note(entity.getNote())
                .partNumberId(entity.getPartNumberId())
                .partNumberCode(entity.getPartNumberCode())
                .partNumberName(partNumberName)
                .retestPersonnel(entity.getRetestPersonnel())
                .retestUnit(entity.getRetestUnit())
                .retestCompany(entity.getRetestCompany())
                .sellerName(entity.getSellerName())
                .sellerIdNumber(entity.getSellerIdNumber())
                .sellerContactPhone(entity.getSellerContactPhone())
                .sellerContactAddress(entity.getSellerContactAddress())
                .creator(systemAccountHelper.findUserNameByUserId(entity.getCreator()))
                .createDatetime(entity.getCreateDatetime())
                .lastModifier(systemAccountHelper.findUserNameByUserId(entity.getLastModifier()))
                .lastModifyDatetime(entity.getLastModifyDatetime())
                .inspectionGroupId(entity.getInspectionGroupId())
                .inspectionGroupCode(entity.getInspectionGroupCode())
                .inspectionGroupName(entity.getInspectionGroupName())
                .inspectionDetails(detailModels)
                .build();

        // 查詢子檢測單 - 直接查詢是否有以當前檢測單代碼為上層表單代碼的子檢測單
        List<InspectionFormEntity> childEntities = inspectionFormRepository.findBySuperFormCode(entity.getInspectionFormCode());

        if (!childEntities.isEmpty()) {
            // 轉換子檢測單，傳遞已處理ID集合防止遞迴
            // 使用新的 HashSet 副本，確保每個分支的處理不會互相影響
            List<RespInspectionFormModel> childModels = childEntities.stream()
                    .map(childEntity -> convertToRespInspectionFormModel(childEntity, new HashSet<>(processedIds)))
                    .filter(Objects::nonNull) // 過濾掉可能因遞迴返回的null值
                    .toList();

            if (!childModels.isEmpty()) {
                model.setChildInspectionForms(childModels);
            }
        }

        return model;
    }

    /**
     * 從當前登入使用者獲取複測相關資訊
     *
     * @return 包含複測人員、單位、公司資訊的陣列 [retestPersonnel, retestUnit, retestCompany]
     */
    private String[] getRetestInfoFromCurrentUser() {
        UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        String retestPersonnel = "";
        String retestUnit = "";
        String retestCompany = "";

        switch (userEntity.getUserType()) {
            case EMPLOYEE -> {
                // 員工類型：從員工資料獲取資訊
                if (userEntity.getEmployeeId() != null) {
                    EmployeeEntity employee = employeeHelper.findEmployeeByEmployeeIdOrNull(userEntity.getEmployeeId());
                    if (employee != null) {
                        // 複測人員：使用員工中文姓名
                        retestPersonnel = employee.getZhName() != null ? employee.getZhName() : "";

                        // 複測單位：獲取員工所屬組織單位
                        List<String> unitNames = employeeUnitHelper.getUnitNameListByEmployeeId(employee.getEmployeeId());
                        retestUnit = unitNames.isEmpty() ? "" : String.join(", ", unitNames);

                        // 複測公司：獲取員工所屬公司名稱
                        retestCompany = companyHelper.getCompanyZhName(employee.getCompanyId());
                    }
                }
            }
            case PARTNER -> {
                // 供應商人員類型：從人員資料獲取資訊
                if (userEntity.getPersonnelId() != null) {
                    PartnerPersonnelEntity personnel = partnerPersonnelHelper.findPartnerPersonnelByIdOrNull(userEntity.getPersonnelId());
                    if (personnel != null) {
                        // 複測人員：使用人員中文姓名
                        retestPersonnel = personnel.getPersonnelZhName() != null ? personnel.getPersonnelZhName() : "";

                        // 複測單位：暫時留空，供應商人員沒有明確的組織單位概念
                        retestUnit = "";

                        // 複測公司：獲取供應商公司名稱
                        if (personnel.getPartnerId() != null) {
                            PartnerProfileEntity partner = partnerHelper.findByPartnerIdAndEnabledIsTrueOrNull(personnel.getPartnerId());
                            retestCompany = partner != null && partner.getPartnerZhName() != null ? partner.getPartnerZhName() : "";
                        }
                    }
                }
            }
            default -> log.warn("未支援的使用者類型進行複測操作：{}", userEntity.getUserType());
        }

        log.debug("獲取複測資訊：人員={}, 單位={}, 公司={}", retestPersonnel, retestUnit, retestCompany);
        return new String[]{retestPersonnel, retestUnit, retestCompany};
    }

    /**
     * 安全地獲取檔案路徑，如果檔案不存在則返回null
     *
     * @param fileId 檔案ID
     * @return 檔案路徑，檔案不存在時返回null
     */
    private String getFilePathSafely(String fileId) {
        if (!StringUtils.hasText(fileId)) {
            return null;
        }

        try {
            return fileHelper.getFilePath(fileId);
        } catch (Exception e) {
            log.warn("獲取檔案路徑失敗，檔案ID: {}, 錯誤: {}", fileId, e.getMessage());
            return null;
        }
    }

    /**
     * 安全地獲取檔案名稱，如果檔案不存在則返回null
     *
     * @param fileId 檔案ID
     * @return 檔案名稱，檔案不存在時返回null
     */
    private String getFileNameSafely(String fileId) {
        if (!StringUtils.hasText(fileId)) {
            return null;
        }

        try {
            return fileHelper.getFileName(fileId);
        } catch (Exception e) {
            log.warn("獲取檔案名稱失敗，檔案ID: {}, 錯誤: {}", fileId, e.getMessage());
            return null;
        }
    }

    /**
     * 匯出檢測單為 Excel 檔案
     *
     * @param headers                動態表頭列表，為空時使用預設欄位
     * @param inspectionFormCode     檢測單單號
     * @param inspectionFormDate     檢測日期
     * @param transactionAgreementCode 買賣契約書代碼
     * @param partnerCode            供應商代碼
     * @param partnerName            供應商名稱
     * @param partnerInspector       供應商檢測人員
     * @param retestUnit             複測單位
     * @param retestPersonnel        複測人員
     * @param inspectionStatus       檢測狀態
     * @param customerId             客戶ID
     * @param customer3c91Id         客戶3c91ID
     * @param customerIposId         客戶IposID
     * @param sellerName             賣家名稱
     * @param sellerContactPhone     賣家聯絡電話
     * @param imei                   IMEI號碼
     * @param serialNumber           序號
     * @param partNumberCode         料件編號
     * @param brand                  品牌
     * @param productSeries          產品系列
     * @param productName            產品名稱
     * @param request                HTTP 請求物件
     * @return Excel 檔案位元組陣列
     */
    public byte[] exportInspectionFormsToExcel(List<String> headers,
                                               String inspectionFormCode,
                                               Long inspectionFormDate,
                                               String transactionAgreementCode,
                                               String partnerCode,
                                               String partnerName,
                                               String partnerInspector,
                                               String retestUnit,
                                               String retestPersonnel,
                                               InspectionStatus inspectionStatus,
                                               String customerId,
                                               String customer3c91Id,
                                               String customerIposId,
                                               String sellerName,
                                               String sellerContactPhone,
                                               String imei,
                                               String serialNumber,
                                               String partNumberCode,
                                               String brand,
                                               String productSeries,
                                               String productName,
                                               HttpServletRequest request) {
        log.info("Exporting inspection forms to Excel with filters");

        // 使用現有查詢方法獲取完整資料（不分頁）
        RespInspectionFormListModel queryResult = queryInspectionForms(
                0, Integer.MAX_VALUE, inspectionFormCode, inspectionFormDate, transactionAgreementCode,
                partnerCode, partnerName, partnerInspector, retestUnit, retestPersonnel,
                inspectionStatus, customerId, customer3c91Id, customerIposId, sellerName,
                sellerContactPhone, imei, serialNumber, partNumberCode, brand, productSeries, productName);

        List<RespInspectionFormSummaryModel> inspectionForms = queryResult.getData();

        // 處理表頭
        List<String> columnNames = (headers != null && !headers.isEmpty())
                ? headers
                : ExcelGenerator.ExcelInfo.INSPECTION_FORM_LIST.getColumnNames();

        // 獲取時區資訊
        ZoneId zoneId = TimeHelper.resolveZoneId(request);

        // 轉換數據為 Map 列表
        List<Map<String, Object>> data = inspectionForms.stream()
                .map(form -> {
                    Map<String, Object> row = new HashMap<>();

                    // 遍歷表頭，根據表頭名稱設置對應的值
                    for (String header : columnNames) {
                        switch (header) {
                            case "檢測單號" -> row.put(header, ExcelDataConverter.safeString(form.getInspectionFormCode()));
                            case "上層表單類型" -> row.put(header, ExcelDataConverter.safeString(form.getSuperFormType()));
                            case "上層表單編號" -> row.put(header, ExcelDataConverter.safeString(form.getSuperFormCode()));
                            case "檢測日期" -> row.put(header, TimeHelper.formatTimestampToDatetime(form.getInspectionDate(), zoneId));
                            case "檢測單狀態" -> row.put(header, form.getInspectionStatus() != null ? form.getInspectionStatus().getDescription() : "");
                            case "檢測單類型" -> row.put(header, form.getInspectionFormType() != null ? form.getInspectionFormType().getDescription() : "");
                            case "回收訂單號" -> row.put(header, ExcelDataConverter.safeString(form.getRecycleOrderId()));
                            case "回收價格" -> row.put(header, form.getRecyclePrice() != null ? form.getRecyclePrice().toString() : "");
                            case "回收報價" -> row.put(header, form.getRecycleQuote() != null ? form.getRecycleQuote().toString() : "");
                            case "門市代碼" -> row.put(header, ExcelDataConverter.safeString(form.getPartnerCode()));
                            case "門市名稱" -> row.put(header, ExcelDataConverter.safeString(form.getPartnerName()));
//                            case "門市檢測人員" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
                            case "序號" -> row.put(header, ExcelDataConverter.safeString(form.getSerialNumber()));
                            case "買賣切結書編號" -> row.put(header, ExcelDataConverter.safeString(form.getTransactionAgreementCode()));
                            case "IMEI 編號" -> row.put(header, ExcelDataConverter.safeString(form.getImei()));
//                            case "Blancco UUID" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
                            case "料件編號" -> row.put(header, ExcelDataConverter.safeString(form.getPartNumberCode()));
                            case "料件名稱" -> row.put(header, ExcelDataConverter.safeString(form.getPartNumberName()));
//                            case "複測人員" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "複測單位" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "複測公司" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "賣家姓名" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "賣家身分證字號" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "賣家聯絡電話" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "賣家聯絡地址" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "檢測群組編號" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "檢測群組名稱" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
//                            case "備註" -> row.put(header, ExcelDataConverter.safeString(""));  // 需要從其他地方獲取
                            case "建立者" -> row.put(header, ExcelDataConverter.safeString(form.getCreator()));
                            case "建立時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(form.getCreateDatetime(), zoneId));
                            case "最後修改者" -> row.put(header, ExcelDataConverter.safeString(form.getLastModifier()));
                            case "最後修改時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(form.getLastModifyDatetime(), zoneId));
                            default -> row.put(header, ""); // 對於未知的表頭，設置空值
                        }
                    }
                    return row;
                })
                .toList();

        // 生成 Excel
        return excelGenerator.generateDynamicHeaderExcel(
                ExcelGenerator.SheetTitle.INSPECTION_FORM_LIST.getTitle(),
                columnNames,
                data,
                ExcelGenerator.ExcelInfo.INSPECTION_FORM_LIST
        );
    }

    private Specification<InspectionFormEntity> applyDataScope(Specification<InspectionFormEntity> spec) {
        List<String> accessiblePartnerCodes = partnerAccessControlHelper.getAccessiblePartnerCodes();
        if (accessiblePartnerCodes.isEmpty()) {
            // 員工或無限制，不添加過濾
            return spec;
        }
        return spec.and((root, query, cb) -> root.get("partnerCode").in(accessiblePartnerCodes));
    }

    private void validateDataScopeAccess(InspectionFormEntity form) {
        List<String> accessiblePartnerCodes = partnerAccessControlHelper.getAccessiblePartnerCodes();
        if (accessiblePartnerCodes.isEmpty()) {
            // 員工或無限制，直接通過
            return;
        }
        if (!accessiblePartnerCodes.contains(form.getPartnerCode())) {
            throw new AccessDeniedException(
                    messageHelper.localizedMessage("error.datascope.access.denied", form.getInspectionFormId()));
        }
    }
}
