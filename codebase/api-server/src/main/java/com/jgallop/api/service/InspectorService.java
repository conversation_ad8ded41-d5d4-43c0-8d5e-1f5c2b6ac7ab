package com.jgallop.api.service;

import com.jgallop.api.entity.CompanyEntity;
import com.jgallop.api.entity.EmployeeEntity;
import com.jgallop.api.entity.PartnerPersonnelEntity;
import com.jgallop.api.entity.PartnerProfileEntity;
import com.jgallop.api.entity.enums.EmploymentStatus;
import com.jgallop.api.model.resp.RespInspectorDropdown;
import com.jgallop.api.repository.CompanyRepository;
import com.jgallop.api.repository.EmployeeRepository;
import com.jgallop.api.repository.PartnerPersonnelRepository;
import com.jgallop.api.repository.PartnerProfileRepository;
import lombok.RequiredArgsConstructor;
import com.jgallop.user.repository.UserRepository;
import com.jgallop.user.entity.UserEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 檢查員服務
 */
@Service
@RequiredArgsConstructor
public class InspectorService {

    private final EmployeeRepository employeeRepository;
    private final PartnerPersonnelRepository partnerPersonnelRepository;
    private final PartnerProfileRepository partnerProfileRepository;
    private final CompanyRepository companyRepository;
    private final PartnerHelper partnerHelper;
    private final UserRepository userRepository;

    /**
     * 根據供應商代碼查詢檢查員下拉選單
     *
     * @param partnerCode 供應商代碼（來自 Header X-Partner-Code）
     * @return 檢查員下拉選單列表
     */
    public List<RespInspectorDropdown> getInspectorDropdown(String partnerCode) {
        // 如果沒有供應商代碼或是創宇代碼，回傳直營門市員工
        if (!StringUtils.hasText(partnerCode) || partnerHelper.isSmDirectStore(partnerCode)) {
            return getDirectStoreEmployeeDropdown();
        }

        // 根據供應商代碼查詢供應商人員
        return getPartnerPersonnelDropdown(partnerCode);
    }

    /**
     * 查詢供應商人員下拉選單
     *
     * @param partnerCode 供應商代碼
     * @return 供應商人員下拉選單列表
     */
    private List<RespInspectorDropdown> getPartnerPersonnelDropdown(String partnerCode) {
        // 根據供應商代碼查詢供應商資料
        Optional<PartnerProfileEntity> partnerOpt = partnerProfileRepository.findByPartnerCode(partnerCode);
        if (partnerOpt.isEmpty()) {
            return Collections.emptyList();
        }

        String partnerId = partnerOpt.get().getPartnerId();

        // 查詢該供應商底下所有啟用且在職的人員
        List<PartnerPersonnelEntity> personnels = partnerPersonnelRepository
                .findByPartnerIdAndEnabledTrueAndEmploymentStatus(partnerId, EmploymentStatus.EMPLOYED);

        return personnels.stream()
                .map(this::convertPartnerPersonnelToDropdown)
                .toList();
    }

    /**
     * 查詢直營門市員工下拉選單
     *
     * @return 直營門市員工下拉選單列表
     */
    private List<RespInspectorDropdown> getDirectStoreEmployeeDropdown() {
        // 查詢所有直營門市
        List<CompanyEntity> directStores = companyRepository.findByIsDirectStoreIsTrue();
        List<String> directStoreIds = directStores.stream()
                .map(CompanyEntity::getCompanyId)
                .toList();

        if (directStoreIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查詢這些直營門市底下所有啟用且在職的員工
        List<EmployeeEntity> employees = employeeRepository
                .findByCompanyIdInAndEnabledTrueAndEmploymentStatus(directStoreIds, EmploymentStatus.EMPLOYED);

        return employees.stream()
                .map(this::convertEmployeeToDropdown)
                .toList();
    }

    /**
     * 將供應商人員實體轉換為下拉選單回應模型
     */
    private RespInspectorDropdown convertPartnerPersonnelToDropdown(PartnerPersonnelEntity entity) {
        RespInspectorDropdown dropdown = new RespInspectorDropdown();
        // 以 personnelId 反查 userId 作為 inspectorId
        String userId = userRepository.findByPersonnelId(entity.getPersonnelId())
                .map(UserEntity::getUserId)
                .orElse(null);
        dropdown.setInspectorId(userId);
        dropdown.setInspectorNumber(entity.getPersonnelNumber());
        dropdown.setZhName(entity.getPersonnelZhName());
        dropdown.setEnName(entity.getPersonnelEnName());
        dropdown.setInspectorType("PARTNER");
        return dropdown;
    }

    /**
     * 將員工實體轉換為下拉選單回應模型
     */
    private RespInspectorDropdown convertEmployeeToDropdown(EmployeeEntity entity) {
        RespInspectorDropdown dropdown = new RespInspectorDropdown();
        // 以 employeeId 反查 userId 作為 inspectorId
        String userId = userRepository.findByEmployeeId(entity.getEmployeeId())
                .map(UserEntity::getUserId)
                .orElse(null);
        dropdown.setInspectorId(userId);
        dropdown.setInspectorNumber(entity.getEmployeeNumber());
        dropdown.setZhName(entity.getZhName());
        dropdown.setEnName(entity.getEnName());
        dropdown.setInspectorType("EMPLOYEE");
        return dropdown;
    }
}
