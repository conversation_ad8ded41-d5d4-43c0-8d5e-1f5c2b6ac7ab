package com.jgallop.api.service;

import com.jgallop.api.exception.AccessTokenException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Base64;
import java.util.Map;

@Service
@Slf4j
public class OAuthCallbackService {

    private final WebClient webClient;

    @Value("${oauth2.basic.client-id}")
    private String clientId;

    @Value("${oauth2.basic.client-secret}")
    private String clientSecret;

    @Value("${oauth2.token.endpoint}")
    private String tokenEndpoint;

    public OAuthCallbackService(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 處理 OAuth 2.0 授權流程中的授權碼 (Authorization Code) callback 請求。
     * URL 應與你的 redirect URI 相匹配（授權伺服器會在授權完成後重定向到該 URI）
     *
     * @param code 接收授權伺服器返回的授權碼 (Authorization Code)
     */
    public Map<String, Object> exchangeAuthorizationCodeForAccessToken(String code, String redirectUri) throws IllegalArgumentException {
        // 設定 Basic Auth Header
        String basicAuthHeader = "Basic "
                + Base64.getEncoder().encodeToString(String.format("%s:%s", clientId, clientSecret).getBytes());

        // 發送 POST 請求並獲取 Access Token
        // @formatter:off
        var response = webClient.post()
                .uri(tokenEndpoint)
                .header(HttpHeaders.AUTHORIZATION, basicAuthHeader)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .bodyValue(UriComponentsBuilder.fromPath("")
                        .queryParam("grant_type", "authorization_code")
                        .queryParam("code", code)
                        .queryParam("redirect_uri", redirectUri)
                        .build()
                        .toUriString()
                        .substring(1)) // 移除前導的 "?"
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {})
                .onErrorMap(WebClientResponseException.class, ex -> {
                    /*
                     * 這裡會攔到所有的 WebClientResponseException (e.g. 400, 500, etc.)
                     * 把訊息記錄下來，回傳前端統一錯誤
                     */
                    log.error("ExchangeAuthorizationCodeForAccessToken WebClientResponseException: {}", ex.getResponseBodyAsString(), ex);
                    return new AccessTokenException(ex.getMessage());
                })
                .block();
        // @formatter:on

        return response;
    }

    /**
     * 使用 refresh token 換取新的 access token
     */
    public Map<String, Object> exchangeRefreshTokenForAccessToken(String refreshToken) throws IllegalArgumentException {
        // 設定 Basic Auth Header
        String basicAuthHeader = "Basic "
                + Base64.getEncoder().encodeToString(String.format("%s:%s", clientId, clientSecret).getBytes());

        // 發送 POST 請求並獲取 Access Token
        // @formatter:off
        var response = webClient.post()
                .uri(tokenEndpoint)
                .header(HttpHeaders.AUTHORIZATION, basicAuthHeader)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .bodyValue(UriComponentsBuilder.fromPath("")
                                   .queryParam("grant_type", "refresh_token")
                                   .queryParam("refresh_token", refreshToken)
                                   .build()
                                   .toUriString()
                                   .substring(1)) // 移除前導的 "?"
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<Map<String, Object>>() {})
                .onErrorMap(WebClientResponseException.class, ex -> {
                    /*
                     * 這裡會攔到所有的 WebClientResponseException (e.g. 400, 500, etc.)
                     * 把訊息記錄下來，回傳前端統一錯誤
                     */
                    log.error("ExchangeRefreshTokenForAccessToken WebClientResponseException: {}", ex.getResponseBodyAsString(), ex);
                    return new AccessTokenException(ex.getMessage());
                })
                .block();
        // @formatter:on

        return response;
    }
}
