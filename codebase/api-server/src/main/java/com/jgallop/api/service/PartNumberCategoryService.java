package com.jgallop.api.service;

import com.jgallop.api.entity.PartNumberBrandEntity;
import com.jgallop.api.entity.PartNumberCategoryBrandEntity;
import com.jgallop.api.entity.PartNumberCategoryBrandId;
import com.jgallop.api.entity.PartNumberCategoryEntity;
import com.jgallop.api.exception.ConflictException;
import com.jgallop.api.exception.ResourceNotFoundException;
import com.jgallop.api.model.req.ReqPartNumberCategoryCreate;
import com.jgallop.api.model.resp.RespBrandCategory;
import com.jgallop.api.model.resp.RespBrandCategoryWrapper;
import com.jgallop.api.model.resp.RespLevel0Category;
import com.jgallop.api.model.resp.RespLevel0CategoryWrapper;
import com.jgallop.api.model.resp.RespLevel1Category;
import com.jgallop.api.model.resp.RespLevel1CategoryWrapper;
import com.jgallop.api.model.resp.RespLevel2Category;
import com.jgallop.api.model.resp.RespLevel2CategoryWrapper;
import com.jgallop.api.model.resp.RespLevelCategory;
import com.jgallop.api.model.resp.RespLevelCategoryWrapper;
import com.jgallop.api.repository.PartNumberBrandRepository;
import com.jgallop.api.repository.PartNumberCategoryBrandRepository;
import com.jgallop.api.repository.PartNumberCategoryRepository;
import com.jgallop.user.service.UserHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 零件編號類別操作服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PartNumberCategoryService {

    private final UserHelper userHelper;
    private final PartNumberHelper partNumberHelper;
    private final PartNumberCategoryRepository partNumberCategoryRepository;
    private final PartNumberBrandRepository brandRepository;
    private final PartNumberCategoryBrandRepository categoryBrandRepository;

    /**
     * 通用方法：根據層級查詢類別
     *
     * @param level    層級 (0-3)
     * @param parentId 父類別ID (可選)
     * @return 指定層級的類別列表
     */
    private List<PartNumberCategoryEntity> queryCategoriesByLevel(Integer level, String parentId) {
        if (level < 0 || level > 3) {
            throw new IllegalArgumentException("層級必須在0-3之間");
        }

        if (parentId != null && !parentId.isEmpty()) {
            return partNumberCategoryRepository.findByLevelAndPartNumberCategoryParentId(level, parentId);
        } else {
            return partNumberCategoryRepository.findByLevel(level);
        }
    }

    /**
     * 查詢 level 0 類別
     *
     * @return 包含大分類數據的包裝器
     */
    public RespLevel0CategoryWrapper queryLevel0Categories() {
        // 查詢所有大分類 (level 0)
        List<PartNumberCategoryEntity> categories = queryCategoriesByLevel(0, null);

        // 轉換為 RespLevel0Category
        List<RespLevel0Category> respCategories = categories.stream()
                .map(entity -> RespLevel0Category.builder()
                        .level0Id(entity.getPartNumberCategoryId())
                        .level0Code(entity.getPartNumberCategoryCode())
                        .level0Name(entity.getPartNumberCategoryName())
                        .enabled(entity.getEnabled())
                        .build())
                .toList();

        return new RespLevel0CategoryWrapper(respCategories);
    }

    /**
     * 將 PartNumberCategoryEntity 映射到 RespLevelCategory
     *
     * @param entity 類別實體
     * @param level  層級 (0-3)
     * @return RespLevelCategory 回應模型
     */
    private RespLevelCategory mapToRespLevelCategory(PartNumberCategoryEntity entity, Integer level) {
        return RespLevelCategory.builder()
                .category("level" + level)
                .parentId(entity.getPartNumberCategoryParentId())
                .id(entity.getPartNumberCategoryId())
                .code(entity.getPartNumberCategoryCode())
                .name(entity.getPartNumberCategoryName())
                .enabled(entity.getEnabled())
                .build();
    }

    /**
     * 查詢指定層級的類別（使用新的回應模型）
     *
     * @param level 指定層級 (0-3)
     * @return 包含類別數據的包裝器
     */
    public RespLevelCategoryWrapper queryWithLevel(Integer level) {
        // 根據層級查詢啟用的類別
        List<PartNumberCategoryEntity> categories = queryCategoriesByLevel(level, null);

        // 轉換為新的回應模型
        List<RespLevelCategory> respCategories = categories.stream()
                .map(entity -> mapToRespLevelCategory(entity, level))
                .toList();

        return new RespLevelCategoryWrapper(respCategories);
    }

    /**
     * 創建新的零件編號類別
     *
     * @param request 包含類別數據的請求
     */
    @Transactional
    public void createPartNumberCategory(ReqPartNumberCategoryCreate request) {
        log.info("正在創建名稱為: {} 的新零件編號類別", request.getPartNumberCategoryName());

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查類別名稱是否已存在
        partNumberHelper.checkCategoryNameDuplicateOrElseThrow(request.getPartNumberCategoryName());

        // 檢查層級是否超出範圍 (0-3)
        if (request.getLevel() < 0 || request.getLevel() > 3) {
            throw new IllegalArgumentException("類別層級必須在0-3之間");
        }

        // 檢查類別代碼在同一層級下是否重複
        String parentId = (request.getPartNumberCategoryParentId() == null ||
                          request.getPartNumberCategoryParentId().isBlank() ||
                          "0".equals(request.getPartNumberCategoryParentId())) ? "0" : request.getPartNumberCategoryParentId();
        partNumberHelper.checkCategoryCodeDuplicateOrElseThrow(request.getPartNumberCategoryCode(), parentId);

        // 創建新的類別實體
        PartNumberCategoryEntity newCategory = new PartNumberCategoryEntity();

        // 處理父類別ID邏輯
        if (request.getPartNumberCategoryParentId() == null ||
                request.getPartNumberCategoryParentId().isBlank() ||
                "0".equals(request.getPartNumberCategoryParentId())) {

            // 設為大分類 (level 0)，父ID為"0"
            newCategory.setPartNumberCategoryParentId("0");

            // 檢查請求的層級是否為0
            partNumberHelper.checkCategoryLevelValidOrElseThrow(request.getLevel(), 0);
        } else {
            // 驗證父子關係的有效性（包含循環檢測）
            partNumberHelper.validatePartNumberCategoryParentRelationship(null, parentId);

            newCategory.setPartNumberCategoryParentId(parentId);

            // 查找父類別
            PartNumberCategoryEntity parent = partNumberHelper.findCategoryByIdOrElseThrow(parentId);

            // 檢查父類別的層級是否為當前層級的上一層
            int expectedParentLevel = request.getLevel() - 1;
            partNumberHelper.checkParentCategoryLevelValidOrElseThrow(parent.getLevel(), expectedParentLevel);

            // 檢查同一個父類別下是否有相同的代碼
            partNumberHelper.checkCategoryCodeDuplicateOrElseThrow(
                    request.getPartNumberCategoryCode(),
                    request.getPartNumberCategoryParentId()
            );
        }

        newCategory.setPartNumberCategoryName(request.getPartNumberCategoryName());
        newCategory.setPartNumberCategoryCode(request.getPartNumberCategoryCode());
        newCategory.setLevel(request.getLevel());
        newCategory.setCreator(loginUserId);
        newCategory.setCreateDatetime(System.currentTimeMillis());

        partNumberCategoryRepository.saveAndFlush(newCategory);
    }

    /**
     * 禁用零件編號類別及其子類別
     *
     * @param categoryId 要禁用的類別ID
     */
    public void disablePartNumberCategory(String categoryId) {
        log.info("正在禁用ID為: {} 的零件編號類別", categoryId);

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查類別是否存在
        PartNumberCategoryEntity category = partNumberHelper.findCategoryByIdOrElseThrow(categoryId);

        // 檢查類別是否已被禁用
        partNumberHelper.checkCategoryNotDisabledOrElseThrow(category, categoryId);

        // 禁用該類別
        category.setEnabled(Boolean.FALSE);
        category.setLastModifier(loginUserId);
        category.setLastModifyDatetime(System.currentTimeMillis());
        partNumberCategoryRepository.saveAndFlush(category);

        // 查找並禁用所有子類別
        disableChildCategories(category.getPartNumberCategoryId(), loginUserId);

        log.info("成功禁用ID為: {} 的零件編號類別及其子類別", categoryId);
    }

    /**
     * 遞歸禁用所有子類別
     *
     * @param parentId    父類別ID
     * @param loginUserId 執行操作的用戶ID
     */
    private void disableChildCategories(String parentId, String loginUserId) {
        List<PartNumberCategoryEntity> childCategories = partNumberCategoryRepository.findByPartNumberCategoryParentId(parentId);

        for (PartNumberCategoryEntity child : childCategories) {
            // 禁用子類別
            child.setEnabled(Boolean.FALSE);
            child.setLastModifier(loginUserId);
            child.setLastModifyDatetime(System.currentTimeMillis());
            partNumberCategoryRepository.saveAndFlush(child);

            // 遞歸禁用其子類別
            disableChildCategories(child.getPartNumberCategoryId(), loginUserId);
        }
    }

    /**
     * 創建類別映射，用於快速查找
     *
     * @return 類別ID到類別實體的映射
     */
    private Map<String, PartNumberCategoryEntity> createCategoryMap() {
        List<PartNumberCategoryEntity> allCategories = partNumberCategoryRepository.findAll();
        return allCategories.stream()
                .collect(Collectors.toMap(
                        PartNumberCategoryEntity::getPartNumberCategoryId,
                        category -> category,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 查詢 level 1 類別，可選擇指定大分類ID進行篩選
     *
     * @param level0Id 大分類ID (可選)
     * @return 包含中分類數據的包裝器
     */
    public RespLevel1CategoryWrapper queryLevel1Categories(String level0Id) {
        // 使用通用方法查詢中分類
        List<PartNumberCategoryEntity> categories = queryCategoriesByLevel(1, level0Id);

        // 獲取類別映射用於查找父類別
        Map<String, PartNumberCategoryEntity> categoryMap = createCategoryMap();

        List<RespLevel1Category> respCategories = categories.stream()
                .map(entity -> {
                    // 獲取父類別 (level 0)
                    PartNumberCategoryEntity parent = categoryMap.get(entity.getPartNumberCategoryParentId());

                    return RespLevel1Category.builder()
                            .level0Id(parent.getPartNumberCategoryId())
                            .level0Code(parent.getPartNumberCategoryCode())
                            .level0Name(parent.getPartNumberCategoryName())
                            .level1Id(entity.getPartNumberCategoryId())
                            .level1Code(entity.getPartNumberCategoryCode())
                            .level1Name(entity.getPartNumberCategoryName())
                            .enabled(entity.getEnabled())
                            .build();
                })
                .toList();

        return new RespLevel1CategoryWrapper(respCategories);
    }

    /**
     * 查詢指定父類別下的所有子類別
     *
     * @param parentIds 父類別ID列表
     * @return 子類別列表
     */
    private List<PartNumberCategoryEntity> queryChildCategoriesByParentIds(List<String> parentIds) {
        List<PartNumberCategoryEntity> childCategories = new ArrayList<>();
        for (String parentId : parentIds) {
            childCategories.addAll(queryCategoriesByLevel(2, parentId));
        }
        return childCategories;
    }

    /**
     * 查詢 level 2 類別，可選擇指定大分類ID和中分類ID進行篩選
     *
     * @param level0Id 大分類ID (可選)
     * @param level1Id 中分類ID (可選)
     * @return 小分類列表
     */
    private List<PartNumberCategoryEntity> findLevel2Categories(String level0Id, String level1Id) {
        if (level1Id != null && !level1Id.isEmpty()) {
            // 如果指定了中分類ID，查詢該中分類下的所有小分類
            return queryCategoriesByLevel(2, level1Id);
        } else if (level0Id != null && !level0Id.isEmpty()) {
            // 如果只指定了大分類ID，查詢該大分類下所有中分類的所有小分類
            List<PartNumberCategoryEntity> level1Categories = queryCategoriesByLevel(1, level0Id);
            List<String> level1Ids = level1Categories.stream()
                    .map(PartNumberCategoryEntity::getPartNumberCategoryId)
                    .toList();

            return queryChildCategoriesByParentIds(level1Ids);
        } else {
            // 否則查詢所有小分類
            return queryCategoriesByLevel(2, null);
        }
    }

    /**
     * 查詢 level 2 類別，可選擇指定大分類ID和中分類ID進行篩選
     *
     * @param level0Id 大分類ID (可選)
     * @param level1Id 中分類ID (可選)
     * @return 包含小分類數據的包裝器
     */
    public RespLevel2CategoryWrapper queryLevel2Categories(String level0Id, String level1Id) {
        List<PartNumberCategoryEntity> categories = findLevel2Categories(level0Id, level1Id);

        // 獲取類別映射用於查找父類別
        Map<String, PartNumberCategoryEntity> categoryMap = createCategoryMap();

        List<RespLevel2Category> respCategories = categories.stream()
                .map(entity -> {
                    // 獲取父類別 (level 1)
                    PartNumberCategoryEntity parent = categoryMap.get(entity.getPartNumberCategoryParentId());
                    // 獲取祖父類別 (level 0)
                    PartNumberCategoryEntity grandparent = categoryMap.get(parent.getPartNumberCategoryParentId());

                    return RespLevel2Category.builder()
                            .level0Id(grandparent.getPartNumberCategoryId())
                            .level0Code(grandparent.getPartNumberCategoryCode())
                            .level0Name(grandparent.getPartNumberCategoryName())
                            .level1Id(parent.getPartNumberCategoryId())
                            .level1Code(parent.getPartNumberCategoryCode())
                            .level1Name(parent.getPartNumberCategoryName())
                            .level2Id(entity.getPartNumberCategoryId())
                            .level2Code(entity.getPartNumberCategoryCode())
                            .level2Name(entity.getPartNumberCategoryName())
                            .enabled(entity.getEnabled())
                            .build();
                })
                .toList();

        return new RespLevel2CategoryWrapper(respCategories);
    }

    /**
     * 查詢小分類ID列表
     *
     * @param level0Id 大分類ID (可選)
     * @param level1Id 中分類ID (可選)
     * @return 小分類ID列表
     */
    private List<String> findLevel2Ids(String level0Id, String level1Id) {
        // 使用通用方法查詢小分類
        List<PartNumberCategoryEntity> level2Categories = findLevel2Categories(level0Id, level1Id);

        // 獲取所有小分類的ID
        return level2Categories.stream()
                .map(PartNumberCategoryEntity::getPartNumberCategoryId)
                .toList();
    }

    /**
     * 查詢品牌，可選擇指定大分類ID、中分類ID進行篩選（使用新的回應模型）
     *
     * @param level0Id 大分類ID (可選)
     * @param level1Id 中分類ID (可選)
     * @return 品牌列表包裝器
     */
    public RespBrandCategoryWrapper queryBrandsWithResp(String level0Id, String level1Id) {
        // 獲取小分類ID列表
        List<String> level2Ids = findLevel2Ids(level0Id, level1Id);

        // 查詢這些小分類關聯的所有品牌
        List<RespBrandCategory> allBrands = new ArrayList<>();
        for (String categoryId : level2Ids) {
            RespBrandCategoryWrapper wrapper = getCategoryBrandsWithResp(categoryId);
            allBrands.addAll(wrapper.getBrands());
        }

        return new RespBrandCategoryWrapper(allBrands);
    }

    /**
     * 檢查類別是否為小分類 (level 2)
     *
     * @param categoryId 類別ID
     * @return 小分類實體
     * @throws ResourceNotFoundException 如果類別不存在
     * @throws IllegalArgumentException  如果類別不是小分類
     */
    private PartNumberCategoryEntity checkAndGetLevel2Category(String categoryId) {
        // 檢查小分類是否存在
        PartNumberCategoryEntity category = partNumberHelper.findCategoryByIdOrElseThrow(categoryId);

        // 檢查是否為小分類 (level 2)
        if (category.getLevel() != 2) {
            throw new IllegalArgumentException("只能查詢小分類 (level 2) 關聯的品牌");
        }

        return category;
    }

    /**
     * 查詢小分類關聯的品牌列表（使用新的回應模型）
     *
     * @param categoryId 小分類ID
     * @return 品牌列表包裝器
     */
    public RespBrandCategoryWrapper getCategoryBrandsWithResp(String categoryId) {
        // 檢查並獲取小分類
        PartNumberCategoryEntity category = checkAndGetLevel2Category(categoryId);

        // 獲取類別映射用於查找父類別
        Map<String, PartNumberCategoryEntity> categoryMap = createCategoryMap();

        // 獲取父類別 (level 1)
        PartNumberCategoryEntity parent = categoryMap.get(category.getPartNumberCategoryParentId());
        // 獲取祖父類別 (level 0)
        PartNumberCategoryEntity grandparent = categoryMap.get(parent.getPartNumberCategoryParentId());

        // 查詢關聯的品牌
        List<PartNumberCategoryBrandEntity> relations = categoryBrandRepository.findById_CategoryIdAndEnabledTrue(categoryId);

        // 轉換為新的回應模型
        List<RespBrandCategory> respBrands = relations.stream()
                .map(relation -> {
                    PartNumberBrandEntity brand = brandRepository.findById(relation.getId().getBrandId())
                            .orElse(null);
                    if (brand != null) {
                        return RespBrandCategory.builder()
                                .level0Id(grandparent.getPartNumberCategoryId())
                                .level0Code(grandparent.getPartNumberCategoryCode())
                                .level0Name(grandparent.getPartNumberCategoryName())
                                .level1Id(parent.getPartNumberCategoryId())
                                .level1Code(parent.getPartNumberCategoryCode())
                                .level1Name(parent.getPartNumberCategoryName())
                                .level2Id(category.getPartNumberCategoryId())
                                .level2Code(category.getPartNumberCategoryCode())
                                .level2Name(category.getPartNumberCategoryName())
                                .brandId(brand.getBrandId())
                                .brandCode(brand.getBrandCode())
                                .brandName(brand.getBrandName())
                                .categoryBrandEnabled(relation.getEnabled())
                                .brandEnabled(brand.getEnabled())
                                .categoryEnabled(category.getEnabled())
                                .build();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new RespBrandCategoryWrapper(respBrands);
    }

    // ===== Dropdown (enabled only) =====

    /**
     * 取得所有啟用的大分類（下拉選單）
     */
    public List<RespLevel0Category> getEnabledLevel0ForDropdown() {
        List<PartNumberCategoryEntity> categories = partNumberCategoryRepository.findByLevelAndEnabledTrue(0);
        return categories.stream()
                .map(entity -> RespLevel0Category.builder()
                        .level0Id(entity.getPartNumberCategoryId())
                        .level0Code(entity.getPartNumberCategoryCode())
                        .level0Name(entity.getPartNumberCategoryName())
                        .enabled(entity.getEnabled())
                        .build())
                .toList();
    }

    /**
     * 取得啟用的中分類（可指定大分類）
     */
    public List<RespLevel1Category> getEnabledLevel1ForDropdown(String level0Id) {
        List<PartNumberCategoryEntity> categories;
        if (level0Id != null && !level0Id.isEmpty()) {
            categories = partNumberCategoryRepository.findByLevelAndPartNumberCategoryParentIdAndEnabledTrue(1, level0Id);
        } else {
            categories = partNumberCategoryRepository.findByLevelAndEnabledTrue(1);
        }

        Map<String, PartNumberCategoryEntity> categoryMap = createCategoryMap();
        return categories.stream()
                .map(entity -> {
                    PartNumberCategoryEntity parent = categoryMap.get(entity.getPartNumberCategoryParentId());
                    return RespLevel1Category.builder()
                            .level0Id(parent.getPartNumberCategoryId())
                            .level0Code(parent.getPartNumberCategoryCode())
                            .level0Name(parent.getPartNumberCategoryName())
                            .level1Id(entity.getPartNumberCategoryId())
                            .level1Code(entity.getPartNumberCategoryCode())
                            .level1Name(entity.getPartNumberCategoryName())
                            .enabled(entity.getEnabled())
                            .build();
                })
                .toList();
    }

    /**
     * 取得啟用的小分類（可指定大/中分類）
     */
    public List<RespLevel2Category> getEnabledLevel2ForDropdown(String level0Id, String level1Id) {
        List<PartNumberCategoryEntity> categories;
        if (level1Id != null && !level1Id.isEmpty()) {
            categories = partNumberCategoryRepository.findByLevelAndPartNumberCategoryParentIdAndEnabledTrue(2, level1Id);
        } else if (level0Id != null && !level0Id.isEmpty()) {
            // find all level1 under level0 (enabled), then level2 enabled under them
            List<PartNumberCategoryEntity> level1Categories = partNumberCategoryRepository.findByLevelAndPartNumberCategoryParentIdAndEnabledTrue(1, level0Id);
            List<String> level1Ids = level1Categories.stream().map(PartNumberCategoryEntity::getPartNumberCategoryId).toList();
            categories = queryChildCategoriesByParentIds(level1Ids).stream()
                    .filter(PartNumberCategoryEntity::getEnabled)
                    .toList();
        } else {
            categories = partNumberCategoryRepository.findByLevelAndEnabledTrue(2);
        }

        Map<String, PartNumberCategoryEntity> categoryMap = createCategoryMap();
        return categories.stream()
                .map(entity -> {
                    PartNumberCategoryEntity parent = categoryMap.get(entity.getPartNumberCategoryParentId());
                    PartNumberCategoryEntity grandparent = categoryMap.get(parent.getPartNumberCategoryParentId());
                    return RespLevel2Category.builder()
                            .level0Id(grandparent.getPartNumberCategoryId())
                            .level0Code(grandparent.getPartNumberCategoryCode())
                            .level0Name(grandparent.getPartNumberCategoryName())
                            .level1Id(parent.getPartNumberCategoryId())
                            .level1Code(parent.getPartNumberCategoryCode())
                            .level1Name(parent.getPartNumberCategoryName())
                            .level2Id(entity.getPartNumberCategoryId())
                            .level2Code(entity.getPartNumberCategoryCode())
                            .level2Name(entity.getPartNumberCategoryName())
                            .enabled(entity.getEnabled())
                            .build();
                })
                .toList();
    }

    /**
     * 添加小分類與品牌的關聯
     *
     * @param categoryId 小分類ID
     * @param brandId    品牌ID
     */
    @Transactional
    public void addBrandToCategory(String categoryId, String brandId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查小分類是否存在
        PartNumberCategoryEntity category = partNumberHelper.findCategoryByIdOrElseThrow(categoryId);

        // 檢查是否為小分類 (level 2)
        partNumberHelper.checkCategoryIsLevel2OrElseThrow(category, categoryId);

        // 檢查品牌是否存在
        brandRepository.findById(brandId).orElseThrow(() -> new ResourceNotFoundException("找不到ID為 " + brandId + " 的品牌"));

        PartNumberCategoryBrandId id = new PartNumberCategoryBrandId(categoryId, brandId);
        // 檢查關聯是否已存在
        if (categoryBrandRepository.existsById(id)) {
            throw new ConflictException("小分類與品牌的關聯已存在");
        }
        // 創建新的關聯
        PartNumberCategoryBrandEntity relation = new PartNumberCategoryBrandEntity();
        relation.setId(id);
        relation.setEnabled(true);
        relation.setCreator(loginUserId);
        relation.setCreateDatetime(System.currentTimeMillis());

        categoryBrandRepository.save(relation);

        log.info("成功添加小分類 {} 與品牌 {} 的關聯", categoryId, brandId);
    }

    /**
     * 停用類別與品牌關聯
     *
     * @param categoryId 小分類ID
     * @param brandId    品牌ID
     */
    @Transactional
    public void disableCategoryBrandRelation(String categoryId, String brandId) {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查小分類是否存在
        partNumberHelper.findCategoryByIdOrElseThrow(categoryId);

        // 檢查品牌是否存在
        brandRepository.findById(brandId)
                .orElseThrow(() -> new ResourceNotFoundException("找不到ID為 " + brandId + " 的品牌"));
        PartNumberCategoryBrandId id = new PartNumberCategoryBrandId(categoryId, brandId);
        // 查找關聯
        PartNumberCategoryBrandEntity relation = categoryBrandRepository.findByIdAndEnabledTrue(id)
                .orElseThrow(() -> new ResourceNotFoundException("找不到小分類與品牌的關聯"));

        // 禁用關聯
        relation.setEnabled(false);
        relation.setLastModifier(loginUserId);
        relation.setLastModifyDatetime(System.currentTimeMillis());

        categoryBrandRepository.save(relation);

        log.info("成功停用小分類 {} 與品牌 {} 的關聯", categoryId, brandId);
    }
}
