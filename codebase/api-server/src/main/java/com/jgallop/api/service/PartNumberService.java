package com.jgallop.api.service;

import com.jgallop.api.entity.*;
import com.jgallop.api.exception.ConflictException;
import com.jgallop.api.exception.ResourceNotFoundException;
import com.jgallop.api.model.req.ReqPartNumberDisable;
import com.jgallop.api.model.req.ReqPartNumberProfileCreate;
import com.jgallop.api.model.req.ReqPartNumberProfileUpdate;
import com.jgallop.api.model.resp.*;
import com.jgallop.api.repository.*;
import com.jgallop.common.model.audit.AuditLogEntity;
import com.jgallop.common.service.audit.AuditLogService;
import com.jgallop.api.util.ExcelDataConverter;

import com.jgallop.hfs.entity.postgres.FileEntity;
import com.jgallop.hfs.service.FileManagementHelper;
import com.jgallop.user.service.UserHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import jakarta.persistence.criteria.Predicate;

import java.util.ArrayList;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 零件編號配置操作服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PartNumberService {

    private final UserHelper userHelper;
    private final FileHelper fileHelper;
    private final ExcelGenerator excelGenerator;
    private final PartNumberHelper partNumberHelper;
    private final ProductSeriesHelper productSeriesHelper;
    private final FileManagementHelper fileManagementHelper;
    private final AuditLogService auditLogService;
    private final EolReasonHelper eolReasonHelper;
    private final SystemAccountHelper systemAccountHelper;

    private final PartNumberRepository partNumberRepository;
    private final PartNumberImageRepository partNumberImageRepository;
    private final PartNumberCategoryRepository partNumberCategoryRepository;
    private final PartNumberCategoryBrandRepository partNumberCategoryBrandRepository;



    /**
     * 使用過濾條件查詢料件基本資料
     *
     * @param customPartIdentifier 料件識別碼
     * @param partNumberCode       料件編號
     * @param partNumberName       料件名稱
     * @param productSeriesId      產品系列ID
     * @param specification        規格
     * @param partNumberCategoryId 料件類別ID
     * @param partNumberNote       備註
     * @param isInUse              是否使用中
     * @param brandId              品牌ID
     * @param page                 分頁頁碼，從 1 開始
     * @param size                 每頁筆數
     * @return 查詢結果包裝器
     */
    public RespPartNumberProfileQueryWrapper queryPartNumberProfiles(String customPartIdentifier,
                                                                     String partNumberCode,
                                                                     String partNumberName,
                                                                     String productSeriesId,
                                                                     String specification,
                                                                     String partNumberCategoryId,
                                                                     String partNumberNote,
                                                                     Boolean isInUse,
                                                                     String brandId,
                                                                     int page,
                                                                     int size) {

        Specification<PartNumberEntity> spec = Specification.where(null);

        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "customPartIdentifier", customPartIdentifier);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partNumberCode", partNumberCode);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partNumberName", partNumberName);
        spec = SpecificationHelper.addEqualSpecificationForString(spec, "productSeriesId", productSeriesId);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "specification", specification);

        // 處理 partNumberCategoryId 參數，支援 level 0 和 level 1
        if (partNumberCategoryId != null && !partNumberCategoryId.isEmpty()) {
            List<String> level2CategoryIds = partNumberHelper.findAllLevel2CategoryIdsUnderCategory(partNumberCategoryId);
            if (!level2CategoryIds.isEmpty()) {
                spec = SpecificationHelper.addInSpecificationForStringList(spec, "partNumberCategoryId", level2CategoryIds);
            } else {
                // 如果找不到任何 level 2 類別，使用一個不存在的 ID 確保查詢結果為空
                spec = SpecificationHelper.addEqualSpecificationForString(spec, "partNumberCategoryId", "NON_EXISTENT_ID");
            }
        }

        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "note", partNumberNote);
        spec = SpecificationHelper.addEqualSpecificationForBoolean(spec, "isInUse", isInUse);

        // 處理 brandId 參數 (需要通過 join 查詢)
        if (brandId != null && !brandId.isEmpty()) {
            spec = spec.and((root, query, criteriaBuilder) -> {
                // 使用子查詢來查找產品系列ID屬於指定品牌的料件
                assert query != null;
                var subquery = query.subquery(String.class);
                var subRoot = subquery.from(PartNumberProductSeriesEntity.class);
                subquery.select(subRoot.get("productSeriesId"))
                        .where(criteriaBuilder.equal(subRoot.get("brandId"), brandId));

                return root.get("productSeriesId").in(subquery);
            });
        }

        var pageable = PageHelper.getPageableOrderByParam(page, size);
        var resultPage = partNumberRepository.findAll(spec, pageable);
        var models = resultPage.getContent().stream().map(this::mapToRespPartNumberProfileQuery).toList();

        return new RespPartNumberProfileQueryWrapper(resultPage.getTotalElements(), models);
    }

    /**
     * 將 PartNumberEntity 映射到 RespPartNumberProfileQuery
     *
     * @param entity PartNumberEntity 實體
     * @return RespPartNumberProfileQuery 響應對象
     */
    private RespPartNumberProfileQuery mapToRespPartNumberProfileQuery(PartNumberEntity entity) {
        RespPartNumberProfileQuery model = new RespPartNumberProfileQuery();

        // 設置料件ID
        model.setPartNumberId(entity.getPartNumberId());

        // 設置料件識別碼
        model.setCustomPartIdentifier(entity.getCustomPartIdentifier());

        // 設置料件類別相關資訊
        model.setPartNumberCategoryId(entity.getPartNumberCategoryId());

        // 設置料件類別名稱 (level 0, 1, 2)
        if (entity.getPartNumberCategoryId() != null) {
            try {
                // 嘗試獲取小類別 (level 2)
                Optional<PartNumberCategoryEntity> level2CategoryOpt = partNumberCategoryRepository.findById(entity.getPartNumberCategoryId());
                if (level2CategoryOpt.isPresent()) {
                    // 小類別存在，設置名稱
                    PartNumberCategoryEntity level2Category = level2CategoryOpt.get();
                    model.setPartNumberCategoryLevel2Name(level2Category.getPartNumberCategoryName());

                    // 嘗試獲取中類別 (level 1)
                    String level1CategoryId = level2Category.getPartNumberCategoryParentId();
                    if (level1CategoryId != null) {
                        Optional<PartNumberCategoryEntity> level1CategoryOpt = partNumberCategoryRepository.findById(level1CategoryId);
                        if (level1CategoryOpt.isPresent()) {
                            // 中類別存在，設置名稱
                            PartNumberCategoryEntity level1Category = level1CategoryOpt.get();
                            model.setPartNumberCategoryLevel1Name(level1Category.getPartNumberCategoryName());

                            // 嘗試獲取大類別 (level 0)
                            String level0CategoryId = level1Category.getPartNumberCategoryParentId();
                            if (level0CategoryId != null) {
                                Optional<PartNumberCategoryEntity> level0CategoryOpt = partNumberCategoryRepository.findById(level0CategoryId);
                                if (level0CategoryOpt.isPresent()) {
                                    // 大類別存在，設置名稱
                                    PartNumberCategoryEntity level0Category = level0CategoryOpt.get();
                                    model.setPartNumberCategoryLevel0Name(level0Category.getPartNumberCategoryName());
                                } else {
                                    // 大類別不存在，設置默認值
                                    model.setPartNumberCategoryLevel0Name("未知大類別");
                                }
                            } else {
                                // 大類別ID為空，設置默認值
                                model.setPartNumberCategoryLevel0Name("未知大類別");
                            }
                        } else {
                            // 中類別不存在，設置默認值
                            model.setPartNumberCategoryLevel1Name("未知中類別");
                            model.setPartNumberCategoryLevel0Name("未知大類別");
                        }
                    } else {
                        // 中類別ID為空，設置默認值
                        model.setPartNumberCategoryLevel1Name("未知中類別");
                        model.setPartNumberCategoryLevel0Name("未知大類別");
                    }
                } else {
                    // 小類別不存在，設置所有類別為默認值
                    model.setPartNumberCategoryLevel2Name("未知小類別");
                    model.setPartNumberCategoryLevel1Name("未知中類別");
                    model.setPartNumberCategoryLevel0Name("未知大類別");
                }
            } catch (Exception e) {
                // 捕獲任何異常，記錄錯誤但繼續處理
                log.error("獲取料件類別信息時發生錯誤，料件ID: {}, 錯誤信息: {}", entity.getPartNumberId(), e.getMessage());
                // 設置默認值
                model.setPartNumberCategoryLevel2Name("類別獲取錯誤");
                model.setPartNumberCategoryLevel1Name("類別獲取錯誤");
                model.setPartNumberCategoryLevel0Name("類別獲取錯誤");
            }
        }

        // 設置料件基本資訊
        model.setPartNumberCode(entity.getPartNumberCode());
        model.setPartNumberName(entity.getPartNumberName());
        model.setSpecification(entity.getSpecification());
        model.setSafetyStockLevel(entity.getSafetyStockLevel());
        model.setPartNumberNote(entity.getNote());
        model.setIsInUse(entity.getIsInUse());

        // 設置產品系列和品牌相關資訊
        model.setProductSeriesId(entity.getProductSeriesId());
        model.setProductSeriesName(productSeriesHelper.findProductSeriesNameByProductSeriesId(entity.getProductSeriesId()));
        model.setBrandName(productSeriesHelper.findBrandNameByProductSeriesId(entity.getProductSeriesId()));
        model.setBrandId(productSeriesHelper.findBrandIdByProductSeriesId(entity.getProductSeriesId()));

        // 設置 Blancco 型號編號
        model.setBlanccoModelNumber(entity.getBlanccoModelNumber());

        // 設置啟用狀態
        model.setEnabled(entity.getEnabled());

        return model;
    }

    /**
     * 創建新的零件編號配置
     *
     * @param reqModel 包含零件編號配置數據的請求模型
     * @return 新創建的料件 ID
     */
    @Transactional
    public String createPartNumberProfile(ReqPartNumberProfileCreate reqModel) {
        log.info("Creating new part number profile with name: {}", reqModel.getPartNumberName());

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 自動產生料件編號
        String partNumberCode = partNumberHelper.generatePartNumberCode();
        log.info("Generated part number code: {} for part: {}", partNumberCode, reqModel.getPartNumberName());

        // 驗證料件類別是否存在且啟用
        PartNumberCategoryEntity category = partNumberHelper.findCategoryByIdOrElseThrow(reqModel.getPartNumberCategoryId());
        partNumberHelper.checkCategoryNotDisabledOrElseThrow(category, reqModel.getPartNumberCategoryId());

        // 驗證料件類別是否為小分類 (level 2)
        partNumberHelper.checkCategoryIsLevel2OrElseThrow(category, reqModel.getPartNumberCategoryId());

        // 驗證產品系列是否存在且啟用
        PartNumberProductSeriesEntity productSeries = productSeriesHelper.findProductSeriesByIdOrElseThrow(reqModel.getProductSeriesId());
        productSeriesHelper.checkProductSeriesEnabledOrElseThrow(productSeries, reqModel.getProductSeriesId());

        PartNumberEntity entity = createPartNumberProfileAndSave(reqModel, partNumberCode, loginUserId);
        return entity.getPartNumberId();
    }

    /**
     * 創建並保存料件基本資料
     *
     * @param reqModel 請求模型
     * @param partNumberCode 自動產生的料件編號
     * @param loginUserId 登入用戶 ID
     * @return 創建的料件實體
     */
    private PartNumberEntity createPartNumberProfileAndSave(ReqPartNumberProfileCreate reqModel, String partNumberCode, String loginUserId) {
        PartNumberEntity entity = new PartNumberEntity();
        entity.setPartNumberCategoryId(reqModel.getPartNumberCategoryId());
        entity.setProductSeriesId(reqModel.getProductSeriesId());
        entity.setPartNumberCode(partNumberCode);
        entity.setPartNumberName(reqModel.getPartNumberName());
        entity.setSpecification(reqModel.getSpecification());
        entity.setSafetyStockLevel(reqModel.getSafetyStockLevel());
        entity.setIsInUse(reqModel.getIsInUse());
        entity.setNote(reqModel.getPartNumberNote());
        entity.setBlanccoModelNumber(reqModel.getBlanccoModelNumber());

        // 生成並設置料件識別碼
        String customPartIdentifier = partNumberHelper.generateCustomPartIdentifier(
                reqModel.getPartNumberCategoryId(),
                reqModel.getProductSeriesId()
        );
        entity.setCustomPartIdentifier(customPartIdentifier);

        entity.setCreator(loginUserId);
        entity.setCreateDatetime(System.currentTimeMillis());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        return partNumberRepository.saveAndFlush(entity);
    }

    /**
     * Update an existing part number profile
     *
     * @param partNumberId The ID of the part number profile to update
     * @param reqModel     The reqModel containing part number profile data to update
     */
    @Transactional
    public void updatePartNumberProfile(String partNumberId, ReqPartNumberProfileUpdate reqModel) {
        log.info("Updating part number profile with ID: {}", partNumberId);

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        updatePartNumberProfileAndSave(partNumberId, reqModel, loginUserId);
    }

    /**
     * 根據產品系列ID取得所有料件（下拉選單）
     *
     * @param productSeriesId 產品系列ID
     * @return 料件列表
     */
    public List<RespPartNumberDropdown> getPartNumbersByProductSeriesForDropdown(String productSeriesId) {
        log.info("根據產品系列ID: {} 取得所有料件（下拉選單）", productSeriesId);

        Specification<PartNumberEntity> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            predicates.add(cb.equal(root.get("productSeriesId"), productSeriesId));
            predicates.add(cb.equal(root.get("enabled"), Boolean.TRUE));
            return cb.and(predicates.toArray(new Predicate[0]));
        };

        List<PartNumberEntity> partNumbers = partNumberRepository.findAll(spec);

        return partNumbers.stream()
                .map(this::mapToRespPartNumberDropdown)
                .toList();
    }

    /**
     * 將 PartNumberEntity 映射到 RespPartNumberDropdown
     *
     * @param entity PartNumberEntity 實體
     * @return RespPartNumberDropdown 響應對象
     */
    private RespPartNumberDropdown mapToRespPartNumberDropdown(PartNumberEntity entity) {
        RespPartNumberDropdown model = new RespPartNumberDropdown();
        model.setPartNumberId(entity.getPartNumberId());
        model.setPartNumberCode(entity.getPartNumberCode());
        model.setPartNumberName(entity.getPartNumberName());
        return model;
    }

    private void updatePartNumberProfileAndSave(String partNumberId, ReqPartNumberProfileUpdate reqModel, String loginUserId) {
        PartNumberEntity entity = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 只更新請求中包含的欄位
        entity.setPartNumberName(reqModel.getPartNumberName());
        entity.setSpecification(reqModel.getSpecification());
        entity.setSafetyStockLevel(reqModel.getSafetyStockLevel());
        entity.setIsInUse(reqModel.getIsInUse());
        entity.setNote(reqModel.getPartNumberNote());
        entity.setBlanccoModelNumber(reqModel.getBlanccoModelNumber());

        // 更新審計欄位
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        partNumberRepository.saveAndFlush(entity);
    }


    /**
     * 根據ID獲取料件基本資料
     *
     * @param partNumberId 料件ID
     * @return 料件基本資料
     */
    public RespPartNumberProfileQuery getPartNumberProfileById(String partNumberId) {
        PartNumberEntity entity = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);
        return mapToRespPartNumberProfileQuery(entity);
    }

    /**
     * 將料件基本資料查詢結果導出為Excel，支援動態表頭
     *
     * @param headers              前端傳入的動態表頭列表
     * @param customPartIdentifier 料件識別碼
     * @param partNumberCode       料件編號
     * @param partNumberName       料件名稱
     * @param productSeriesId      產品系列ID
     * @param specification        規格
     * @param partNumberCategoryId 料件類別ID
     * @param partNumberNote       備註
     * @param isInUse              是否使用中
     * @param brandId              品牌ID
     * @return Excel文件的字節數組
     */
    public byte[] exportPartNumberProfilesToExcel(List<String> headers,
                                                  String customPartIdentifier,
                                                  String partNumberCode,
                                                  String partNumberName,
                                                  String productSeriesId,
                                                  String specification,
                                                  String partNumberCategoryId,
                                                  String partNumberNote,
                                                  Boolean isInUse,
                                                  String brandId) {

        // 使用現有的查詢方法獲取所有數據（不分頁）
        RespPartNumberProfileQueryWrapper wrapper = queryPartNumberProfiles(customPartIdentifier, partNumberCode, partNumberName,
                                                                            productSeriesId, specification, partNumberCategoryId,
                                                                            partNumberNote, isInUse, brandId,
                                                                            0, Integer.MAX_VALUE);

        List<RespPartNumberProfileQuery> partNumbers = wrapper.getContent();

        // 如果前端沒有提供表頭，使用預設表頭
        if (headers == null || headers.isEmpty()) {
            headers = ExcelGenerator.ExcelInfo.PART_NUMBER_LIST.getColumnNames();
        }

        // 創建一個最終的表頭列表（使其成為effectively final）
        final List<String> finalHeaders = new ArrayList<>(headers);

        // 將數據轉換為Map列表，根據動態表頭
        List<Map<String, Object>> data = partNumbers.stream()
                .map(partNumber -> {
                    Map<String, Object> row = new HashMap<>();
                    // 遍歷表頭，根據表頭名稱設置對應的值
                    for (String header : finalHeaders) {
                        switch (header) {
                            case "料件識別碼" -> row.put(header, ExcelDataConverter.safeString(partNumber.getCustomPartIdentifier()));
                            case "料件編號" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberCode()));
                            case "料件名稱" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberName()));
                            case "規格" -> row.put(header, ExcelDataConverter.safeString(partNumber.getSpecification()));
                            case "安全庫存" -> row.put(header, ExcelDataConverter.convertIntegerToString(partNumber.getSafetyStockLevel()));
                            case "備註" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberNote()));
                            case "使用中" -> row.put(header, ExcelDataConverter.convertBooleanToYesNo(partNumber.getIsInUse()));
                            case "料件類別(大類別)" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberCategoryLevel0Name()));
                            case "料件類別(中類別)" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberCategoryLevel1Name()));
                            case "料件類別(小類別)" -> row.put(header, ExcelDataConverter.safeString(partNumber.getPartNumberCategoryLevel2Name()));
                            case "品牌" -> row.put(header, ExcelDataConverter.safeString(partNumber.getBrandName()));
                            case "產品系列" -> row.put(header, ExcelDataConverter.safeString(partNumber.getProductSeriesName()));
                            case "Blancco型號編號" -> row.put(header, ExcelDataConverter.safeString(partNumber.getBlanccoModelNumber()));
                            default -> row.put(header, ""); // 對於未知的表頭，設置空值
                        }
                    }
                    return row;
                })
                .toList();

        // 使用ExcelGenerator生成Excel，傳入動態表頭
        return excelGenerator.generateDynamicHeaderExcel(
                ExcelGenerator.SheetTitle.PART_NUMBER_LIST.getTitle(),
                headers,
                data,
                ExcelGenerator.ExcelInfo.PART_NUMBER_LIST
        );
    }

    /**
     * 為料件添加圖片
     *
     * @param partNumberId 料件ID
     * @param file         圖片文件
     * @param description  圖檔說明
     * @throws IOException 如果文件處理過程中發生錯誤
     */
    @Transactional
    public void addPartNumberImage(String partNumberId, MultipartFile file, String description) throws IOException {
        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查料件是否存在
        partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 使用 fileManagementHelper 上傳文件並獲取文件實體
        FileEntity fileEntity = fileManagementHelper.createNewFile(file, loginUserId);
        String fileId = fileEntity.getFileId();

        // 創建複合主鍵
        PartNumberImageId id = new PartNumberImageId(partNumberId, fileId);

        // 檢查是否已存在相同的圖片
        if (partNumberImageRepository.existsById(id)) {
            throw new ConflictException("此料件已存在相同圖片");
        }

        // 創建並保存圖片實體
        PartNumberImageEntity entity = new PartNumberImageEntity();
        entity.setId(id);
        entity.setDescription(description);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setCreateUser(loginUserId);

        partNumberImageRepository.saveAndFlush(entity);
    }

    /**
     * 刪除料件圖片
     *
     * @param partNumberId 料件ID
     * @param fileId       文件ID
     */
    @Transactional
    public void deletePartNumberImage(String partNumberId, String fileId) {
        // 創建複合主鍵
        PartNumberImageId id = new PartNumberImageId(partNumberId, fileId);

        // 檢查圖片是否存在
        if (!partNumberImageRepository.existsById(id)) {
            throw new ResourceNotFoundException("找不到指定的料件圖片");
        }

        // 使用 fileManagementHelper 刪除文件
        fileManagementHelper.deleteFileByFileId(fileId);

        // 刪除資料庫參照
        partNumberImageRepository.deleteById(id);
    }

    /**
     * 查詢料件的所有圖片
     *
     * @param partNumberId 料件ID
     * @return 料件圖片列表
     */
    public List<RespPartNumberImage> getPartNumberImages(String partNumberId) {
        // 檢查料件是否存在
        partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 查詢料件的所有圖片
        List<PartNumberImageEntity> images = partNumberImageRepository.findById_PartNumberId(partNumberId);

        // 轉換為響應模型
        return images.stream()
                .map(image -> {
                    String fileId = image.getId().getFileId();
                    String filePath = fileHelper.getFilePath(fileId);
                    String fileName = fileHelper.getFileName(fileId);

                    return RespPartNumberImage.builder()
                            .partNumberId(partNumberId)
                            .fileId(fileId)
                            .filePath(filePath)
                            .fileName(fileName)
                            .description(image.getDescription())
                            .build();
                })
                .toList();
    }








    /**
     * 取得所有啟用料件類別（下拉選單）
     *
     * @return 啟用料件類別列表
     */
    public List<RespPartNumberCategoryDropdown> getAllEnabledPartNumberCategories() {
        log.info("Getting all enabled part number categories for dropdown");

        // 查詢所有啟用的料件類別
        List<PartNumberCategoryEntity> categories = partNumberCategoryRepository.findByEnabledTrue();

        // 建立 ID 到實體的映射，用於查找父類別
        Map<String, PartNumberCategoryEntity> categoryMap = categories.stream()
                .collect(Collectors.toMap(
                        PartNumberCategoryEntity::getPartNumberCategoryId,
                        category -> category
                ));

        // 轉換為下拉選單回應模型
        return categories.stream()
                .map(entity -> mapToRespPartNumberCategoryDropdown(entity, categoryMap))
                .toList();
    }

    /**
     * 將料件類別實體轉換為下拉選單回應模型，並包含完整名稱
     *
     * @param entity 料件類別實體
     * @param categoryMap 所有類別的映射，用於查找父類別
     * @return 下拉選單回應模型
     */
    private RespPartNumberCategoryDropdown mapToRespPartNumberCategoryDropdown(
            PartNumberCategoryEntity entity, 
            Map<String, PartNumberCategoryEntity> categoryMap) {

        // 建立基本回應模型
        RespPartNumberCategoryDropdown model = RespPartNumberCategoryDropdown.builder()
                .id(entity.getPartNumberCategoryId())
                .parentId(entity.getPartNumberCategoryParentId())
                .code(entity.getPartNumberCategoryCode())
                .name(entity.getPartNumberCategoryName())
                .build();

        // 計算完整名稱 (包含上層類別)
        StringBuilder fullName = new StringBuilder(entity.getPartNumberCategoryName());
        String currentParentId = entity.getPartNumberCategoryParentId();

        // 如果不是頂層類別 (parentId 不是 "0")，則添加父類別名稱
        while (currentParentId != null && !currentParentId.equals("0") && categoryMap.containsKey(currentParentId)) {
            PartNumberCategoryEntity parent = categoryMap.get(currentParentId);
            fullName.insert(0, parent.getPartNumberCategoryName() + " > ");
            currentParentId = parent.getPartNumberCategoryParentId();
        }

        model.setFullName(fullName.toString());
        return model;
    }

    /**
     * 根據料件類別 ID 查詢品牌（下拉選單）
     *
     * @param categoryId 料件類別 ID
     * @return 品牌列表
     */
    @Transactional(readOnly = true)
    public List<RespBrandDropdown> getBrandsByCategoryId(String categoryId) {
        log.info("Getting brands for category ID: {}", categoryId);

        // 查詢指定類別 ID 關聯的所有啟用品牌關聯
        List<PartNumberCategoryBrandEntity> categoryBrands = partNumberCategoryBrandRepository
                .findById_CategoryIdAndEnabledTrue(categoryId);

        // 僅回傳啟用的品牌
        return categoryBrands.stream()
                .map(PartNumberCategoryBrandEntity::getBrand)
                .filter(Objects::nonNull)
                .filter(PartNumberBrandEntity::getEnabled)
                .map(this::mapToRespBrandDropdown)
                .toList();
    }

    /**
     * 將品牌實體轉換為下拉選單回應模型，並包含完整名稱
     *
     * @param entity 品牌實體
     * @return 下拉選單回應模型
     */
    private RespBrandDropdown mapToRespBrandDropdown(PartNumberBrandEntity entity) {

        return RespBrandDropdown.builder()
                .brandId(entity.getBrandId())
                .brandCode(entity.getBrandCode())
                .brandName(entity.getBrandName())
                .build();
    }

    /**
     * 查詢物料編號近1個月的修改記錄
     *
     * @param partNumberId 物料編號ID
     * @return 修改記錄回應
     */
    public RespPartNumberModificationHistory getPartNumberModificationHistory(String partNumberId) {
        log.info("Getting modification history for part number: {}", partNumberId);

        // 驗證物料編號是否存在
        if (!partNumberRepository.existsById(partNumberId)) {
            throw new ResourceNotFoundException("Part number not found with id: " + partNumberId);
        }

        // 計算近1個月的時間範圍
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusMonths(1);

        // 查詢audit記錄
        List<AuditLogEntity> auditLogs = auditLogService.getEntityModificationHistory(
                "PartNumberEntity", partNumberId, startTime, endTime);

        // 過濾掉系統欄位，只保留業務欄位的變更記錄
        List<AuditLogEntity> filteredAuditLogs = auditLogs.stream()
                .filter(this::isBusinessField)
                .toList();

        // 轉換為回應DTO
        List<RespPartNumberModificationHistory.ModificationRecord> modifications = filteredAuditLogs.stream()
                .map(this::mapToModificationRecord)
                .collect(Collectors.toList());

        return RespPartNumberModificationHistory.builder()
                .modifications(modifications)
                .build();
    }

    /**
     * 將AuditLogEntity轉換為ModificationRecord
     */
    private RespPartNumberModificationHistory.ModificationRecord mapToModificationRecord(AuditLogEntity auditLog) {
        // 根據 userId 查詢 userAccount，如果找不到則使用原始的 userId
        String creator = userHelper.findUserAccountByUserIdOrElseNull(auditLog.getUserId());
        if (creator == null) {
            creator = auditLog.getUserId(); // 如果找不到 userAccount，使用原始的 userId
        }

        return RespPartNumberModificationHistory.ModificationRecord.builder()
                .modificationId(auditLog.getId().toString())
                .modificationField(auditLog.getFieldName())
                .beforeModification(auditLog.getBeforeValue())
                .afterModification(auditLog.getAfterValue())
                .creator(creator)
                .createDatetime(auditLog.getChangedAt().toEpochSecond(ZoneOffset.UTC) * 1000) // 轉換為毫秒時間戳
                .build();
    }

    /**
     * 判斷是否為業務欄位（過濾掉系統欄位）
     */
    private boolean isBusinessField(AuditLogEntity auditLog) {
        String columnName = auditLog.getColumnName();

        // 系統欄位列表 - 這些欄位的變更不會顯示在修改記錄中
        Set<String> systemFields = Set.of(
                "creator",
                "createDatetime",
                "lastModifier",
                "lastModifyDatetime",
                "ENTITY_CREATED",    // 實體創建操作
                "ENTITY_DELETED"     // 實體刪除操作
        );

        // 如果不是系統欄位，則為業務欄位
        return !systemFields.contains(columnName);
    }

    /**
     * 停用料件
     *
     * @param partNumberId 料件ID
     * @param reqModel 停用請求模型
     */
    @Transactional
    public void disablePartNumber(String partNumberId, ReqPartNumberDisable reqModel) {
        log.info("停用料件: {}", partNumberId);

        final String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查料件是否存在
        PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 檢查料件是否已停用
        partNumberHelper.checkPartNumberNotDisabledOrElseThrow(partNumber, partNumberId);

        // 檢查EOL原因是否存在且啟用
        PartNumberEolReasonEntity eolReason = eolReasonHelper.findEolReasonByIdOrElseThrow(reqModel.getEolReasonId());
        eolReasonHelper.checkEolReasonNotDisabledOrElseThrow(eolReason, reqModel.getEolReasonId());

        // 停用料件
        partNumber.setEnabled(Boolean.FALSE);
        partNumber.setEolReasonId(reqModel.getEolReasonId());
        partNumber.setLastModifier(loginUserId);
        partNumber.setLastModifyDatetime(System.currentTimeMillis());

        partNumberRepository.saveAndFlush(partNumber);

        log.info("成功停用料件: {}", partNumberId);
    }

    /**
     * 查詢料件停用資訊
     *
     * @param partNumberId 料件ID
     * @return 停用資訊
     */
    public RespPartNumberDisableInfo getPartNumberDisableInfo(String partNumberId) {
        log.info("查詢料件停用資訊: {}", partNumberId);

        // 檢查料件是否存在
        PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 檢查料件是否已停用
        partNumberHelper.checkPartNumberDisabledOrElseThrow(partNumber, partNumberId);

        // 獲取EOL原因資訊
        PartNumberEolReasonEntity eolReason = eolReasonHelper.findEolReasonByIdOrElseThrow(partNumber.getEolReasonId());

        // 獲取停用人員姓名
        String disabledByUserName = systemAccountHelper.findUserNameByUserId(partNumber.getLastModifier());

        // 組裝回應
        RespPartNumberDisableInfo response = new RespPartNumberDisableInfo();
        response.setEolReasonName(eolReason.getEolReasonName());
        response.setEolReasonExplanation(eolReason.getEolExplanation());
        response.setDisabledByUserName(disabledByUserName);
        response.setDisabledDateTime(partNumber.getLastModifyDatetime());

        return response;
    }
}
