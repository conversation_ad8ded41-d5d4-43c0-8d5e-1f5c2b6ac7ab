package com.jgallop.api.service;

import com.jgallop.api.entity.*;
import com.jgallop.api.exception.ConflictException;
import com.jgallop.api.config.CurrencyProperties;
import com.jgallop.common.service.MessageHelper;
import com.jgallop.user.entity.UserEntity;
import com.jgallop.user.entity.UserType;
import com.jgallop.api.model.PartnerBasicInfoModel;
import com.jgallop.api.model.PartnerPaymentModel;
import com.jgallop.api.model.req.ReqPartnerProfileUpdate;
import com.jgallop.api.model.req.ReqPartnerSubsidiaryCreate;
import com.jgallop.api.model.resp.RespPartnerProfileDropdown;
import com.jgallop.api.model.resp.RespPartnerProfileQuery;
import com.jgallop.api.model.resp.RespPartnerProfileQueryWrapper;
import com.jgallop.api.repository.PartnerProfileRepository;
import com.jgallop.api.repository.PartnerTypeRepository;
import com.jgallop.api.repository.PaymentSettingDetailRepository;
import com.jgallop.api.repository.PartnerCurrencyRepository;
import com.jgallop.user.entity.DataScope;
import com.jgallop.user.enums.PermissionName;
import com.jgallop.user.service.UserHelper;
import com.jgallop.user.util.FieldMaskingUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneId;
import java.util.*;

import jakarta.persistence.criteria.JoinType;

/**
 * 供應商基本資料服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PartnerProfileService {

    private final UserHelper userHelper;
    private final MessageHelper messageHelper;
    private final PaymentHelper paymentHelper;
    private final PartnerHelper partnerHelper;
    private final ExcelGenerator excelGenerator;
    private final FieldMaskingUtils fieldMaskingUtils;
    private final SystemAccountHelper systemAccountHelper;
    private final PartnerProfileHelper partnerProfileHelper;
    private final PartnerAccessControlHelper partnerAccessControlHelper;
    private final GuiNumberValidationHelper guiNumberValidationHelper;
    private final CurrencyHelper currencyHelper;
    private final CurrencyProperties currencyProperties;

    private final PartnerTypeRepository partnerTypeRepository;
    private final PartnerProfileRepository partnerProfileRepository;
    private final PaymentSettingDetailRepository paymentSettingDetailRepository;
    private final PartnerCurrencyRepository partnerCurrencyRepository;

    /**
     * 查詢供應商基本資料
     *
     * @param partnerTypeIds  供應商類型ID列表
     * @param partnerId       供應商ID
     * @param partnerCode     供應商編號
     * @param zhName          供應商中文名稱
     * @param guiNumber       統一編號
     * @param owner           負責人
     * @param enabled         是否啟用
     * @param creator         建檔人員
     * @param createDatetime  建檔日期
     * @param lastModifier    最後修改人員
     * @param lastModifyDatetime 最後修改日期
     * @param paymentTermId     付款條件
     * @param paymentMethodId   付款方式
     * @param paymentDateTypeId 付款日期類型
     * @param paymentBaseDateId 付款基準日
     * @param page            分頁頁碼
     * @param size            每頁筆數
     * @param request         HTTP請求
     * @return 供應商基本資料查詢結果
     */
    @Transactional(readOnly = true)
    public RespPartnerProfileQueryWrapper queryPartnerProfiles(List<String> partnerTypeIds,
                                                               String partnerId,
                                                               String partnerCode,
                                                               String zhName,
                                                               Integer guiNumber,
                                                               String owner,
                                                               Boolean enabled,
                                                               String creator,
                                                               Long createDatetime,
                                                               String lastModifier,
                                                               Long lastModifyDatetime,
                                                               String paymentTermId,
                                                               String paymentMethodId,
                                                               String paymentDateTypeId,
                                                               String paymentBaseDateId,
                                                               int page,
                                                               int size,
                                                               HttpServletRequest request) {

        ZoneId zoneId = TimeHelper.resolveZoneId(request);

        // 初始化 Specification，預設 null
        Specification<PartnerProfileEntity> spec = Specification.where(null);
        //欄位搜尋 -> 依照不同型別，使用不同搜尋方法

        // 如果有指定供應商類型，則使用關聯表查詢
        if (partnerTypeIds != null && !partnerTypeIds.isEmpty()) {
            spec = spec.and((root, query, cb) -> {
                // 使用 JOIN 查詢關聯表
                assert query != null;
                query.distinct(true);
                var join = root.join("partnerTypeRelations", JoinType.INNER);
                return cb.and(
                    join.get("id").get("partnerTypeId").in(partnerTypeIds),
                    cb.equal(join.get("enabled"), true)
                );
            });
        }

        // 處理付款相關條件
        if (paymentTermId != null || paymentMethodId != null || paymentDateTypeId != null || paymentBaseDateId != null) {
            spec = spec.and((root, query, cb) -> {
                // 使用 JOIN 查詢付款設定表
                assert query != null;
                query.distinct(true);
                var paymentJoin = root.join("paymentSettings", JoinType.INNER);

                // 建立條件列表
                List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

                // 只查詢啟用的付款設定
                predicates.add(cb.equal(paymentJoin.get("enabled"), true));

                // 根據前端傳入的ID進行篩選
                if (paymentTermId != null) {
                    predicates.add(cb.equal(paymentJoin.get("paymentTermId"), paymentTermId));
                }

                if (paymentMethodId != null) {
                    predicates.add(cb.equal(paymentJoin.get("paymentMethodId"), paymentMethodId));
                }

                if (paymentDateTypeId != null) {
                    predicates.add(cb.equal(paymentJoin.get("paymentDateTypeId"), paymentDateTypeId));
                }

                if (paymentBaseDateId != null) {
                    predicates.add(cb.equal(paymentJoin.get("paymentBaseDateId"), paymentBaseDateId));
                }

                return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
            });
        }

        spec = SpecificationHelper.addEqualSpecificationForString(spec, "partnerId", partnerId);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partnerCode", partnerCode);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "partnerZhName", zhName);
        spec = SpecificationHelper.addFuzzySearchSpecificationForInteger(spec, "guiNumber", guiNumber);
        spec = SpecificationHelper.addFuzzySearchSpecificationForString(spec, "owner", owner);
        spec = SpecificationHelper.addEqualSpecificationForBoolean(spec, "enabled", enabled);
        spec = SpecificationHelper.addEqualSpecificationForString(spec, "creator", creator);
        spec = SpecificationHelper.addDateRangeSpecificationForLong(spec, "createDatetime", createDatetime, zoneId);
        spec = SpecificationHelper.addEqualSpecificationForString(spec, "lastModifier", lastModifier);
        spec = SpecificationHelper.addDateRangeSpecificationForLong(spec, "lastModifyDatetime", lastModifyDatetime, zoneId);

        // 添加數據範圍過濾
        spec = applyPartnerDataScope(spec);

        Pageable pageable = PageHelper.getPageableOrderByParam(page, size, "lastModifyDatetime", Sort.Direction.DESC);
        Page<PartnerProfileEntity> resultPage = partnerProfileRepository.findAll(spec, pageable);

        List<RespPartnerProfileQuery> data = resultPage.getContent().stream().map(this::convertToRespPartnerProfileQuery).toList();

        return new RespPartnerProfileQueryWrapper(resultPage.getTotalElements(), data);
    }

    /**
     * 取得所有啟用的供應商（下拉選單）
     *
     * @return 啟用的供應商列表
     */
    public List<RespPartnerProfileDropdown> getAllEnabledPartnersForDropdown() {
        List<PartnerProfileEntity> partners = partnerProfileRepository.findByEnabledTrueOrderByPartnerCodeAscPartnerZhNameAsc();
        return partners.stream()
                .map(this::convertToRespPartnerProfileDropdown)
                .toList();
    }

    /**
     * 將供應商實體轉換為下拉選單響應對象
     *
     * @param entity 供應商實體
     * @return 下拉選單響應對象
     */
    private RespPartnerProfileDropdown convertToRespPartnerProfileDropdown(PartnerProfileEntity entity) {
        if (entity == null) {
            return null;
        }

        RespPartnerProfileDropdown dropdown = new RespPartnerProfileDropdown();
        dropdown.setPartnerId(entity.getPartnerId());
        dropdown.setPartnerCode(entity.getPartnerCode());
        dropdown.setZhName(entity.getPartnerZhName());
        dropdown.setEnName(entity.getPartnerEnName());
        return dropdown;
    }

    /**
     * 根據ID獲取單一供應商基本資料
     *
     * @param partnerId 供應商ID
     * @return 供應商基本資料
     */
    @Transactional(readOnly = true)
    public RespPartnerProfileQuery getPartnerProfileById(String partnerId) {
        // 權限檢核：檢查是否有權限查詢此供應商
        partnerAccessControlHelper.validatePartnerAccess(partnerId, PartnerAccessControlHelper.PartnerOperation.READ);

        // 使用 helper 方法查詢供應商，若不存在則拋出異常
        PartnerProfileEntity partnerEntity = partnerProfileHelper.findByPartnerIdAndEnabledIsTrueOrThrow(partnerId);

        // 驗證 dataScope 權限
        validatePartnerDataScopeAccess(partnerEntity);

        // 使用現有的轉換方法將實體轉換為響應模型
        return convertToRespPartnerProfileQuery(partnerEntity);
    }

    /**
     * 驗證當前用戶是否有權限存取指定的供應商資料
     *
     * @param targetPartner 目標供應商實體
     * @throws IllegalArgumentException 如果沒有權限存取
     */
    private void validatePartnerDataScopeAccess(PartnerProfileEntity targetPartner) {
        // 獲取當前用戶
        final UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        // 根據用戶類型進行不同處理
        if (userEntity.getUserType() == UserType.EMPLOYEE) {
            // 員工可以查看所有數據，不需要過濾
            return;
        }

        if (userEntity.getUserType() == UserType.PARTNER) {
            // 供應商只能查看自己的數據
            try {
                // 獲取供應商ID
                String currentPartnerId = partnerHelper.getPartnerIdFromAuthentication();

                // 獲取數據範圍
                DataScope scope = fieldMaskingUtils.getDataScope(PermissionName.PARTNER);
                if (scope == null) {
                    // 如果沒有設置數據範圍，默認只能查看自己的數據
                    if (!currentPartnerId.equals(targetPartner.getPartnerId())) {
                        throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"));
                    }
                    return;
                }

                // 根據不同範圍進行權限檢查
                switch (scope) {
                    case SELF:
                        // 只能看自己的數據
                        if (!currentPartnerId.equals(targetPartner.getPartnerId())) {
                            throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"));
                        }
                        break;

                    case DEPT:
                    case ALL:
                        // 可以查看自己和子節點的數據
                        List<String> allowedPartnerIds = new ArrayList<>();
                        allowedPartnerIds.add(currentPartnerId);

                        // 遞迴獲取所有子供應商ID
                        getChildPartnerIds(currentPartnerId, allowedPartnerIds);

                        if (!allowedPartnerIds.contains(targetPartner.getPartnerId())) {
                            throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"));
                        }
                        break;

                    default:
                        // 默認只能查看自己的數據
                        if (!currentPartnerId.equals(targetPartner.getPartnerId())) {
                            throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"));
                        }
                }
            } catch (Exception e) {
                // 如果獲取供應商ID失敗，拋出權限不足異常
                throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"), e);
            }
        } else {
            // 其他用戶類型不能查看任何數據
            throw new IllegalArgumentException(messageHelper.localizedMessage("error.partner.access.denied"));
        }
    }

    /**
     * 根據用戶類型和數據範圍過濾供應商數據
     *
     * @param spec 原始查詢規範
     * @return 添加了數據範圍過濾的查詢規範
     */
    private Specification<PartnerProfileEntity> applyPartnerDataScope(Specification<PartnerProfileEntity> spec) {
        // 獲取當前用戶
        final UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        // 根據用戶類型進行不同處理
        if (userEntity.getUserType() == UserType.EMPLOYEE) {
            // 員工可以查看所有數據，不需要過濾
            return spec;
        } else if (userEntity.getUserType() == UserType.PARTNER) {
            // 供應商只能查看自己的數據
            try {
                // 獲取供應商ID
                String partnerId = partnerHelper.getPartnerIdFromAuthentication();

                // 獲取數據範圍
                DataScope scope = fieldMaskingUtils.getDataScope(PermissionName.PARTNER);
                if (scope == null) {
                    // 如果沒有設置數據範圍，默認只能查看自己的數據
                    return SpecificationHelper.addEqualSpecificationForString(spec, "partnerId", partnerId);
                }

                // 根據不同範圍套用不同的 Specification
                switch (scope) {
                    case SELF:
                        // 只能看自己的數據
                        return SpecificationHelper.addEqualSpecificationForString(spec, "partnerId", partnerId);

                    case DEPT, ALL:
                        // 可以查看自己和子節點的數據
                        List<String> partnerIds = new ArrayList<>();
                        partnerIds.add(partnerId);

                        // 遞迴獲取所有子供應商ID
                        getChildPartnerIds(partnerId, partnerIds);

                        // 添加供應商過濾條件
                        if (!partnerIds.isEmpty()) {
                            spec = SpecificationHelper.addInSpecificationForStringList(spec, "partnerId", partnerIds);
                        }
                        return spec;

                    default:
                        // 默認只能查看自己的數據
                        return SpecificationHelper.addEqualSpecificationForString(spec, "partnerId", partnerId);
                }
            } catch (Exception e) {
                // 如果獲取供應商ID失敗，返回空結果
                log.error("Failed to get partner ID from authentication", e);
                return (root, query, cb) -> cb.equal(cb.literal(1), 0); // 返回永假條件，確保不返回任何結果
            }
        } else {
            // 其他用戶類型不能查看任何數據
            return (root, query, cb) -> cb.equal(cb.literal(1), 0); // 返回永假條件，確保不返回任何結果
        }
    }

    /**
     * 遞迴獲取所有子供應商ID
     *
     * @param parentPartnerId 父供應商ID
     * @param partnerIds      存儲所有供應商ID的列表
     */
    private void getChildPartnerIds(String parentPartnerId, List<String> partnerIds) {
        List<PartnerProfileEntity> childPartners = partnerProfileRepository.findByPartnerParentIdAndEnabledTrue(parentPartnerId);

        for (PartnerProfileEntity childPartner : childPartners) {
            String childPartnerId = childPartner.getPartnerId();
            if (!partnerIds.contains(childPartnerId)) {
                partnerIds.add(childPartnerId);
                getChildPartnerIds(childPartnerId, partnerIds);
            }
        }
    }

    public RespPartnerProfileQuery convertToRespPartnerProfileQuery(PartnerProfileEntity entity) {
        if (entity == null) {
            return null;
        }

        RespPartnerProfileQuery model = new RespPartnerProfileQuery();
        model.setPartnerId(entity.getPartnerId());
        model.setPartnerCode(entity.getPartnerCode());
        model.setPartnerName(entity.getPartnerZhName());
        model.setPartnerEnName(entity.getPartnerEnName());
        model.setGuiNumber(entity.getGuiNumber());
        model.setOwner(entity.getOwner());
        model.setAddress(entity.getPhysicalAddress());
        model.setQrcode(entity.getQrcode());
        model.setGoogleMapLink(entity.getGoogleMapLink());
        model.setEnabled(entity.getEnabled());
        model.setCreator(systemAccountHelper.findUserNameByUserId(entity.getCreator()));
        model.setCreateDatetime(entity.getCreateDatetime());
        model.setLastModifier(systemAccountHelper.findUserNameByUserId(entity.getLastModifier()));
        model.setLastModifyDatetime(entity.getLastModifyDatetime());
        model.setEmail(entity.getEmail());
        // 權限控管：可見金額 權限才可看見保留%與佣金%
        boolean canViewAmounts = fieldMaskingUtils.canViewAmountFields(PermissionName.PARTNER);
        if (canViewAmounts) {
            model.setRetentionPercentage(entity.getRetentionPercentage());
            model.setCommissionPercentage(entity.getCommissionPercentage());
        } else {
            model.setRetentionPercentage(null);
            model.setCommissionPercentage(null);
        }

        // 設置新增的聯絡資訊欄位
        model.setCompanyPhone(entity.getCompanyPhone());
        model.setContactPerson(entity.getContactPerson());
        model.setContactMobile(entity.getContactMobile());

        // 獲取付款設定
        List<PaymentSettingDetailEntity> paymentSettings = paymentSettingDetailRepository.findByPartnerId(entity.getPartnerId());

        // 如果有付款設定，設置主要付款條件的值
        if (!paymentSettings.isEmpty()) {
            // 優先使用標記為主要付款條件的設定
            Optional<PaymentSettingDetailEntity> majorPaymentSetting = paymentSettings.stream()
                    .filter(PaymentSettingDetailEntity::getMajorPaymentTerm)
                    .filter(PaymentSettingDetailEntity::getEnabled)
                    .findFirst();

            // 如果沒有主要付款條件，使用第一個啟用的付款條件
            PaymentSettingDetailEntity paymentSetting = majorPaymentSetting.orElseGet(() -> 
                    paymentSettings.stream()
                            .filter(PaymentSettingDetailEntity::getEnabled)
                            .findFirst()
                            .orElse(null));

            if (paymentSetting != null) {
                model.setPaymentTerm(paymentHelper.getPaymentTermName(paymentSetting.getPaymentTermId()));
                model.setPaymentMethod(paymentHelper.getPaymentMethodName(paymentSetting.getPaymentMethodId()));
                model.setPaymentDateType(paymentHelper.getPaymentDateTypeName(paymentSetting.getPaymentDateTypeId()));
                model.setPaymentBaseDate(paymentHelper.getPaymentBaseDateName(paymentSetting.getPaymentBaseDateId()));
            }
        }

        // 設置所有啟用的付款設定
        List<PartnerPaymentModel> enabledPaymentSettings = paymentSettings.stream()
                .filter(PaymentSettingDetailEntity::getEnabled)
                .map(ps -> {
                    PartnerPaymentModel paymentModel = new PartnerPaymentModel();
                    paymentModel.setPaymentSettingId(ps.getPaymentSettingDetailId());
                    paymentModel.setPaymentMethod(paymentHelper.getPaymentMethodName(ps.getPaymentMethodId()));
                    paymentModel.setPaymentTerm(paymentHelper.getPaymentTermName(ps.getPaymentTermId()));
                    paymentModel.setPaymentDateType(paymentHelper.getPaymentDateTypeName(ps.getPaymentDateTypeId()));
                    paymentModel.setPaymentBaseDate(paymentHelper.getPaymentBaseDateName(ps.getPaymentBaseDateId()));
                    paymentModel.setMajorPaymentTerm(ps.getMajorPaymentTerm());
                    paymentModel.setEnabled(ps.getEnabled());
                    return paymentModel;
                })
                .toList();
        model.setPaymentSettings(enabledPaymentSettings);

        // 設置供應商類型名稱列表
        if (entity.getPartnerTypeRelations() != null && !entity.getPartnerTypeRelations().isEmpty()) {
            List<String> typeNames = entity.getPartnerTypeRelations().stream()
                    .filter(PartnerTypeRelationEntity::getEnabled)  // 只獲取啟用的類型
                    .map(relation -> {
                        // 通過 partnerTypeId 獲取類型名稱
                        return partnerTypeRepository.findById(relation.getId().getPartnerTypeId())
                                .map(PartnerTypeEntity::getPartnerTypeName)
                                .orElse(null);
                    })
                    .filter(Objects::nonNull)
                    .toList();

            model.setPartnerType(typeNames);
        } else {
            model.setPartnerType(List.of());
        }

        return model;
    }

    /**
     * 創建供應商基本資料並返回ID
     *
     * @param basicInfo      基本資料模型
     */
    @Transactional
    public void createPartnerBasicInfo(PartnerBasicInfoModel basicInfo) {
        String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查統一編號是否重複
        guiNumberValidationHelper.checkGuiNumberDuplicate(basicInfo.getGuiNumber());

        // 儲存基本資訊並獲取供應商檔案ID
        createPartnerBasicInfoAndSave(basicInfo, loginUserId);
    }

    private void createPartnerBasicInfoAndSave(PartnerBasicInfoModel basicInfo, String loginUserId) {
        PartnerProfileEntity entity = new PartnerProfileEntity();

        // 設置基本資訊
        entity.setPartnerCode(basicInfo.getPartnerCode());
        entity.setPartnerZhName(basicInfo.getPartnerName());
        entity.setPartnerEnName(basicInfo.getPartnerEnName());
        entity.setOwner(basicInfo.getOwner());
        entity.setEmail(basicInfo.getEmail());
        entity.setGuiNumber(basicInfo.getGuiNumber());
        entity.setRetentionPercentage(basicInfo.getRetentionPercentage());
        entity.setCommissionPercentage(basicInfo.getCommissionPercentage());
        entity.setQrcode(basicInfo.getQrcode());
        entity.setGoogleMapLink(basicInfo.getGoogleMapLink());

        // 設置聯絡資訊欄位
        entity.setCompanyPhone(basicInfo.getCompanyPhone());
        entity.setContactPerson(basicInfo.getContactPerson());
        entity.setContactMobile(basicInfo.getContactMobile());

        // 設置地址
        entity.setRegisterAddress(basicInfo.getAddress());
        entity.setPhysicalAddress(basicInfo.getAddress());

        // 設置審計欄位
        entity.setCreator(loginUserId);
        entity.setCreateDatetime(System.currentTimeMillis());
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        partnerProfileRepository.saveAndFlush(entity);

        // 為新創建的供應商設定預設交易幣別
        setupDefaultCurrencyForPartner(entity.getPartnerId(), loginUserId);
    }

    /**
     * 為供應商設定預設交易幣別
     *
     * @param partnerId   供應商ID
     * @param loginUserId 登入用戶ID
     */
    private void setupDefaultCurrencyForPartner(String partnerId, String loginUserId) {
        try {
            // 從配置檔讀取預設幣別代碼
            String defaultCurrencyCode = currencyProperties.getDefaultCurrencyCode();
            log.debug("Setting up default currency for partner: {}, currency code: {}", partnerId, defaultCurrencyCode);

            // 根據幣別代碼查詢幣別實體
            CurrencyEntity defaultCurrency = currencyHelper.findCurrencyByCodeOrNull(defaultCurrencyCode);
            if (defaultCurrency == null) {
                log.warn("Default currency not found for code: {}, skipping default currency setup for partner: {}",
                        defaultCurrencyCode, partnerId);
                return;
            }

            // 創建供應商交易幣別關聯
            PartnerCurrencyEntity partnerCurrency = new PartnerCurrencyEntity();

            // 設定複合主鍵
            PartnerCurrencyId id = new PartnerCurrencyId();
            id.setPartnerId(partnerId);
            id.setCurrencyId(defaultCurrency.getCurrencyId());
            partnerCurrency.setId(id);

            // 設定為主要幣別
            partnerCurrency.setMainCurrency(Boolean.TRUE);
            partnerCurrency.setEnabled(Boolean.TRUE);

            // 設定審計欄位
            partnerCurrency.setCreator(loginUserId);
            partnerCurrency.setCreateDatetime(System.currentTimeMillis());
            partnerCurrency.setLastModifier(loginUserId);
            partnerCurrency.setLastModifyDatetime(System.currentTimeMillis());

            // 儲存供應商交易幣別
            partnerCurrencyRepository.save(partnerCurrency);

            log.info("Successfully set up default currency {} for partner: {}",
                    defaultCurrency.getCurrencyZhName(), partnerId);

        } catch (Exception e) {
            log.error("Failed to setup default currency for partner: {}, error: {}", partnerId, e.getMessage(), e);
            // 不拋出異常，避免影響供應商創建流程
        }
    }

    /**
     * 異動供應商基本資料
     *
     * @param partnerId      供應商ID
     * @param reqModel       供應商基本資料請求
     */
    @Transactional
    public void updatePartnerProfile(String partnerId, ReqPartnerProfileUpdate reqModel) {
        // 權限檢核：檢查是否有權限修改此供應商
        partnerAccessControlHelper.validatePartnerAccess(partnerId, PartnerAccessControlHelper.PartnerOperation.UPDATE);

        // 查詢供應商基本資料並檢查狀態是否為啟用
        partnerProfileHelper.checkPartnerActiveOrElseThrow(partnerId);
        PartnerProfileEntity entity = partnerProfileHelper.findPartnerProfileByPartnerIdOrThrow(partnerId);

        String loginUserId = userHelper.findUserIdFromAuthentication();


        // 檢查統一編號是否重複（如果有提供統編）
        if (reqModel.getGuiNumber() != null) {
            guiNumberValidationHelper.checkGuiNumberDuplicateForPartnerUpdate(reqModel.getGuiNumber(), partnerId);
        }

        // 更新基本資料
        entity.setPartnerZhName(reqModel.getPartnerName());
        entity.setPartnerEnName(reqModel.getPartnerEnName());
        entity.setOwner(reqModel.getOwner());
        entity.setRegisterAddress(reqModel.getAddress());
        entity.setPhysicalAddress(reqModel.getAddress());
        entity.setEmail(reqModel.getEmail());
        entity.setGuiNumber(reqModel.getGuiNumber());
        entity.setRetentionPercentage(reqModel.getRetentionPercentage());
        entity.setCommissionPercentage(reqModel.getCommissionPercentage());
        entity.setQrcode(reqModel.getQrcode());
        entity.setGoogleMapLink(reqModel.getGoogleMapLink());

        // 設置聯絡資訊欄位
        entity.setCompanyPhone(reqModel.getCompanyPhone());
        entity.setContactPerson(reqModel.getContactPerson());
        entity.setContactMobile(reqModel.getContactMobile());

        // 設置最後修改人員及修改時間
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        // 儲存供應商基本資料
        partnerProfileRepository.saveAndFlush(entity);
    }

    /**
     * 停用供應商基本資料
     *
     * @param partnerId 供應商ID
     */
    @Transactional
    public void disablePartnerProfile(String partnerId) {
        // 權限檢核：檢查是否有權限停用此供應商
        partnerAccessControlHelper.validatePartnerAccess(partnerId, PartnerAccessControlHelper.PartnerOperation.DELETE);

        // 查詢供應商基本資料
        PartnerProfileEntity entity = partnerProfileHelper.findPartnerProfileByPartnerIdOrThrow(partnerId);

        // 檢查是否已經停用
        if (!entity.getEnabled()) {
            throw new ConflictException(messageHelper.localizedMessage("error.partner.profile.already.disabled", partnerId));
        }

        String loginUserId = userHelper.findUserIdFromAuthentication();


        // 停用供應商
        entity.setEnabled(false);
        entity.setLastModifier(loginUserId);
        entity.setLastModifyDatetime(System.currentTimeMillis());

        // 儲存供應商基本資料
        partnerProfileRepository.saveAndFlush(entity);

        log.info("Partner profile disabled: {} by user: {}", partnerId, loginUserId);
    }

    /**
     * 匯出供應商基本資料至Excel
     *
     * @param headers            動態表頭
     * @param partnerTypeIds     供應商類型ID列表
     * @param partnerId          供應商ID
     * @param partnerCode        供應商編號
     * @param zhName             供應商中文名稱
     * @param guiNumber          統一編號
     * @param owner              負責人
     * @param enabled            是否啟用
     * @param creator            建檔人員
     * @param createDatetime     建檔日期
     * @param lastModifier       最後修改人員
     * @param lastModifyDatetime 最後修改日期
     * @param paymentTerm        付款條件
     * @param paymentMethod      付款方式
     * @param paymentDateType    付款日期類型
     * @param paymentBaseDate    付款基準日
     * @param request            HTTP請求
     * @return Excel檔案字節數組
     */
    @Transactional(readOnly = true)
    public byte[] exportPartnerProfilesToExcel(List<String> headers,
                                               List<String> partnerTypeIds,
                                               String partnerId,
                                               String partnerCode,
                                               String zhName,
                                               Integer guiNumber,
                                               String owner,
                                               Boolean enabled,
                                               String creator,
                                               Long createDatetime,
                                               String lastModifier,
                                               Long lastModifyDatetime,
                                               String paymentTerm,
                                               String paymentMethod,
                                               String paymentDateType,
                                               String paymentBaseDate,
                                               HttpServletRequest request) {
        // 使用現有的查詢方法獲取所有數據（不分頁）
        RespPartnerProfileQueryWrapper wrapper = queryPartnerProfiles(
                partnerTypeIds, partnerId, partnerCode, zhName, guiNumber, owner, enabled,
                creator, createDatetime, lastModifier, lastModifyDatetime,
                paymentTerm, paymentMethod, paymentDateType, paymentBaseDate,
                0, Integer.MAX_VALUE, request);

        List<RespPartnerProfileQuery> partnerProfiles = wrapper.getData();

        // 如果前端沒有提供表頭，使用預設表頭
        if (headers == null || headers.isEmpty()) {
            headers = ExcelGenerator.ExcelInfo.PARTNER_PROFILE_LIST.getColumnNames();
        }

        // 獲取時區用於日期格式化
        ZoneId zoneId = TimeHelper.resolveZoneId(request);

        // 創建最終表頭列表（使其有效地為final）
        final List<String> finalHeaders = new ArrayList<>(headers);

        // 根據動態表頭將數據轉換為Map列表
        List<Map<String, Object>> data = partnerProfiles.stream()
                .map(partnerProfile -> {
                    Map<String, Object> row = new HashMap<>();

                    // 遍歷表頭並設置相應的值
                    for (String header : finalHeaders) {
                        switch (header) {
                            case "供應商編號" -> row.put(header, partnerProfile.getPartnerId());
                            case "供應商名稱" -> row.put(header, partnerProfile.getPartnerName());
                            case "供應商英文名稱" -> row.put(header, partnerProfile.getPartnerEnName());
                            case "統一編號" -> row.put(header, partnerProfile.getGuiNumber());
                            case "負責人" -> row.put(header, partnerProfile.getOwner());
                            case "公司地址" -> row.put(header, partnerProfile.getAddress());
                            case "付款條件" -> row.put(header, partnerProfile.getPaymentTerm());
                            case "付款方式" -> row.put(header, partnerProfile.getPaymentMethod());
                            case "付款日期類型" -> row.put(header, partnerProfile.getPaymentDateType());
                            case "付款基準日" -> row.put(header, partnerProfile.getPaymentBaseDate());
                            case "是否啟用" -> row.put(header, partnerProfile.getEnabled());
                            case "建立人" -> row.put(header, partnerProfile.getCreator());
                            case "建立日期時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(partnerProfile.getCreateDatetime(), zoneId));
                            case "最後修改人" -> row.put(header, partnerProfile.getLastModifier());
                            case "最後修改日期時間" -> row.put(header, TimeHelper.formatTimestampToDatetime(partnerProfile.getLastModifyDatetime(), zoneId));
                            default -> row.put(header, ""); // 對於未知表頭，設置空值
                        }
                    }
                    return row;
                })
                .toList();

        // 使用ExcelGenerator生成具有動態表頭的Excel
        return excelGenerator.generateDynamicHeaderExcel(
                ExcelGenerator.SheetTitle.PARTNER_PROFILE_LIST.getTitle(),
                headers,
                data,
                ExcelGenerator.ExcelInfo.PARTNER_PROFILE_LIST
        );
    }

    /**
     * 獲取母公司下拉選單
     * 根據當前登入用戶的數據範圍權限，返回可選擇的母公司清單
     *
     * @return 可選擇的母公司列表
     */
    @Transactional(readOnly = true)
    public List<RespPartnerProfileDropdown> getParentCompaniesForDropdown() {
        // 獲取當前用戶
        final UserEntity userEntity = userHelper.findUserEntityFromAuthentication();

        // 根據用戶類型進行不同處理
        if (userEntity.getUserType() == UserType.EMPLOYEE) {
            // 員工可以查看所有啟用的供應商
            return getAllEnabledPartnersForDropdown();
        } else if (userEntity.getUserType() == UserType.PARTNER) {
            // 供應商根據數據範圍權限查看相應的供應商
            try {
                // 獲取供應商ID
                String partnerId = partnerHelper.getPartnerIdFromAuthentication();

                // 獲取數據範圍
                DataScope scope = fieldMaskingUtils.getDataScope(PermissionName.PARTNER);
                if (scope == null) {
                    // 如果沒有設置數據範圍，默認只能查看自己
                    PartnerProfileEntity currentPartner = partnerProfileHelper.findByPartnerIdAndEnabledIsTrueOrThrow(partnerId);
                    return List.of(convertToRespPartnerProfileDropdown(currentPartner));
                }

                // 根據不同範圍返回不同的供應商列表
                switch (scope) {
                    case SELF:
                        // 只能看自己
                        PartnerProfileEntity currentPartner = partnerProfileHelper.findByPartnerIdAndEnabledIsTrueOrThrow(partnerId);
                        return List.of(convertToRespPartnerProfileDropdown(currentPartner));

                    case DEPT, ALL:
                        // 可以查看自己和子節點的數據
                        List<String> partnerIds = new ArrayList<>();
                        partnerIds.add(partnerId);

                        // 遞迴獲取所有子供應商ID
                        getChildPartnerIds(partnerId, partnerIds);

                        // 查詢所有相關的啟用供應商
                        List<PartnerProfileEntity> partners = partnerProfileRepository.findByPartnerIdInAndEnabledTrueOrderByPartnerCodeAscPartnerZhNameAsc(partnerIds);
                        return partners.stream()
                                .map(this::convertToRespPartnerProfileDropdown)
                                .toList();

                    default:
                        // 默認只能查看自己
                        PartnerProfileEntity defaultPartner = partnerProfileHelper.findByPartnerIdAndEnabledIsTrueOrThrow(partnerId);
                        return List.of(convertToRespPartnerProfileDropdown(defaultPartner));
                }
            } catch (Exception e) {
                // 如果獲取供應商ID失敗，返回空列表
                log.error("Failed to get partner ID from authentication for dropdown", e);
                return List.of();
            }
        } else {
            // 其他用戶類型不能查看任何數據
            return List.of();
        }
    }

    /**
     * 新增子公司/門市
     * 在指定母公司下新增子公司/門市，繼承母公司部分資料
     *
     * @param parentId 母公司ID
     * @param reqSubsidiaryCreate 新門市的基本資料
     */
    @Transactional
    public void createSubsidiary(String parentId, ReqPartnerSubsidiaryCreate reqSubsidiaryCreate) {
        // 權限檢核：檢查是否有權限在此母公司下新增子公司
        partnerAccessControlHelper.validatePartnerAccess(parentId, PartnerAccessControlHelper.PartnerOperation.CREATE);

        // 驗證母公司存在且啟用
        PartnerProfileEntity parentCompany = partnerProfileHelper.findByPartnerIdAndEnabledIsTrueOrThrow(parentId);

        // 驗證當前用戶是否有權限存取此母公司
        validatePartnerDataScopeAccess(parentCompany);

        String loginUserId = userHelper.findUserIdFromAuthentication();

        // 檢查供應商編號是否重複
        partnerProfileHelper.checkPartnerCodeDuplicateOrElseThrow(reqSubsidiaryCreate.getPartnerCode());

        // 創建新的子公司實體，繼承母公司部分資料
        PartnerProfileEntity subsidiary = new PartnerProfileEntity();

        // 設置新門市的基本資訊（不繼承的欄位）
        subsidiary.setPartnerCode(reqSubsidiaryCreate.getPartnerCode());
        subsidiary.setPartnerZhName(reqSubsidiaryCreate.getPartnerName());
        subsidiary.setPartnerEnName(reqSubsidiaryCreate.getPartnerEnName());
        subsidiary.setRegisterAddress(reqSubsidiaryCreate.getAddress());
        subsidiary.setPhysicalAddress(reqSubsidiaryCreate.getAddress());
        subsidiary.setCompanyPhone(reqSubsidiaryCreate.getCompanyPhone());
        subsidiary.setContactPerson(reqSubsidiaryCreate.getContactPerson());
        subsidiary.setContactMobile(reqSubsidiaryCreate.getContactMobile());
        subsidiary.setEmail(reqSubsidiaryCreate.getEmail());
        subsidiary.setGoogleMapLink(reqSubsidiaryCreate.getGoogleMapLink());
        subsidiary.setQrcode(reqSubsidiaryCreate.getQrcode());

        // 繼承母公司的資料（除了名稱、地址、電話、Email、Google地圖連結、QR碼外）
        subsidiary.setOwner(parentCompany.getOwner());
        subsidiary.setGuiNumber(parentCompany.getGuiNumber());
        subsidiary.setRetentionPercentage(parentCompany.getRetentionPercentage());
        subsidiary.setCommissionPercentage(parentCompany.getCommissionPercentage());

        // 設置父子關係
        subsidiary.setPartnerParentId(parentId);

        // 設置審計欄位
        subsidiary.setCreator(loginUserId);
        subsidiary.setCreateDatetime(System.currentTimeMillis());
        subsidiary.setLastModifier(loginUserId);
        subsidiary.setLastModifyDatetime(System.currentTimeMillis());

        // 儲存子公司
        partnerProfileRepository.saveAndFlush(subsidiary);

        // 為新創建的子公司設定預設交易幣別
        setupDefaultCurrencyForPartner(subsidiary.getPartnerId(), loginUserId);

        log.info("Successfully created subsidiary {} under parent company {} by user: {}",
                subsidiary.getPartnerId(), parentId, loginUserId);
    }
}
