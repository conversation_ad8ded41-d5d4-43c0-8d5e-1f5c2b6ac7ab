package com.jgallop.api.service;

import com.jgallop.api.entity.*;
import com.jgallop.api.entity.enums.InspectionFormType;
import com.jgallop.api.entity.enums.InspectionStatus;
import com.jgallop.api.entity.enums.QuestionType;
import com.jgallop.api.exception.ResourceNotFoundException;
import com.jgallop.api.model.req.ReqRecycleTest;
import com.jgallop.api.model.resp.*;
import com.jgallop.api.repository.*;
import com.jgallop.api.service.mapper.RecycleWebsiteMapper;
import com.jgallop.api.service.query.RecycleWebsiteQueryService;
import com.jgallop.common.service.MessageHelper;
import com.jgallop.hfs.entity.postgres.FileEntity;
import com.jgallop.hfs.service.FileManagementHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Comparator;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 供應商回收網站服務
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecycleWebsiteService {

    private final FileHelper fileHelper;
    private final PartnerHelper partnerHelper;
    private final PartNumberHelper partNumberHelper;
    private final PartnerTreeHelper partnerTreeHelper;
    private final RecycleOrderHelper recycleOrderHelper;
    private final RecycleWebsiteHelper recycleWebsiteHelper;
    private final InspectionFormHelper inspectionFormHelper;
    private final InspectionGroupHelper inspectionGroupHelper;
    private final SellerInfomationHelper sellerInformationHelper;
    private final FileManagementHelper fileManagementHelper;
    private final MessageHelper messageHelper;
    private final TransactionAgreementHelper transactionAgreementHelper;

    // 新增的優化服務
    private final RecycleWebsiteQueryService queryService;
    private final RecycleWebsiteMapper mapper;


    private final CompanyRepository companyRepository;
    private final PartNumberRepository partNumberRepository;
    private final PartnerBrandRepository partnerBrandRepository;
    private final PartnerProfileRepository partnerProfileRepository;
    private final PartNumberBrandRepository partNumberBrandRepository;
    private final PartNumberProductSeriesRepository partNumberProductSeriesRepository;
    private final PartNumberRecoveryDetailRepository partNumberRecoveryDetailRepository;

    private final InspectionFormRepository inspectionFormRepository;
    private final InspectionGroupRepository inspectionGroupRepository;
    private final InspectionDetailRepository inspectionDetailRepository;
    private final InspectionResultRepository inspectionResultRepository;
    private final InspectionQuestionRepository inspectionQuestionRepository;
    private final InspectionGroupQuestionRepository inspectionGroupQuestionRepository;
    private final InspectionQuestionOptionRepository inspectionQuestionOptionRepository;
    private final ProductQuestionOptionAmountRepository productQuestionOptionAmountRepository;

    /**
     * 查詢供應商可回收的品牌
     *
     * @param partnerCode 供應商代碼
     * @return 可回收品牌列表
     */
    @Transactional(readOnly = true)
    public List<RespRecycleWebsiteBrand> queryRecycleWebsiteBrands(String partnerCode) {
        log.info("Querying recycle website brands for partner code: {}", partnerCode);

        // 檢查是否為創宇（直接商店合作夥伴）
        if (partnerHelper.isSmDirectStore(partnerCode)) {
            List<PartNumberBrandEntity> brands = partNumberBrandRepository.findByEnabledIsTrue();
            return mapper.mapToBrandList(brands);
        }

        // 供應商邏輯
        String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);
        List<PartnerBrandEntity> partnerBrands = partnerBrandRepository
                .findById_PartnerIdAndEnabledIsTrueAndVisibleToSupplierIsTrue(partnerId);

        // 獲取品牌詳細資訊
        List<String> brandIds = partnerBrands.stream()
                .map(PartnerBrandEntity::getId)
                .map(PartnerBrandId::getBrandId)
                .toList();

        Map<String, PartNumberBrandEntity> brandMap = partNumberBrandRepository.findAllById(brandIds).stream()
                .collect(Collectors.toMap(PartNumberBrandEntity::getBrandId, brand -> brand));

        // 轉換為回應物件
        return partnerBrands.stream()
                .map(entity -> recycleWebsiteHelper.mapToRespRecycleWebsiteBrand(entity, brandMap))
                .toList();
    }

    /**
     * 查詢指定品牌下的產品系列（樹狀結構）
     *
     * @param brandId     品牌ID
     * @param partnerCode 供應商代碼
     * @return 產品系列樹狀結構列表
     */
    @Transactional(readOnly = true)
    public RespRecycleWebsiteProductSeriesWrapper queryBrandProductSeries(String brandId, String partnerCode) {
        log.info("Querying product series tree for brand ID: {} and partner code: {}", brandId, partnerCode);

        List<PartNumberProductSeriesEntity> allProductSeries;

        // 檢查是否為創宇（直接商店合作夥伴）
        if (partnerHelper.isSmDirectStore(partnerCode)) {
            // 創宇可以查看指定品牌的啟用產品系列
            allProductSeries = partNumberProductSeriesRepository.findByBrandIdAndEnabledTrue(brandId);
        } else {
            // 供應商邏輯 - 需要驗證供應商是否有權限查看該品牌
            String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);

            // 檢查供應商是否有權限查看該品牌
            List<PartnerBrandEntity> partnerBrands = partnerBrandRepository
                    .findById_PartnerIdAndEnabledIsTrueAndVisibleToSupplierIsTrue(partnerId);

            boolean hasAccessToBrand = partnerBrands.stream()
                    .anyMatch(pb -> brandId.equals(pb.getId().getBrandId()));

            if (!hasAccessToBrand) {
                throw new ResourceNotFoundException(messageHelper.localizedMessage("error.brand.access.denied", brandId));
            }

            // 查詢該品牌下的啟用產品系列
            allProductSeries = partNumberProductSeriesRepository.findByBrandIdAndEnabledTrue(brandId);
        }

        // 找出所有根節點（parent_id 為 "0" 或 null）
        List<PartNumberProductSeriesEntity> rootSeries = allProductSeries.stream()
                .filter(series -> "0".equals(series.getProductSeriesParentId()) ||
                        series.getProductSeriesParentId() == null)
                .toList();

        // 建立樹狀結構
        List<RespRecycleWebsiteProductSeries> treeNodes = buildRecycleWebsiteProductSeriesTree(rootSeries, allProductSeries);

        return RespRecycleWebsiteProductSeriesWrapper.builder()
                .productSeries(treeNodes)
                .build();
    }

    /**
     * 查詢單一商品詳情
     *
     * @param partNumberId 料件ID
     * @param partnerCode  供應商代碼
     * @return 商品詳情
     */
    @Transactional
    public RespRecycleWebsiteProductDetail queryRecycleWebsiteProductDetail(String partNumberId, String partnerCode) {
        // 檢查是否為創宇（直接商店合作夥伴）
        if (partnerHelper.isSmDirectStore(partnerCode)) {
            // 如果是創宇，直接返回料件詳情，不檢查供應商權限
            PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

            // 增加 viewCount 計數
            if (partNumber.getViewCount() == null) {
                partNumber.setViewCount(1);
            } else {
                partNumber.setViewCount(partNumber.getViewCount() + 1);
            }
            partNumberRepository.save(partNumber);

            // 轉換為回應物件
            return recycleWebsiteHelper.mapToProductDetail(partNumber, null);
        }

        // 原有邏輯（供應商）
        String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);
        log.info("Querying item detail for part number ID: {} and partner ID: {}", partNumberId, partnerId);

        // 查詢料件基本資訊
        PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);

        // 查詢料件回收詳情
        PartNumberRecoveryDetailEntity recoveryDetail = partNumberHelper.findRecoveryDetailByIdOrThrow(partNumberId, partnerId);

        // 檢查供應商是否有權限查看該料件
        if (!recoveryDetail.getEnabled() || !recoveryDetail.getVisibleToSupplier()) {
            throw new ResourceNotFoundException("供應商無權查看此料件");
        }

        // 建立料件回收詳情ID
        PartNumberRecoveryDetailId id = new PartNumberRecoveryDetailId(partNumberId, partnerId);

        // 檢查是否有登入用戶
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && !(authentication instanceof AnonymousAuthenticationToken)) {
            try {
                // 嘗試獲取登入用戶的供應商ID
                String loggedInPartnerId = partnerHelper.getPartnerIdFromAuthentication();

                // 如果登入的供應商ID與請求的供應商ID相同，則增加供應商觀看次數
                if (partnerId.equals(loggedInPartnerId)) {
                    partNumberRecoveryDetailRepository.incrementSupplierViewCount(id);
                    log.info("Incremented supplier view count for part number ID: {} and partner ID: {}", partNumberId, partnerId);
                } else {
                    // 登入用戶不是供應商，增加外部訪問次數
                    partNumberRecoveryDetailRepository.incrementExternalViewCount(id);
                    log.info("Incremented external view count for part number ID: {} and partner ID: {}", partNumberId, partnerId);
                }
            } catch (Exception e) {
                // 如果獲取供應商ID失敗，視為外部訪問
                partNumberRecoveryDetailRepository.incrementExternalViewCount(id);
                log.debug("User is not a partner, incrementing external view count: {}", e.getMessage());
            }
        } else {
            // 未登入用戶，增加外部訪問次數
            partNumberRecoveryDetailRepository.incrementExternalViewCount(id);
            log.info("Incremented external view count for anonymous user, part number ID: {} and partner ID: {}", partNumberId, partnerId);
        }

        // 轉換為回應物件
        return recycleWebsiteHelper.mapToProductDetail(partNumber, recoveryDetail);
    }

    /**
     * 查詢品牌與商品巢狀結構（支持關鍵字搜索和分頁）
     *
     * @param brandId         品牌ID (可選)
     * @param productSeriesId 產品系列ID (可選)
     * @param keyword         關鍵字，用於搜索品牌名稱或商品名稱 (可選)
     * @param partnerCode     供應商代碼
     * @param page            頁碼，從0開始
     * @param size            每頁大小
     * @return 品牌與商品巢狀結構包裝器
     */
    @Transactional(readOnly = true)
    public RespRecycleWebsiteBrandProductQueryWrapper searchRecycleWebsiteBrandProducts(String brandId, String productSeriesId, String keyword, String partnerCode, int page, int size) {
        log.info("開始查詢品牌與商品巢狀結構 - brandId: {}, productSeriesId: {}, keyword: {}, partnerCode: {}, page: {}, size: {}",
                 brandId, productSeriesId, keyword, partnerCode, page, size);

        // 建立分頁參數
        Pageable pageable = PageHelper.getPageableOrderByParam(page, size, "partNumberName", Sort.Direction.ASC);
        log.debug("建立分頁參數完成 - 排序欄位: partNumberName, 排序方向: ASC");

        // 檢查是否為創宇（直接商店合作夥伴）
        boolean isDirectStore = partnerHelper.isSmDirectStore(partnerCode);
        log.debug("合作夥伴類型判斷完成 - partnerCode: {}, isDirectStore: {}", partnerCode, isDirectStore);

        if (isDirectStore) {
            log.info("執行創宇直營店查詢邏輯");
            RecycleWebsiteQueryService.DirectStoreQueryResult queryResult = queryService.queryDirectStoreDataWithPagination(brandId, productSeriesId, keyword, pageable);

            if (queryResult.isEmpty()) {
                log.info("創宇直營店查詢結果為空，返回空結果");
                return RespRecycleWebsiteBrandProductQueryWrapper.builder()
                        .totalElements(0L)
                        .content(Collections.emptyList())
                        .build();
            }

            log.debug("創宇直營店查詢結果 - 品牌數量: {}, 料件數量: {}, 總筆數: {}",
                      queryResult.getBrands().size(),
                      queryResult.getPartNumbers().size(),
                      queryResult.getTotalElements());

            List<RespRecycleWebsiteBrandProduct> content = mapper.buildBrandProductStructure(
                    queryResult.getBrands(),
                    queryResult.getProductSeriesMap(),
                    queryResult.getPartNumbers(),
                    true
            );

            log.info("創宇直營店資料結構建立完成 - 品牌商品結構數量: {}, 總筆數: {}",
                     content.size(), queryResult.getTotalElements());

            return RespRecycleWebsiteBrandProductQueryWrapper.builder()
                    .totalElements(queryResult.getTotalElements())
                    .content(content)
                    .build();
        }

        // 供應商邏輯
        log.info("執行一般供應商查詢邏輯");
        RecycleWebsiteQueryService.SupplierQueryResult queryResult = queryService.querySupplierDataWithPagination(brandId, productSeriesId, keyword, partnerCode, pageable);

        if (queryResult.isEmpty()) {
            log.info("供應商查詢結果為空，返回空結果");
            return RespRecycleWebsiteBrandProductQueryWrapper.builder()
                    .totalElements(0L)
                    .content(Collections.emptyList())
                    .build();
        }

        log.debug("供應商查詢結果 - 合作夥伴品牌數量: {}, 料件數量: {}, 總筆數: {}",
                  queryResult.getPartnerBrands().size(),
                  queryResult.getPartNumbers().size(),
                  queryResult.getTotalElements());

        List<RespRecycleWebsiteBrandProduct> content = mapper.buildSupplierBrandProductStructure(
                queryResult.getPartnerBrands(),
                queryResult.getBrandMap(),
                queryResult.getProductSeriesMap(),
                queryResult.getPartNumbers(),
                queryResult.getRecoveryDetailMap()
        );

        log.info("供應商資料結構建立完成 - 品牌商品結構數量: {}, 總筆數: {}",
                 content.size(), queryResult.getTotalElements());

        log.info("品牌與商品巢狀結構查詢完成 - partnerCode: {}, 最終結果數量: {}",
                 partnerCode, content.size());

        return RespRecycleWebsiteBrandProductQueryWrapper.builder()
                .totalElements(queryResult.getTotalElements())
                .content(content)
                .build();
    }

    /**
     * 點我估價 - 獲取料件的檢測題目
     *
     * @param partNumberId 料件ID
     * @return 料件估價回應包裝器，包含檢測題目、選項及扣款金額或加款金額
     */
    @Transactional(readOnly = true)
    public RespPartNumberEstimateWrapper getPartNumberEstimate(String partNumberId) {
        log.info("Getting estimate for part number ID: {}", partNumberId);

        // 1. 獲取料件實體
        PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(partNumberId);
        String productSeriesId = partNumber.getProductSeriesId();

        if (productSeriesId == null || productSeriesId.isEmpty()) {
            log.info("Part number {} has no associated product series, returning empty result", partNumberId);
            // 如果料件沒有關聯產品系列，返回空結果
            return recycleWebsiteHelper.createEmptyEstimateResult(partNumberId, partNumber.getPartNumberName(), partNumber.getProductName(), null, null, null);
        }

        // 2. 查找產品系列及其父系列的檢測群組 (使用遞迴方式)
        log.info("Searching for inspection group starting from product series ID: {}", productSeriesId);
        InspectionGroupProductSeriesEntity groupProductSeries = inspectionGroupHelper.findInspectionGroupForProductSeries(productSeriesId, 0);

        if (groupProductSeries == null) {
            log.info("No inspection group found for part number {} in any parent product series, returning empty result", partNumberId);
            // 如果沒有關聯的檢測群組，返回空結果
            return recycleWebsiteHelper.createEmptyEstimateResult(partNumberId, partNumber.getPartNumberName(), partNumber.getProductName(), null, null, null);
        }

        log.info("Found inspection group ID: {} for product series ID: {}",
                 groupProductSeries.getId().getInspectionGroupId(),
                 groupProductSeries.getId().getProductSeriesId());

        // 3. 獲取檢測群組ID
        String groupId = groupProductSeries.getId().getInspectionGroupId();

        // 3.1 獲取檢測群組資訊
        InspectionGroupEntity group = inspectionGroupRepository.findById(groupId).orElse(null);
        String groupCode = group != null ? group.getGroupCode() : null;
        String groupName = group != null ? group.getGroupName() : null;

        // 4. 查詢檢測群組關聯的所有問題
        List<InspectionGroupQuestionEntity> groupQuestions = inspectionGroupQuestionRepository.findByIdGroupId(groupId);

        // 過濾出啟用的問題關聯
        groupQuestions = groupQuestions.stream()
                .filter(InspectionGroupQuestionEntity::getEnabled)
                .toList();

        if (groupQuestions.isEmpty()) {
            // 如果沒有關聯的問題，返回空結果
            return recycleWebsiteHelper.createEmptyEstimateResult(partNumberId, partNumber.getPartNumberName(), partNumber.getProductName(), null, null, null);
        }

        // 5. 獲取所有問題ID
        List<String> questionIds = groupQuestions.stream()
                .map(entity -> entity.getId().getQuestionId())
                .distinct()
                .toList();

        // 6. 查詢這些問題的詳細資訊 (直接使用repository方法查詢啟用的SMARTMOBILE類型的問題)
        List<InspectionQuestionEntity> questions = inspectionQuestionRepository.findByQuestionIdInAndQuestionTypeAndEnabledIsTrueAndIsMultipleIsFalse(questionIds, QuestionType.SMARTMOBILE);

        if (questions.isEmpty()) {
            // 如果沒有 SMART MOBILE 類型的問題，返回空結果
            return recycleWebsiteHelper.createEmptyEstimateResult(partNumberId, partNumber.getPartNumberName(), partNumber.getProductName(), groupId, groupCode, groupName);
        }

        // 7. 獲取所有自定義金額設定
        List<ProductQuestionOptionAmountEntity> customAmounts = productQuestionOptionAmountRepository.findByPartNumberId(partNumberId);

        // 建立映射以便快速查找
        Map<String, ProductQuestionOptionAmountEntity> customAmountMap = new HashMap<>();
        for (ProductQuestionOptionAmountEntity amount : customAmounts) {
            customAmountMap.put(amount.getOptionId(), amount);
        }

        // 8. 構建回應模型
        List<RespPartNumberEstimateQuestion> questionModels = new ArrayList<>();

        for (InspectionQuestionEntity question : questions) {
            // 9. 查詢問題的選項
            List<InspectionQuestionOptionEntity> options = inspectionQuestionOptionRepository.findByQuestionIdAndEnabled(
                    question.getQuestionId(), true
            );

            if (options.isEmpty()) {
                continue; // 跳過沒有選項的問題
            }

            // 10. 構建選項模型
            List<RespPartNumberEstimateQuestionOption> optionModels = new ArrayList<>();

            for (InspectionQuestionOptionEntity option : options) {
                // TODO: 需要配合檢測題目選項結構調整
                // InspectionQuestionOptionEntity 已移除金額相關欄位，需要從子選項獲取金額資訊
                // 11. 查詢是否有自定義金額
                BigDecimal bonusAmount = BigDecimal.valueOf(0); // option.getBonusAmount(); // TODO: 需要從子選項獲取
                BigDecimal deductionAmount = BigDecimal.valueOf(0); // option.getDeductionAmount(); // TODO: 需要從子選項獲取

                // 檢查是否有針對此料件的自定義金額
                ProductQuestionOptionAmountEntity customAmount = customAmountMap.get(option.getOptionId());

                if (customAmount != null) {
                    // 如果有自定義金額，使用自定義金額
                    bonusAmount = customAmount.getBonusAmount();
                    deductionAmount = customAmount.getDeductionAmount(); // TODO: 已移除此欄位
                }

                // 構建選項模型
                // 處理範例圖片
                List<RespSampleImageModel> sampleImages = new ArrayList<>();
                if (option.getSampleImages() != null && !option.getSampleImages().isEmpty()) {
                    sampleImages = option.getSampleImages().stream()
                            .filter(image -> Boolean.TRUE.equals(image.getEnabled()))
                            .map(image -> {
                                RespSampleImageModel sampleImage = new RespSampleImageModel();
                                sampleImage.setSampleImageId(image.getSampleImageId());
                                sampleImage.setImageId(image.getImageId());
                                sampleImage.setSequence(image.getSequence());
                                sampleImage.setImagePath(fileHelper.getFilePath(image.getImageId()));
                                sampleImage.setImageName(fileHelper.getFileName(image.getImageId()));
                                return sampleImage;
                            })
                            .sorted(Comparator.comparing(RespSampleImageModel::getSequence))
                            .collect(Collectors.toList());
                }

                RespPartNumberEstimateQuestionOption optionModel = RespPartNumberEstimateQuestionOption.builder()
                        .optionId(option.getOptionId())
                        .sequence(option.getSequence())
                        .content(option.getContent())
//                        .calculationType(option.getCalculationType()) //todo
                        .bonusAmount(bonusAmount)
                        .deductionAmount(deductionAmount)
                        .isRequired(option.getIsRequired())
                        .coverImagePath(option.getCoverImageId() != null ? fileHelper.getFilePath(option.getCoverImageId()) : null)
                        .coverImageName(option.getCoverImageId() != null ? fileHelper.getFileName(option.getCoverImageId()) : null)
                        .sampleImages(sampleImages)
                        .hint(option.getHint())
                        .build();

                optionModels.add(optionModel);
            }

            // 按照 sequence 排序選項
            optionModels.sort(Comparator.comparing(RespPartNumberEstimateQuestionOption::getSequence));

            // 構建問題模型
            RespPartNumberEstimateQuestion questionModel = RespPartNumberEstimateQuestion.builder()
                    .questionId(question.getQuestionId())
                    .questionType(question.getQuestionType())
                    .questionCode(question.getQuestionCode())
                    .questionName(question.getQuestionName())
                    .isRequired(question.getIsRequired())
                    .isMultiple(question.getIsMultiple())
                    .options(optionModels)
                    .build();

            questionModels.add(questionModel);
        }

        // 12. 構建並返回最終回應
        return RespPartNumberEstimateWrapper.builder()
                .partNumberId(partNumberId)
                .partNumberName(partNumber.getPartNumberName())
                .productName(partNumber.getProductName())
                .groupId(groupId)
                .groupCode(groupCode)
                .groupName(groupName)
                .price(new BigDecimal("10000")) // todo
                .minPrice(new BigDecimal("1000")) // todo
                .questions(questionModels)
                .build();
    }


    /**
     * 根據測試結果建立檢測單
     *
     * @param reqModel    回收測試結果請求
     * @param partnerCode 供應商代碼
     * @return 檢測單資訊
     */
    @Transactional
    public RespRecycleTestResult createInspectionFormFromTest(ReqRecycleTest reqModel, String partnerCode) {
        log.info("根據測試結果建立檢測單，料件ID: {}, 供應商代碼: {}", reqModel.getPartNumberId(), partnerCode);

        // 1. 查詢料件資訊
        PartNumberEntity partNumber = partNumberHelper.findPartNumberByIdOrThrow(reqModel.getPartNumberId());

        // 2. 建立檢測單
        InspectionFormEntity inspectionForm = new InspectionFormEntity();
        inspectionForm.setInspectionFormCode(inspectionFormHelper.generateInspectionFormCode());
        inspectionForm.setInspectionDate(System.currentTimeMillis());
        inspectionForm.setInspectionStatus(InspectionStatus.NEW.getCode());
        inspectionForm.setInspectionFormType(InspectionFormType.RECYCLING_WEBSITE);
        inspectionForm.setRecyclePrice(reqModel.getRecyclePrice());
        inspectionForm.setRecycleQuote(reqModel.getRecycleQuote());
        inspectionForm.setPartNumberId(reqModel.getPartNumberId());
        inspectionForm.setPartNumberCode(partNumber.getPartNumberCode());
        inspectionForm.setCreateDatetime(System.currentTimeMillis());
        inspectionForm.setInspectionGroupId(reqModel.getGroupId());
        inspectionForm.setInspectionGroupCode(reqModel.getGroupCode());
        inspectionForm.setInspectionGroupName(reqModel.getGroupName());

        // 3. 根據賣家資訊類型處理不同的情況
        if (reqModel.getSmSellerInformation() != null) {
            // 如果是 SM 賣家資訊，直接使用 smSellerInformation
            ReqRecycleTest.SmSellerInformation smInfo = reqModel.getSmSellerInformation();

            // 設置賣家資訊
            inspectionForm.setSellerName(smInfo.getSellerName());
            inspectionForm.setSellerIdNumber(smInfo.getIdNumber());
            inspectionForm.setSellerContactPhone(smInfo.getContactPhone());
            inspectionForm.setSellerContactAddress(smInfo.getContactAddress());

            // 設置退貨收件人資訊
            inspectionForm.setReturnRecipientName(smInfo.getReturnRecipientName());
            inspectionForm.setReturnRecipientContactPhone(smInfo.getReturnRecipientPhone());
            inspectionForm.setReturnRecipientContactAddress(smInfo.getReturnRecipientAddress());

            // 如果有提供 idNumber，則建立或更新 SellerInformationEntity
            if (smInfo.getIdNumber() != null && !smInfo.getIdNumber().isEmpty()) {
                // 使用 sellerInformationHelper 創建或更新賣家資訊
                String sellerId = sellerInformationHelper.createOrUpdateSeller(smInfo.getSellerName(), smInfo.getIdNumber(), smInfo.getContactPhone(), smInfo.getContactAddress());

                // 將賣家ID寫入檢測單
                inspectionForm.setSellerId(sellerId);
            }
        } else if (reqModel.getPartnerSellerInformation() != null) {
            // 如果是 partner 賣家資訊，需要查詢供應商資訊
            String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);
            PartnerProfileEntity partnerProfile = partnerHelper.findPartnerProfileByPartnerIdOrThrow(partnerId);

            // 設置供應商資訊
            inspectionForm.setPartnerCode(partnerCode);
            inspectionForm.setPartnerName(partnerProfile.getPartnerZhName());

            // 設置賣家資訊
            ReqRecycleTest.PartnerSellerInformation partnerInfo = reqModel.getPartnerSellerInformation();
            inspectionForm.setSellerName(partnerInfo.getSellerName());
            inspectionForm.setSellerContactPhone(partnerInfo.getContactPhone());
            inspectionForm.setPartnerStore(partnerInfo.getStoreName());

        } else {
            // 如果兩種賣家資訊都沒有提供，拋出異常
            throw new IllegalArgumentException("必須提供賣家資訊（SM 或 Partner）");
        }

        // 4. 儲存檢測單
        InspectionFormEntity savedForm = inspectionFormRepository.saveAndFlush(inspectionForm);

        // 5. 處理檢測明細和結果
        processTestResults(savedForm, reqModel.getTestResults());

        if (Boolean.TRUE.equals(reqModel.getIsHomePickup()) && reqModel.getSmSellerInformation() != null) {
            // 處理電子簽名檔上傳
            String signatureFileId = null;
            String identificationFileId = null;
            ReqRecycleTest.SmSellerInformation smInfo = reqModel.getSmSellerInformation();

            if (smInfo.getSignatureFile() != null && !smInfo.getSignatureFile().isEmpty()) {
                try {
                    // 使用與回收訂單相同的邏輯處理電子簽名檔
                    FileEntity fileEntity = fileManagementHelper.createNewFile(smInfo.getSignatureFile(), StringUtils.EMPTY);
                    signatureFileId = fileEntity.getFileId();
                    log.info("電子簽名檔上傳成功，檔案ID: {}", signatureFileId);
                } catch (Exception e) {
                    log.error("電子簽名檔上傳失敗", e);
                    throw new RuntimeException(messageHelper.localizedMessage("error.signature.upload.failed", e.getMessage()), e);
                }
            }

            // 處理證件照上傳
            if (smInfo.getIdentificationFile() != null && !smInfo.getIdentificationFile().isEmpty()) {
                try {
                    FileEntity fileEntity = fileManagementHelper.createNewFile(smInfo.getIdentificationFile(), StringUtils.EMPTY);
                    identificationFileId = fileEntity.getFileId();
                    log.info("證件照上傳成功，檔案ID: {}", identificationFileId);
                } catch (Exception e) {
                    log.error("證件照上傳失敗", e);
                    throw new RuntimeException(messageHelper.localizedMessage("error.identification.upload.failed", e.getMessage()), e);
                }
            }

            // 建立回收訂單（傳遞電子簽名檔ID和證件照ID）
            String recycleOrderId = recycleOrderHelper.createRecycleOrderFromInspectionForm(savedForm, signatureFileId, identificationFileId);
            inspectionForm.setRecycleOrderId(recycleOrderId);
            inspectionForm.setInspectionStatus(InspectionStatus.ESTABLISHED.getCode());
            inspectionForm.setLastModifyDatetime(System.currentTimeMillis());

            // 產生買賣切結書
            String agreementFileId = recycleOrderHelper.generateTransactionAgreement(savedForm, transactionAgreementHelper);
            String agreementFileName = getFileNameSafely(agreementFileId);
            savedForm.setTransactionAgreementId(agreementFileId);
            savedForm.setTransactionAgreementCode(agreementFileName);

            inspectionFormRepository.save(savedForm);
        }

        // 7. 返回檢測單號和其他相關資訊
        return new RespRecycleTestResult(savedForm.getInspectionFormCode());
    }

    /**
     * 處理測試結果，建立檢測明細和結果
     */
    private void processTestResults(InspectionFormEntity inspectionForm, List<ReqRecycleTest.TestResult> testResults) {
        for (ReqRecycleTest.TestResult result : testResults) {

            // 3. 建立檢測明細
            InspectionDetailEntity detail = new InspectionDetailEntity();
            detail.setInspectionFormId(inspectionForm.getInspectionFormId());
            detail.setInspectionType(result.getQuestionType().name());
            detail.setInspectionCode(result.getQuestionCode());
            detail.setInspectionQuestion(result.getQuestionName());

            detail.setCreateDatetime(System.currentTimeMillis());

            InspectionDetailEntity savedDetail = inspectionDetailRepository.save(detail);

            // 4. 處理選項結果
            for (ReqRecycleTest.Option option : result.getOptions()) {
                // 處理所有選項，不管是否被選中
                InspectionResultEntity resultEntity = new InspectionResultEntity();
                resultEntity.setInspectionDetailId(savedDetail.getInspectionDetailId());
                resultEntity.setInspectionOption(option.getContent());
                // 根據選項的選中狀態設置 isSelected
                resultEntity.setIsSelected(option.getSelected() != null && option.getSelected());
                resultEntity.setCalculationType(option.getCalculationType());
                resultEntity.setBonusAmount(option.getBonusAmount());
                resultEntity.setDeductionAmount(option.getDeductionAmount());

                resultEntity.setCreateDatetime(System.currentTimeMillis());

                inspectionResultRepository.save(resultEntity);
            }
        }
    }

    /**
     * 獲取供應商關係企業扁平結構，包含上下節點
     *
     * @param partnerCode 供應商代碼
     * @return 供應商關係企業扁平列表
     */
    public List<RespStoreDropdown> getStoreDropdownList(String partnerCode) {
        // 檢查是否為直接商店合作夥伴
        if (partnerHelper.isSmDirectStore(partnerCode)) {
            return companyRepository.findByIsDirectStoreIsTrue().stream()
                    .map(entity -> {
                        RespStoreDropdown storeDropdown = new RespStoreDropdown();
                        storeDropdown.setStoreId(entity.getCompanyId());
                        storeDropdown.setStoreName(entity.getZhName());
                        storeDropdown.setStoreCode(entity.getCompanyCode());
                        return storeDropdown;
                    })
                    .collect(Collectors.toList());
        }

        // 1. 根據供應商代碼查找供應商
        String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);
        PartnerProfileEntity partner = partnerHelper.findPartnerProfileByPartnerIdOrThrow(partnerId);

        // 2. 獲取所有啟用的供應商
        List<PartnerProfileEntity> allPartners = partnerProfileRepository.findByEnabledTrueOrderByPartnerCodeAscPartnerZhNameAsc();

        // 3. 找到根節點（最上層的父節點）
        PartnerProfileEntity rootPartner = partnerTreeHelper.findRootPartner(partner, allPartners);

        // 4. 收集所有相關的供應商（包括根節點及其所有子節點）
        List<PartnerProfileEntity> relatedPartners = partnerTreeHelper.collectRelatedPartners(rootPartner, allPartners);

        // 5. 將供應商實體轉換為扁平結構響應對象
        return relatedPartners.stream()
                .map(recycleWebsiteHelper::mapToStoreDropdown)
                .toList();
    }

    /**
     * 建立回收網站產品系列樹狀結構
     *
     * @param parentNodes 父節點
     * @param allSeries   所有產品系列實體
     * @return 樹狀節點列表
     */
    private List<RespRecycleWebsiteProductSeries> buildRecycleWebsiteProductSeriesTree(
            List<PartNumberProductSeriesEntity> parentNodes,
            List<PartNumberProductSeriesEntity> allSeries) {

        return parentNodes.stream()
                .map(parent -> {
                    RespRecycleWebsiteProductSeries node = mapToRespRecycleWebsiteProductSeries(parent);

                    // 找出當前節點的所有子節點
                    List<PartNumberProductSeriesEntity> children = allSeries.stream()
                            .filter(series -> parent.getProductSeriesId().equals(series.getProductSeriesParentId()))
                            .toList();

                    // 遞迴建立子樹
                    if (!children.isEmpty()) {
                        node.setChildren(buildRecycleWebsiteProductSeriesTree(children, allSeries));
                    }

                    return node;
                })
                .toList();
    }

    /**
     * 將 PartNumberProductSeriesEntity 轉換為 RespRecycleWebsiteProductSeries
     *
     * @param entity 產品系列實體
     * @return 回收網站產品系列回應
     */
    private RespRecycleWebsiteProductSeries mapToRespRecycleWebsiteProductSeries(PartNumberProductSeriesEntity entity) {
        return RespRecycleWebsiteProductSeries.builder()
                .productSeriesId(entity.getProductSeriesId())
                .productSeriesParentId(entity.getProductSeriesParentId())
                .productSeriesName(entity.getProductSeriesName())
                .enabled(entity.getEnabled())
                .build();
    }

    /**
     * 安全地獲取檔案名稱，如果檔案不存在則返回null
     *
     * @param fileId 檔案ID
     * @return 檔案名稱，檔案不存在時返回null
     */
    private String getFileNameSafely(String fileId) {
        if (StringUtils.isBlank(fileId)) {
            return StringUtils.EMPTY;
        }

        try {
            return fileHelper.getFileName(fileId);
        } catch (Exception e) {
            log.warn("獲取檔案名稱失敗，檔案ID: {}, 錯誤: {}", fileId, e.getMessage());
            return StringUtils.EMPTY;
        }
    }
}
