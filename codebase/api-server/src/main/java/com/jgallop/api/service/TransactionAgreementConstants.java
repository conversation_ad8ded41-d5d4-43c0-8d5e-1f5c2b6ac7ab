package com.jgallop.api.service;

import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;

/**
 * 買賣切結書PDF生成相關常數定義
 * 集中管理所有硬編碼值，確保一致性和易維護性
 */
public final class TransactionAgreementConstants {

    private TransactionAgreementConstants() {
        // 工具類，禁止實例化
    }

    /**
     * PDF顏色常數
     */
    public static final class Colors {
        public static final Color BLUE_HEADER = new DeviceRgb(68, 114, 196); // #4472c4
        public static final Color YELLOW_HIGHLIGHT = new DeviceRgb(255, 192, 0); // #ffc000
        public static final Color LIGHT_YELLOW = new DeviceRgb(249, 238, 205); // #f9eecd
        public static final Color LIGHT_BLUE = new DeviceRgb(207, 224, 255); // #cfe0ff
        public static final Color WHITE = new DeviceRgb(255, 255, 255);
        public static final Color BLACK = new DeviceRgb(0, 0, 0);
        public static final Color BORDER_GRAY = new DeviceRgb(51, 51, 51); // #333
        public static final Color LOGO_BLUE = new DeviceRgb(74, 144, 226);
        public static final Color TEXT_GRAY = new DeviceRgb(102, 102, 102); // #666
        public static final Color LIGHT_GRAY = new DeviceRgb(153, 153, 153); // #999
        public static final Color DATE_RED = new DeviceRgb(231, 76, 60);
    }

    /**
     * 字型大小常數
     */
    public static final class FontSizes {
        public static final float TITLE = 20f;
        public static final float LOGO_TEXT = 16f;
        public static final float LOGO_ICON = 12f;
        public static final float HEADER = 10f;
        public static final float LABEL = 9f;
        public static final float DATA = 11f;
        public static final float SMALL_DATA = 9f;
        public static final float HIGHLIGHT = 8f;
        public static final float FOOTER = 9f;
        public static final float DATE = 11f;
        public static final float LOGO_SUBTITLE = 9f;
        public static final float LEGAL_TERMS = 7f;
        public static final float BARCODE_TEXT = 11f;
        public static final float BARCODE_FALLBACK = 16f;
    }

    /**
     * 間距和尺寸常數
     */
    public static final class Dimensions {
        public static final float PAGE_MARGIN = 15f;
        public static final float LOGO_SIZE = 35f;
        public static final float LOGO_BORDER_RADIUS = 8f;
        public static final float BORDER_WIDTH = 1f;
        public static final float CELL_PADDING = 2f;
        public static final float LARGE_CELL_PADDING = 4f;
        public static final float BARCODE_HEIGHT = 28f;
        public static final float SIGNATURE_MAX_WIDTH = 100f;
        public static final float SIGNATURE_MAX_HEIGHT = 50f;
        public static final int BARCODE_WIDTH = 200;
        public static final int BARCODE_HEIGHT_INT = 50;
    }

    /**
     * 公司資訊常數
     */
    public static final class CompanyInfo {
        public static final String COMPANY_NAME = "創宇數位科技有限公司";
        public static final String COMPANY_TAX_ID = "25166891";
        public static final String COMPANY_ADDRESS = "桃園市中壢區中山東路二段546號";
        public static final String COMPANY_REPRESENTATIVE = "莊涵凱";
        public static final String LOGO_TEXT_SMART = "";
        public static final String LOGO_TEXT_MOBILE = "";
        public static final String LOGO_SUBTITLE = "";
        public static final String LOGO_ICON_TEXT = "SM";
        // 動態產生於 TransactionAgreementHelper，這裡保留基底文案以便他處使用
        public static final String COPYRIGHT = "創宇數位科技 版權所有，侵權必究";
    }

    /**
     * 表格標題常數
     */
    public static final class TableHeaders {
        public static final String PRODUCT_CONTENT = "◆ 商品內容 ◆";
        public static final String PERSONAL_TRADE_DECLARATION = "◆ 個人一時貿易申報表 ◆";
        public static final String SELLER_DATA = "◆ 賣方立書人資料 ◆";
        public static final String DECLARATION_UNIT = "申報單位";
        public static final String SCAN_NUMBER = "掃描編號";
        public static final String INSPECTION_ITEMS = "買務項目/檢測項目：檢測結果";
    }

    /**
     * 表格標籤常數
     */
    public static final class TableLabels {
        public static final String STORE_NAME = "分店名稱";
        public static final String STAFF_MEMBER = "承辦人員";
        public static final String PRODUCT_NAME = "商品名稱";
        public static final String IMEI = "IMEI";
        public static final String RECYCLE_AMOUNT = "回收金額";
        public static final String NAME = "姓名";
        public static final String ID_NUMBER = "身分證字號";
        public static final String CONTACT_PHONE = "聯絡電話";
        public static final String ADDRESS = "地址";
        public static final String DECLARATION_TAX_ID = "申報單位統編";
        public static final String INCOME_YEAR = "所得給付年度";
        public static final String FORMAT_CODE = "格式代號及所得類別";
        public static final String FORM_NUMBER = "製單編號";
        public static final String AUDIT_AUTHORITY = "稽核機關";
        public static final String INCOME_PERSON_NAME = "所得人姓名";
        public static final String INCOME_PERSON_TAX_ID = "所得人統編";
        public static final String INCOME_PERSON_ADDRESS = "所得人地址";
        public static final String PURCHASE_DATE = "買受日期";
        public static final String PURCHASE_REASON = "買受原因";
        public static final String PURCHASE_GOODS_NAME = "買受貨物名稱";
        public static final String UNIT_PRICE = "單價";
        public static final String QUANTITY = "數量";
        public static final String AMOUNT = "金額";
        public static final String TOTAL = "合計";
        public static final String COMPANY_NAME_LABEL = "名稱";
        public static final String COMPANY_ADDRESS_LABEL = "地址";
        public static final String REPRESENTATIVE = "負責人";
    }

    /**
     * 預設值常數
     */
    public static final class DefaultValues {
        public static final String NORMAL_INSPECTION_RESULT = "正常";
        public static final String PURCHASE_REASON_VALUE = "進貨";
        public static final String INCOME_TYPE_CODE = "71營利所得";
        public static final String QUANTITY_VALUE = "1";
        public static final String PRICE_PREFIX = "NTD ";
        public static final String UNKNOWN_PRODUCT = "未知產品";
        public static final String BARCODE_FALLBACK_TEXT = "||||| |||| ||||| ||||";
        public static final String BARCODE_DEFAULT_VALUE = "N/A";
        public static final String SIGNATURE_LOAD_FAILED = "電子簽名載入失敗";
    }

    /**
     * 日期格式常數
     */
    public static final class DateFormats {
        public static final String CHINESE_DATE = "yyyy年MM月dd日";
        public static final String STANDARD_DATE = "yyyy-MM-dd";
        public static final String PRINT_TIME = "yyyy/MM/dd HH:mm";
    }

    /**
     * 表格佈局常數
     */
    public static final class TableLayouts {
        public static final float[] HEADER_LAYOUT = {1, 2, 1};
        public static final float[] MAIN_TABLE_LAYOUT = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
        public static final float[] SELLER_TABLE_LAYOUT = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1};
        public static final float[] INSPECTION_TABLE_LAYOUT = {1, 1};
        public static final float[] FOOTER_LAYOUT = {1, 1};
        public static final float[] LOGO_LAYOUT = {1, 3};
        public static final float[] TERMS_LAYOUT = {4, 1};
        public static final float[] BARCODE_LAYOUT = {1}; // 單列佈局，條碼圖片和文字垂直排列
    }

    /**
     * 行間距常數
     */
    public static final class LineSpacing {
        public static final float TIGHT = 0.8f;
        public static final float NORMAL = 0.9f;
        public static final float STANDARD = 1.0f;
    }

    /**
     * 邊距常數
     */
    public static final class Margins {
        public static final float TITLE_BOTTOM = 8f;
        public static final float SECTION_BOTTOM = 5f;
        public static final float PARAGRAPH_BOTTOM = 2f;
        public static final float LOGO_PADDING_LEFT = 6f;
    }
}
