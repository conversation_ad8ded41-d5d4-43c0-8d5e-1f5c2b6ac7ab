package com.jgallop.api.service;

import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.layout.properties.BorderRadius;
import org.springframework.core.io.ClassPathResource;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.io.image.ImageDataFactory;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.oned.Code128Writer;
import com.jgallop.api.entity.InspectionDetailEntity;
import com.jgallop.api.entity.InspectionFormEntity;
import com.jgallop.api.entity.InspectionResultEntity;
import com.jgallop.api.entity.RecycleOrderEntity;
import com.jgallop.api.entity.SellerInformationEntity;
import com.jgallop.api.repository.InspectionDetailRepository;
import com.jgallop.api.repository.InspectionResultRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import com.jgallop.hfs.service.FileManagementHelper;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import org.apache.commons.lang3.StringUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.text.NumberFormat;

import static com.jgallop.api.service.TransactionAgreementConstants.*;
import static com.jgallop.api.service.LegalTermsConstants.*;

/**
 * 買賣切結書PDF生成輔助類
 * 負責生成回收訂單的買賣切結書PDF文檔，包含：
 * - 商品資訊和個人申報表
 * - 賣方資訊
 * - 法律聲明條款
 * - 格式化的表格和內容
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionAgreementHelper {

    private final ProductInfoHelper productInfoHelper;
    private final ProductSeriesHelper productSeriesHelper;
    private final InspectionDetailRepository inspectionDetailRepository;
    private final InspectionResultRepository inspectionResultRepository;
    private final RecycleOrderQueryHelper recycleOrderQueryHelper;
    private final SellerInfomationHelper sellerInfomationHelper;
    private final SystemAccountHelper systemAccountHelper;
    private final FileManagementHelper fileManagementHelper;

    // 字型檔案路徑
    private static final String CHINESE_FONT_PATH = "fonts/msjh.ttf";

    /**
     * 載入中文字型
     *
     * @return PdfFont 字型物件
     * @throws IOException 如果字型載入失敗
     */
    private PdfFont loadChineseFont() throws IOException {
        try {
            ClassPathResource fontResource = new ClassPathResource(CHINESE_FONT_PATH);
            if (fontResource.exists()) {
                log.debug("載入中文字型檔案: {}", CHINESE_FONT_PATH);
                byte[] fontBytes = fontResource.getInputStream().readAllBytes();
                FontProgram fontProgram = FontProgramFactory.createFont(fontBytes);
                return PdfFontFactory.createFont(fontProgram, "Identity-H");
            } else {
                log.warn("中文字型檔案不存在: {}，使用預設字型", CHINESE_FONT_PATH);
                return createFallbackFont();
            }
        } catch (Exception e) {
            log.error("載入中文字型失敗: {}，使用預設字型", CHINESE_FONT_PATH, e);
            return createFallbackFont();
        }
    }

    /**
     * 創建降級字型
     *
     * @return 預設字型
     * @throws IOException 如果字型創建失敗
     */
    private PdfFont createFallbackFont() throws IOException {
        return PdfFontFactory.createFont(StandardFonts.HELVETICA);
    }

    /**
     * 生成條碼圖片
     *
     * @param text   條碼內容
     * @param width  條碼寬度
     * @param height 條碼高度
     * @return 條碼圖片的字節數組
     * @throws IOException     如果圖片轉換失敗
     */
    private byte[] generateBarcodeImage(String text, int width, int height) throws IOException {
        String barcodeText = (text == null || text.trim().isEmpty()) ? DefaultValues.BARCODE_DEFAULT_VALUE : text;

        Code128Writer barcodeWriter = new Code128Writer();
        BitMatrix bitMatrix = barcodeWriter.encode(barcodeText, BarcodeFormat.CODE_128, width, height);
        BufferedImage barcodeImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

        return convertImageToByteArray(barcodeImage);
    }

    /**
     * 將圖片轉換為字節數組
     *
     * @param image 圖片物件
     * @return 字節數組
     * @throws IOException 如果轉換失敗
     */
    private byte[] convertImageToByteArray(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        javax.imageio.ImageIO.write(image, "PNG", baos);
        return baos.toByteArray();
    }

    /**
     * 從回收單ID生成買賣切結書PDF
     *
     * @param recycleOrderId 回收單ID
     * @return PDF字節數組
     * @throws IOException 如果PDF生成失敗
     */
    public byte[] generatePdfFromRecycleOrder(String recycleOrderId) throws IOException {
        log.debug("開始從回收單生成買賣切結書PDF，回收單ID: {}", recycleOrderId);

        // 查詢回收單
        RecycleOrderEntity recycleOrder = recycleOrderQueryHelper.findRecycleOrderByIdOrElseThrow(recycleOrderId);

        // 獲取關聯的檢測單列表
        List<InspectionFormEntity> inspectionForms = recycleOrder.getInspectionForms();
        if (inspectionForms == null || inspectionForms.isEmpty()) {
            throw new IllegalStateException("回收單中沒有關聯的檢測單，無法生成買賣切結書");
        }

        // 使用第一個檢測單作為主要資料來源，並整合回收單的賣家資訊
        InspectionFormEntity primaryInspectionForm = inspectionForms.getFirst();

        // 整合完整的資料
        TransactionAgreementData agreementData = buildTransactionAgreementData(recycleOrder, primaryInspectionForm, inspectionForms);

        return generatePdfFromData(agreementData);
    }

    /**
     * 生成買賣切結書PDF（原有方法，保持向後兼容）
     *
     * @param inspectionForm 檢測單實體
     * @return PDF字節數組
     * @throws IOException 如果PDF生成失敗
     */
    public byte[] generatePdf(InspectionFormEntity inspectionForm) throws IOException {
        log.debug("開始生成買賣切結書PDF，檢測單ID: {}", inspectionForm.getInspectionFormId());

        // 將 InspectionFormEntity 轉換為 TransactionAgreementData
        TransactionAgreementData agreementData = buildTransactionAgreementData(inspectionForm);

        return generatePdfFromData(agreementData);
    }

    /**
     * 添加PDF標題
     *
     * @param document PDF文檔
     */
    private void addTitle(Document document) {
        Table headerTable = createHeaderTable();
        Cell logoCell = createLogoCell();
        Cell titleCell = createTitleCell();
        Cell dateCell = createDateCell();

        headerTable.addCell(logoCell);
        headerTable.addCell(titleCell);
        headerTable.addCell(dateCell);

        document.add(headerTable);
        document.add(new Paragraph().setMarginBottom(Margins.TITLE_BOTTOM));
    }

    /**
     * 創建標題表格
     */
    private Table createHeaderTable() {
        return new Table(UnitValue.createPercentArray(TableLayouts.HEADER_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));
    }

    /**
     * 創建Logo單元格
     */
    private Cell createLogoCell() {
        Table logoTable = new Table(UnitValue.createPercentArray(TableLayouts.LOGO_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        Cell logoIconCell = createLogoIconCell();
        Cell logoTextCell = createLogoTextCell();

        logoTable.addCell(logoIconCell);
        logoTable.addCell(logoTextCell);

        return new Cell()
                .add(logoTable)
                .setBorder(null)
                .setTextAlignment(TextAlignment.LEFT);
    }

    /**
     * 創建Logo圖示單元格
     */
    private Cell createLogoIconCell() {
        try {
            // 使用新的 sm_logo.png 圖片
            String logoPath = "/images/sm_logo.png";
            byte[] logoBytes = getClass().getResourceAsStream(logoPath).readAllBytes();
            Image logoImage = new Image(ImageDataFactory.create(logoBytes));

            // 設置logo圖片尺寸為 166*42 比例
            float logoWidth = 166f;
            float logoHeight = 42f;
            logoImage.setWidth(UnitValue.createPointValue(logoWidth));
            logoImage.setHeight(UnitValue.createPointValue(logoHeight));

            return new Cell()
                    .add(logoImage)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setWidth(UnitValue.createPointValue(logoWidth))
                    .setHeight(UnitValue.createPointValue(logoHeight))
                    .setPadding(0)
                    .setBorder(null);
        } catch (Exception e) {
            log.warn("Logo圖片載入失敗，使用文字替代: {}", e.getMessage());
            // 降級處理：使用原有的文字logo
            return new Cell()
                    .add(new Paragraph(CompanyInfo.LOGO_ICON_TEXT)
                            .setFontSize(FontSizes.LOGO_ICON)
                            .setBold()
                            .setFontColor(Colors.WHITE)
                            .setMultipliedLeading(LineSpacing.STANDARD))
                    .setBackgroundColor(Colors.LOGO_BLUE)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setWidth(UnitValue.createPointValue(Dimensions.LOGO_SIZE))
                    .setHeight(UnitValue.createPointValue(Dimensions.LOGO_SIZE))
                    .setPadding(0)
                    .setBorderRadius(new BorderRadius(Dimensions.LOGO_BORDER_RADIUS))
                    .setBorder(null);
        }
    }

    /**
     * 創建Logo文字單元格
     */
    private Cell createLogoTextCell() {
        return new Cell()
                .add(new Paragraph().setMultipliedLeading(LineSpacing.NORMAL)
                        .add(new Text(CompanyInfo.LOGO_TEXT_SMART)
                                .setFontSize(FontSizes.LOGO_TEXT)
                                .setBold()
                                .setFontColor(Colors.TEXT_GRAY))
                        .add(new Text(CompanyInfo.LOGO_TEXT_MOBILE)
                                .setFontSize(FontSizes.LOGO_TEXT)
                                .setBold()
                                .setFontColor(Colors.BLUE_HEADER)))
                .add(new Paragraph(CompanyInfo.LOGO_SUBTITLE)
                        .setFontSize(FontSizes.LOGO_SUBTITLE)
                        .setFontColor(Colors.LIGHT_GRAY)
                        .setMultipliedLeading(LineSpacing.TIGHT))
                .setBorder(null)
                .setPaddingLeft(Margins.LOGO_PADDING_LEFT)
                .setVerticalAlignment(VerticalAlignment.MIDDLE);
    }

    /**
     * 創建標題單元格
     */
    private Cell createTitleCell() {
        return new Cell()
                .add(new Paragraph("商品買賣切結書")
                        .setFontSize(FontSizes.TITLE)
                        .setBold()
                        .setFontColor(Colors.BLACK)
                        .setMultipliedLeading(LineSpacing.STANDARD))
                .setBorder(null)
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE);
    }

    /**
     * 創建日期單元格
     */
    private Cell createDateCell() {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DateFormats.CHINESE_DATE));
        return new Cell()
                .add(new Paragraph(currentDate)
                        .setFontSize(FontSizes.DATE)
                        .setBold()
                        .setFontColor(Colors.DATE_RED)
                        .setMultipliedLeading(LineSpacing.STANDARD))
                .setBorder(null)
                .setTextAlignment(TextAlignment.RIGHT)
                .setVerticalAlignment(VerticalAlignment.MIDDLE);
    }

    /**
     * 統一的單元格建造者類
     * 簡化單元格創建邏輯，減少重複程式碼
     */
    private static class CellBuilder {
        private final Cell cell;

        private CellBuilder(int rowspan, int colspan) {
            this.cell = new Cell(rowspan, colspan);
        }

        public static CellBuilder create() {
            return new CellBuilder(1, 1);
        }

        public static CellBuilder create(int colspan) {
            return new CellBuilder(1, colspan);
        }

        public static CellBuilder create(int rowspan, int colspan) {
            return new CellBuilder(rowspan, colspan);
        }

        public CellBuilder addText(String text, float fontSize, Color fontColor, boolean bold) {
            Paragraph paragraph = new Paragraph(text != null ? text : "")
                    .setFontSize(fontSize)
                    .setFontColor(fontColor)
                    .setMultipliedLeading(LineSpacing.STANDARD);
            if (bold) {
                paragraph.setBold();
            }
            cell.add(paragraph);
            return this;
        }

        public CellBuilder addText(String text, float fontSize, Color fontColor) {
            return addText(text, fontSize, fontColor, false);
        }

        public CellBuilder setBackground(Color backgroundColor) {
            cell.setBackgroundColor(backgroundColor);
            return this;
        }

        public CellBuilder setAlignment(TextAlignment textAlignment, VerticalAlignment verticalAlignment) {
            cell.setTextAlignment(textAlignment);
            cell.setVerticalAlignment(verticalAlignment);
            return this;
        }

        public CellBuilder setBorder(Color borderColor, float borderWidth) {
            cell.setBorder(new SolidBorder(borderColor, borderWidth));
            return this;
        }

        public CellBuilder setBorder(SolidBorder border) {
            cell.setBorder(border);
            return this;
        }

        public CellBuilder removeBorder() {
            cell.setBorder(null);
            return this;
        }

        public CellBuilder setPadding(float padding) {
            cell.setPadding(padding);
            return this;
        }

        public CellBuilder addElement(com.itextpdf.layout.element.IBlockElement element) {
            cell.add(element);
            return this;
        }

        public Cell build() {
            return cell;
        }
    }

    /**
     * 獲取檢測結果
     *
     * @param inspectionDetail 檢測明細
     * @return 檢測結果字串
     */
    private String getInspectionResult(InspectionDetailEntity inspectionDetail) {
        try {
            List<InspectionResultEntity> results = inspectionResultRepository
                    .findByInspectionDetailId(inspectionDetail.getInspectionDetailId());

            if (results.isEmpty()) {
                return DefaultValues.NORMAL_INSPECTION_RESULT;
            }

            return results.stream()
                    .filter(result -> Boolean.TRUE.equals(result.getIsSelected()))
                    .findFirst()
                    .map(InspectionResultEntity::getInspectionOption)
                    .orElse(DefaultValues.NORMAL_INSPECTION_RESULT);
        } catch (Exception e) {
            log.warn("無法獲取檢測結果，檢測明細ID: {}", inspectionDetail.getInspectionDetailId(), e);
            return DefaultValues.NORMAL_INSPECTION_RESULT;
        }
    }

    /**
     * 添加法律聲明
     *
     * @param document PDF文檔
     * @param data     買賣切結書資料
     */
    private void addLegalTerms(Document document, TransactionAgreementData data) {
        document.add(new Paragraph().setMarginBottom(Margins.SECTION_BOTTOM));

        Table termsTable = new Table(UnitValue.createPercentArray(TableLayouts.TERMS_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        // 添加標題行
        termsTable.addCell(createLegalTermsHeaderCell(data.sellerName));
        termsTable.addCell(createSignatureHeaderCell());

        // 添加內容行
        termsTable.addCell(createLegalTermsContentCell());
        termsTable.addCell(createSignatureCell(data));

        document.add(termsTable);
    }

    /**
     * 創建法律條文標題單元格
     *
     * @param sellerName 賣方姓名，用於動態替換標題中的佔位符
     */
    private Cell createLegalTermsHeaderCell(String sellerName) {
        // 動態替換標題中的賣方姓名
        String dynamicHeader = String.format(Titles.MAIN_HEADER, sellerName != null ? sellerName : "");

        return CellBuilder.create()
                .addText(dynamicHeader, FontSizes.HEADER, Colors.WHITE, true)
                .setBackground(Colors.BLUE_HEADER)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setPadding(Dimensions.LARGE_CELL_PADDING)
                .build();
    }

    /**
     * 創建簽名標題單元格
     */
    private Cell createSignatureHeaderCell() {
        return CellBuilder.create()
                .addText(Titles.SIGNATURE_HEADER, FontSizes.HEADER, Colors.WHITE, true)
                .setBackground(Colors.BLUE_HEADER)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setPadding(Dimensions.LARGE_CELL_PADDING)
                .build();
    }

    /**
     * 創建法律條文內容單元格
     */
    private Cell createLegalTermsContentCell() {
        Cell contentCell = new Cell()
                .setPadding(Dimensions.LARGE_CELL_PADDING)
                .setVerticalAlignment(VerticalAlignment.TOP);

        contentCell.add(createLegalTermsParagraph(Titles.PRODUCT_SOURCE_GUARANTEE, Contents.PRODUCT_SOURCE_GUARANTEE_TEXT));
        contentCell.add(createLegalTermsParagraph(Titles.PERSONAL_DATA_COLLECTION, Contents.PERSONAL_DATA_COLLECTION_TEXT));
        contentCell.add(createLegalTermsParagraph(Titles.MODIFICATION_CORRECTION, Contents.MODIFICATION_CORRECTION_TEXT));
        contentCell.add(createLegalTermsParagraph(Titles.MEMBER_AGREEMENT, Contents.MEMBER_AGREEMENT_TEXT));
        contentCell.add(createLegalTermsParagraph(Titles.INCOME_DECLARATION, Contents.INCOME_DECLARATION_TEXT));
        contentCell.add(createLegalTermsParagraph(Titles.OTHER_TERMS, Contents.OTHER_TERMS_TEXT));

        return contentCell;
    }

    /**
     * 創建法律條文段落
     */
    private Paragraph createLegalTermsParagraph(String title, String content) {
        Paragraph paragraph = new Paragraph()
                .setFontSize(FontSizes.LEGAL_TERMS)
                .setMultipliedLeading(LineSpacing.NORMAL)
                .setMarginBottom(Margins.PARAGRAPH_BOTTOM);

        paragraph.add(new Text(title + "\n").setBold());
        paragraph.add(content);

        return paragraph;
    }

    /**
     * 創建簽名單元格
     */
    private Cell createSignatureCell(TransactionAgreementData data) {
        log.info("🖊️ 開始創建電子簽名單元格，回收訂單ID: {}", data.recycleOrderId);
        Cell signatureCell = createBasicSignatureCell();

        if (data.electronicSignatureFileId != null && !data.electronicSignatureFileId.isEmpty()) {
            log.info("📁 檢測到電子簽名檔案ID: {}", data.electronicSignatureFileId);
            try {
                addSignatureImageToCell(signatureCell, data.electronicSignatureFileId);
                log.info("✅ 電子簽名圖片成功添加到PDF單元格");
            } catch (Exception e) {
                log.error("❌ 載入電子簽名圖片失敗，檔案ID: {}, 錯誤類型: {}, 錯誤訊息: {}",
                         data.electronicSignatureFileId, e.getClass().getSimpleName(), e.getMessage(), e);
                addSignatureErrorMessageToCell(signatureCell);
                log.info("🔄 已添加錯誤訊息到簽名單元格作為降級處理");
            }
        } else {
            log.warn("⚠️ 電子簽名檔案ID為空，將創建空的簽名單元格，回收訂單ID: {}", data.recycleOrderId);
        }

        return signatureCell;
    }

    /**
     * 創建基本的簽名單元格
     */
    private Cell createBasicSignatureCell() {
        return CellBuilder.create()
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 添加簽名圖片到單元格
     */
    private void addSignatureImageToCell(Cell signatureCell, String fileId) throws IOException {
        log.info("📥 開始載入電子簽名圖片，檔案ID: {}", fileId);

        // 載入圖片資料
        byte[] imageBytes = loadSignatureImageBytes(fileId);
        log.info("📊 圖片資料載入成功，大小: {} bytes ({} KB)", imageBytes.length, imageBytes.length / 1024);

        // 驗證圖片資料
        validateImageBytes(imageBytes);
        log.info("✅ 圖片格式驗證通過");

        // 創建並配置圖片
        Image signatureImage = createSignatureImage(imageBytes);
        log.info("🖼️ 圖片物件創建成功，尺寸調整完成");

        // 添加到單元格
        signatureCell.add(signatureImage);
        log.info("📄 電子簽名圖片成功添加到PDF單元格，檔案ID: {}", fileId);
    }

    /**
     * 載入簽名圖片字節數組
     */
    private byte[] loadSignatureImageBytes(String fileId) throws IOException {
        log.info("🔄 正在從檔案管理系統載入圖片，檔案ID: {}", fileId);

        try (var inputStream = fileManagementHelper.downloadFileAsInputStream(fileId)) {
            if (inputStream == null) {
                log.error("❌ 檔案管理系統返回空的 InputStream，檔案ID: {}", fileId);
                throw new IOException("檔案管理系統返回空的 InputStream，檔案ID: " + fileId);
            }

            byte[] imageBytes = inputStream.readAllBytes();
            log.info("📥 圖片資料載入完成，檔案ID: {}, 資料大小: {} bytes", fileId, imageBytes.length);

            if (imageBytes.length == 0) {
                log.error("❌ 載入的圖片檔案為空，檔案ID: {}", fileId);
                throw new IOException("載入的圖片檔案為空，檔案ID: " + fileId);
            }

            return imageBytes;
        } catch (Exception e) {
            log.error("❌ 從檔案管理系統載入圖片失敗，檔案ID: {}, 錯誤: {}", fileId, e.getMessage(), e);
            throw new IOException("載入電子簽名圖片失敗，檔案ID: " + fileId, e);
        }
    }

    /**
     * 驗證圖片字節數組
     */
    private void validateImageBytes(byte[] imageBytes) throws IOException {
        log.info("🔍 開始驗證圖片格式，資料大小: {} bytes", imageBytes.length);

        if (imageBytes == null || imageBytes.length == 0) {
            log.error("❌ 圖片資料為空或null");
            throw new IOException("圖片資料為空");
        }

        // 檢查是否為支援的圖片格式（PNG, JPG, GIF等）
        String detectedFormat = detectImageFormat(imageBytes);
        if (detectedFormat == null) {
            log.error("❌ 不支援的圖片格式，檔案頭: {}", getFileHeader(imageBytes));
            throw new IOException("不支援的圖片格式，請使用 PNG、JPEG 或 GIF 格式");
        }

        log.info("✅ 圖片格式驗證通過，檢測到格式: {}", detectedFormat);
    }

    /**
     * 檢測圖片格式
     */
    private String detectImageFormat(byte[] imageBytes) {
        if (imageBytes.length < 4) {
            return null;
        }

        // 檢查PNG格式 (89 50 4E 47)
        if (imageBytes[0] == (byte)0x89 && imageBytes[1] == 0x50 &&
            imageBytes[2] == 0x4E && imageBytes[3] == 0x47) {
            return "PNG";
        }

        // 檢查JPEG格式 (FF D8 FF)
        if (imageBytes[0] == (byte)0xFF && imageBytes[1] == (byte)0xD8 &&
            imageBytes[2] == (byte)0xFF) {
            return "JPEG";
        }

        // 檢查GIF格式 (47 49 46)
        if (imageBytes[0] == 0x47 && imageBytes[1] == 0x49 && imageBytes[2] == 0x46) {
            return "GIF";
        }

        // 檢查WebP格式 (52 49 46 46 ... 57 45 42 50)
        if (imageBytes.length >= 12 &&
            imageBytes[0] == 0x52 && imageBytes[1] == 0x49 &&
            imageBytes[2] == 0x46 && imageBytes[3] == 0x46 &&
            imageBytes[8] == 0x57 && imageBytes[9] == 0x45 &&
            imageBytes[10] == 0x42 && imageBytes[11] == 0x50) {
            return "WebP";
        }

        return null;
    }

    /**
     * 獲取檔案頭資訊用於診斷
     */
    private String getFileHeader(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length == 0) {
            return "空檔案";
        }

        StringBuilder header = new StringBuilder();
        int headerLength = Math.min(16, imageBytes.length);
        for (int i = 0; i < headerLength; i++) {
            header.append(String.format("%02X ", imageBytes[i] & 0xFF));
        }
        return header.toString().trim();
    }

    /**
     * 檢查是否為有效的圖片格式（向後兼容）
     */
    private boolean isValidImageFormat(byte[] imageBytes) {
        return detectImageFormat(imageBytes) != null;
    }

    /**
     * 創建簽名圖片
     */
    private Image createSignatureImage(byte[] imageBytes) throws IOException {
        log.info("🎨 開始創建PDF圖片物件，原始資料大小: {} bytes", imageBytes.length);

        try {
            Image signatureImage = new Image(ImageDataFactory.create(imageBytes));
            log.info("✅ PDF圖片物件創建成功");

            // 記錄原始圖片尺寸
            float originalWidth = signatureImage.getImageWidth();
            float originalHeight = signatureImage.getImageHeight();
            log.info("📐 原始圖片尺寸: {}x{} 像素", originalWidth, originalHeight);

            // 設定圖片尺寸，保持比例
            signatureImage.scaleToFit(Dimensions.SIGNATURE_MAX_WIDTH, Dimensions.SIGNATURE_MAX_HEIGHT);
            log.info("📏 圖片尺寸調整完成，最大尺寸限制: {}x{} 像素",
                     Dimensions.SIGNATURE_MAX_WIDTH, Dimensions.SIGNATURE_MAX_HEIGHT);

            // 設定圖片對齊方式
            signatureImage.setHorizontalAlignment(com.itextpdf.layout.properties.HorizontalAlignment.CENTER);
            log.info("🎯 圖片對齊方式設定完成：水平居中");

            return signatureImage;
        } catch (Exception e) {
            log.error("❌ 創建PDF圖片物件失敗，錯誤: {}", e.getMessage(), e);
            throw new IOException("創建PDF圖片物件失敗", e);
        }
    }

    /**
     * 添加簽名錯誤訊息到單元格
     */
    private void addSignatureErrorMessageToCell(Cell signatureCell) {
        Paragraph errorParagraph = new Paragraph(DefaultValues.SIGNATURE_LOAD_FAILED)
                .setFontSize(FontSizes.HIGHLIGHT)
                .setFontColor(Colors.BLACK)
                .setTextAlignment(TextAlignment.CENTER)
                .setMultipliedLeading(LineSpacing.STANDARD);

        signatureCell.add(errorParagraph);
    }

    /**
     * 添加頁腳
     *
     * @param document PDF文檔
     */
    private void addFooter(Document document) {
        document.add(new Paragraph().setMarginBottom(Margins.SECTION_BOTTOM));

        Table footerTable = new Table(UnitValue.createPercentArray(TableLayouts.FOOTER_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        footerTable.addCell(createPrintTimeCell());
        footerTable.addCell(createCopyrightCell());

        document.add(footerTable);
    }

    /**
     * 創建列印時間單元格
     */
    private Cell createPrintTimeCell() {
        String printTime = "列印時間: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateFormats.PRINT_TIME));
        return CellBuilder.create()
                .addText(printTime, FontSizes.FOOTER, Colors.BLACK)
                .setAlignment(TextAlignment.LEFT, VerticalAlignment.MIDDLE)
                .removeBorder()
                .setPadding(0)
                .build();
    }

    /**
     * 創建版權單元格
     */
    private Cell createCopyrightCell() {
        String copyright = buildCopyrightText();
        return CellBuilder.create()
                .addText(copyright, FontSizes.FOOTER, Colors.BLACK)
                .setAlignment(TextAlignment.RIGHT, VerticalAlignment.MIDDLE)
                .removeBorder()
                .setPadding(0)
                .build();
    }

    /**
     * 產出當年度的版權字串（使用 Ⓒ + 年份）
     */
    private String buildCopyrightText() {
        int year = LocalDate.now().getYear();
        return "創宇數位科技 版權所有，侵權必究 © " + year;
    }

    /**
     * 創建標題單元格
     *
     * @param text    標題文字
     * @param colspan 跨列數
     * @return 格式化的標題單元格
     */
    private Cell createHeaderCell(String text, int colspan) {
        return CellBuilder.create(colspan)
                .addText(text, FontSizes.LABEL, Colors.WHITE, true)
                .setBackground(Colors.BLUE_HEADER)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建藍色標題單元格
     *
     * @param text    標題文字
     * @param colspan 跨列數
     * @return 格式化的藍色標題單元格
     */
    private Cell createBlueHeaderCell(String text, int colspan) {
        return createHeaderCell(text, colspan); // 與標題單元格相同
    }

    /**
     * 創建黃色突出顯示單元格
     *
     * @param text    文字內容
     * @param colspan 跨列數
     * @return 格式化的黃色突出單元格
     */
    private Cell createYellowHighlightCell(String text, int colspan) {
        return CellBuilder.create(colspan)
                .addText(text, FontSizes.HIGHLIGHT, Colors.BLACK, true)
                .setBackground(Colors.YELLOW_HIGHLIGHT)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建淺黃色資料單元格
     *
     * @param text    資料文字
     * @param colspan 跨列數
     * @return 格式化的淺黃色資料單元格
     */
    private Cell createLightYellowDataCell(String text, int colspan) {
        return CellBuilder.create(colspan)
                .addText(text, FontSizes.DATA, Colors.BLACK, true)
                .setBackground(Colors.LIGHT_YELLOW)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建標籤單元格
     *
     * @param text 標籤文字
     * @return 格式化的標籤單元格
     */
    private Cell createLabelCell(String text) {
        return createLabelCell(text, 1);
    }

    /**
     * 創建標籤單元格（指定跨列數）
     *
     * @param text    標籤文字
     * @param colspan 跨列數
     * @return 格式化的標籤單元格
     */
    private Cell createLabelCell(String text, int colspan) {
        return CellBuilder.create(colspan)
                .addText(text, FontSizes.LABEL, Colors.BLACK, true)
                .setBackground(Colors.LIGHT_BLUE)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建價格單元格（右對齊，不折行）
     *
     * @param text 價格文字
     * @return 格式化的價格單元格
     */
    private Cell createPriceCell(String text) {
        return new Cell()
                .add(new Paragraph(text != null ? text : "")
                        .setFontSize(FontSizes.DATA)
                        .setBold()
                        .setFontColor(Colors.BLACK)
                        .setMultipliedLeading(LineSpacing.STANDARD)
                        .setKeepTogether(true)) // 防止內容折行
                .setTextAlignment(TextAlignment.RIGHT)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setBorder(new SolidBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH))
                .setPadding(Dimensions.CELL_PADDING)
                .setKeepTogether(true) // 防止單元格內容折行
                .setMinWidth(UnitValue.createPointValue(60)); // 設置最小寬度確保內容顯示
    }

    /**
     * 創建資料單元格
     *
     * @param text 資料文字
     * @return 格式化的資料單元格
     */
    private Cell createDataCell(String text) {
        return createDataCell(text, 1);
    }

    /**
     * 創建資料單元格（指定跨列數）
     *
     * @param text    資料文字
     * @param colspan 跨列數
     * @return 格式化的資料單元格
     */
    private Cell createDataCell(String text, int colspan) {
        return CellBuilder.create(colspan)
                .addText(text, FontSizes.SMALL_DATA, Colors.BLACK)
                .setAlignment(TextAlignment.LEFT, VerticalAlignment.MIDDLE)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建商品名稱資料單元格（多行文字上下置中）
     *
     * @param text    商品名稱文字（可能包含換行符號）
     * @param colspan 跨列數
     * @return 格式化的商品名稱單元格
     */
    private Cell createProductNameDataCell(String text, int colspan) {
        return new Cell(1, colspan)
                .add(new Paragraph(text != null ? text : "")
                        .setFontSize(FontSizes.SMALL_DATA)
                        .setFontColor(Colors.BLACK)
                        .setMultipliedLeading(LineSpacing.STANDARD)
                        .setTextAlignment(TextAlignment.LEFT))
                .setTextAlignment(TextAlignment.LEFT)
                .setVerticalAlignment(VerticalAlignment.MIDDLE) // 確保上下置中
                .setBorder(new SolidBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH))
                .setPadding(Dimensions.CELL_PADDING)
                .setKeepTogether(true); // 防止內容分頁
    }

    /**
     * 創建條碼單元格
     *
     * @param serialNumber 序號
     * @param colspan      跨列數
     * @return 格式化的條碼單元格
     */
    private Cell createBarcodeCell(String serialNumber, int colspan) {
        return createBarcodeCell(serialNumber, colspan, 1);
    }

    /**
     * 創建條碼單元格（支援rowspan）
     *
     * @param serialNumber 序號
     * @param colspan      跨列數
     * @param rowspan      跨行數
     * @return 格式化的條碼單元格
     */
    private Cell createBarcodeCell(String serialNumber, int colspan, int rowspan) {
        Table barcodeTable = createBarcodeTable(serialNumber);

        return new Cell(rowspan, colspan)
                .add(barcodeTable)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setBorder(new SolidBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH))
                .setPadding(Dimensions.CELL_PADDING);
    }

    /**
     * 創建條碼表格
     * 條碼圖片在上方，IMEI數字在正下方垂直對齊
     *
     * @param serialNumber 序號
     * @return 條碼表格
     */
    private Table createBarcodeTable(String serialNumber) {
        Table barcodeTable = new Table(UnitValue.createPercentArray(TableLayouts.BARCODE_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        // 第一行：條碼圖片
        barcodeTable.addCell(createBarcodeImageCell(serialNumber));

        // 第二行：IMEI數字文字（放在條碼正下方）
        barcodeTable.addCell(createBarcodeTextCell(serialNumber));

        return barcodeTable;
    }

    /**
     * 創建條碼圖片單元格
     *
     * @param serialNumber 序號
     * @return 條碼圖片單元格
     */
    private Cell createBarcodeImageCell(String serialNumber) {
        try {
            byte[] barcodeBytes = generateBarcodeImage(serialNumber, Dimensions.BARCODE_WIDTH, Dimensions.BARCODE_HEIGHT_INT);
            Image barcodeImage = new Image(ImageDataFactory.create(barcodeBytes));
            barcodeImage.setWidth(UnitValue.createPercentValue(100));
            barcodeImage.setHeight(UnitValue.createPointValue(Dimensions.BARCODE_HEIGHT));

            return new Cell()
                    .add(barcodeImage)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setPadding(Dimensions.CELL_PADDING)
                    .setBorder(null);
        } catch (Exception e) {
            log.warn("條碼生成失敗，使用文字替代: {}", e.getMessage());
            return createBarcodeFallbackCell();
        }
    }

    /**
     * 創建條碼降級單元格
     *
     * @return 條碼降級單元格
     */
    private Cell createBarcodeFallbackCell() {
        return new Cell()
                .add(new Paragraph(DefaultValues.BARCODE_FALLBACK_TEXT)
                        .setFontSize(FontSizes.BARCODE_FALLBACK)
                        .setMultipliedLeading(LineSpacing.STANDARD))
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setHeight(UnitValue.createPointValue(Dimensions.BARCODE_HEIGHT))
                .setPadding(Dimensions.CELL_PADDING)
                .setBorder(null);
    }

    /**
     * 創建條碼文字單元格
     *
     * @param serialNumber 序號
     * @return 條碼文字單元格
     */
    private Cell createBarcodeTextCell(String serialNumber) {
        String displayText = formatSerialNumber(serialNumber);

        return new Cell()
                .add(new Paragraph(displayText)
                        .setFontSize(FontSizes.BARCODE_TEXT)
                        .setBold() // 使文字更清晰
                        .setFontColor(Colors.TEXT_GRAY)
                        .setMultipliedLeading(LineSpacing.STANDARD))
                .setTextAlignment(TextAlignment.CENTER)
                .setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setPadding(Dimensions.CELL_PADDING)
                .setBorder(null);
    }

    /**
     * 格式化序號顯示
     * IMEI序號顯示為連續的15位數字，不添加連字符號
     *
     * @param serialNumber 原始序號
     * @return 格式化後的序號
     */
    private String formatSerialNumber(String serialNumber) {
        if (serialNumber == null || serialNumber.trim().isEmpty()) {
            return "";
        }

        // 直接返回清理後的序號，不添加任何格式化符號
        return serialNumber.trim();
    }

    /**
     * 創建帶rowspan的標籤單元格
     *
     * @param text    標籤文字
     * @param colspan 跨列數
     * @param rowspan 跨行數
     * @return 格式化的標籤單元格
     */
    private Cell createLabelCellWithRowspan(String text, int colspan, int rowspan) {
        return CellBuilder.create(rowspan, colspan)
                .addText(text, FontSizes.HIGHLIGHT, Colors.BLACK, true)
                .setBackground(Colors.LIGHT_BLUE)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建帶rowspan的條碼單元格
     *
     * @param serialNumber 序號
     * @param colspan      跨列數
     * @param rowspan      跨行數
     * @return 格式化的條碼單元格
     */
    private Cell createBarcodeCellWithRowspan(String serialNumber, int colspan, int rowspan) {
        return createBarcodeCell(serialNumber, colspan, rowspan);
    }

    /**
     * 創建帶rowspan的資料單元格
     *
     * @param text    資料文字
     * @param colspan 跨列數
     * @param rowspan 跨行數
     * @return 格式化的資料單元格
     */
    private Cell createDataCellWithRowspan(String text, int colspan, int rowspan) {
        return CellBuilder.create(rowspan, colspan)
                .addText(text, FontSizes.DATA, Colors.BLACK)
                .setAlignment(TextAlignment.LEFT, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建帶rowspan的黃色突出顯示單元格
     *
     * @param text    文字內容
     * @param colspan 跨列數
     * @param rowspan 跨行數
     * @return 格式化的黃色突出單元格
     */
    private Cell createYellowHighlightCellWithRowspan(String text, int colspan, int rowspan) {
        return CellBuilder.create(rowspan, colspan)
                .addText(text, FontSizes.HIGHLIGHT, Colors.BLACK, true)
                .setBackground(Colors.YELLOW_HIGHLIGHT)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 創建帶rowspan的淺黃色資料單元格
     *
     * @param text    資料文字
     * @param colspan 跨列數
     * @param rowspan 跨行數
     * @return 格式化的淺黃色資料單元格
     */
    private Cell createLightYellowDataCellWithRowspan(String text, int colspan, int rowspan) {
        return CellBuilder.create(rowspan, colspan)
                .addText(text, FontSizes.DATA, Colors.BLACK, true)
                .setBackground(Colors.LIGHT_YELLOW)
                .setAlignment(TextAlignment.CENTER, VerticalAlignment.MIDDLE)
                .setBorder(Colors.BORDER_GRAY, Dimensions.BORDER_WIDTH)
                .setPadding(Dimensions.CELL_PADDING)
                .build();
    }

    /**
     * 資料提取器內部類
     * 統一處理所有資料提取邏輯
     */
    private class DataExtractor {

        /**
         * 從資料物件獲取產品名稱
         *
         * @param data 買賣切結書資料
         * @return 產品名稱
         */
        public String getProductName(TransactionAgreementData data) {
            try {
                if (data.partNumberId != null) {
                    var partNumber = productInfoHelper.findProductByIdOrElseThrow(data.partNumberId);
                    return partNumber.getProductName() != null ? partNumber.getProductName() : partNumber.getPartNumberName();
                }
            } catch (Exception e) {
                log.warn("無法獲取產品名稱，回收單ID: {}, 料件ID: {}",
                         data.recycleOrderId, data.partNumberId, e);
            }
            return DefaultValues.UNKNOWN_PRODUCT;
        }

        /**
         * 從資料物件獲取品牌名稱
         *
         * @param data 買賣切結書資料
         * @return 品牌名稱
         */
        public String getBrandName(TransactionAgreementData data) {
            try {
                if (data.partNumberId != null) {
                    var partNumber = productInfoHelper.findProductByIdOrElseThrow(data.partNumberId);
                    if (partNumber.getProductSeriesId() != null) {
                        return productSeriesHelper.findBrandNameByProductSeriesId(partNumber.getProductSeriesId());
                    }
                }
            } catch (Exception e) {
                log.warn("無法獲取品牌名稱，回收單ID: {}, 料件ID: {}",
                         data.recycleOrderId, data.partNumberId, e);
            }
            return "";
        }

        /**
         * 從資料物件獲取序號
         *
         * @param data 買賣切結書資料
         * @return 序號（IMEI、序號或虛擬序號）
         */
        public String getSerialNumber(TransactionAgreementData data) {
            if (data.imei != null && !data.imei.isEmpty()) {
                return data.imei;
            }
            if (data.serialNumber != null && !data.serialNumber.isEmpty()) {
                return data.serialNumber;
            }
            if (data.virtualSerialNumber != null && !data.virtualSerialNumber.isEmpty()) {
                return data.virtualSerialNumber;
            }
            return "";
        }

        /**
         * 從資料物件獲取回收價格
         * 買賣切結書基於單一檢測單生成，只顯示該檢測單的回收成交價
         *
         * @param data 買賣切結書資料
         * @return 格式化的回收價格，如果成交價為空或為0則返回空字符串
         */
        public String getRecyclePrice(TransactionAgreementData data) {
            NumberFormat formatter = NumberFormat.getNumberInstance();

            // 只使用單一檢測單的回收成交價
            if (data.recyclePrice != null && data.recyclePrice.compareTo(BigDecimal.ZERO) > 0) {
                return DefaultValues.PRICE_PREFIX + formatter.format(data.recyclePrice);
            }

            // 如果回收成交價為空或為0，直接返回空字符串
            log.warn("⚠️ 檢測單的回收成交價為空或為0，檢測單ID: {}", data.inspectionFormId);
            return "";
        }
    }

    // 創建資料提取器實例
    private final DataExtractor dataExtractor = new DataExtractor();

    /**
     * 從資料物件獲取產品名稱
     *
     * @param data 買賣切結書資料
     * @return 產品名稱
     */
    private String getProductNameFromData(TransactionAgreementData data) {
        return dataExtractor.getProductName(data);
    }

    /**
     * 從資料物件獲取品牌名稱
     *
     * @param data 買賣切結書資料
     * @return 品牌名稱
     */
    private String getBrandNameFromData(TransactionAgreementData data) {
        return dataExtractor.getBrandName(data);
    }

    /**
     * 從資料物件獲取序號
     *
     * @param data 買賣切結書資料
     * @return 序號（IMEI、序號或虛擬序號）
     */
    private String getSerialNumberFromData(TransactionAgreementData data) {
        return dataExtractor.getSerialNumber(data);
    }

    /**
     * 從資料物件獲取回收價格
     *
     * @param data 買賣切結書資料
     * @return 格式化的回收價格
     */
    private String getRecyclePriceFromData(TransactionAgreementData data) {
        return dataExtractor.getRecyclePrice(data);
    }

    /**
     * 創建 MultipartFile 實例
     *
     * @param fileName 檔案名稱
     * @param content  檔案內容
     * @return MultipartFile 實例
     */
    public MultipartFile createMultipartFile(String fileName, byte[] content) {
        return new MultipartFile() {
            @Override
            public String getName() {
                return "file";
            }

            @Override
            public String getOriginalFilename() {
                return fileName;
            }

            @Override
            public String getContentType() {
                return "application/pdf";
            }

            @Override
            public boolean isEmpty() {
                return content == null || content.length == 0;
            }

            @Override
            public long getSize() {
                return content != null ? content.length : 0;
            }

            @Override
            public byte[] getBytes() {
                return content;
            }

            @Override
            public java.io.InputStream getInputStream() {
                return new java.io.ByteArrayInputStream(content);
            }

            @Override
            public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                    fos.write(content);
                }
            }
        };
    }

    /**
     * 建構買賣切結書資料
     *
     * @param inspectionForm 檢測單實體
     * @return 買賣切結書資料
     */
    private TransactionAgreementData buildTransactionAgreementData(InspectionFormEntity inspectionForm) {
        TransactionAgreementData data = new TransactionAgreementData();

        // 設置檢測單資訊
        data.inspectionFormId = inspectionForm.getInspectionFormId();
        data.inspectionFormCode = inspectionForm.getInspectionFormCode();
        data.partNumberId = inspectionForm.getPartNumberId();
        data.imei = inspectionForm.getImei();
        data.serialNumber = inspectionForm.getSerialNumber();
        data.virtualSerialNumber = inspectionForm.getVirtualSerialNumber();
        data.recyclePrice = inspectionForm.getRecyclePrice();
        data.recycleQuote = inspectionForm.getRecycleQuote();
        data.partnerStore = inspectionForm.getPartnerStore();
        String inspectorName = systemAccountHelper.findUserNameByUserId(inspectionForm.getPartnerInspector());
        if (StringUtils.isBlank(inspectorName)) {
            inspectorName = inspectionForm.getPartnerInspector();
        }
        data.partnerInspector = inspectorName;

        // 設置賣家資訊
        data.sellerName = inspectionForm.getSellerName();
        data.sellerIdNumber = inspectionForm.getSellerIdNumber();
        data.sellerContactPhone = inspectionForm.getSellerContactPhone();
        data.sellerContactAddress = inspectionForm.getSellerContactAddress();

        return data;
    }

    /**
     * 建構買賣切結書資料（公開方法，供 RecycleOrderHelper 使用）
     *
     * @param recycleOrder          回收單實體
     * @param primaryInspectionForm 主要檢測單
     * @param allInspectionForms    所有檢測單列表
     * @return 買賣切結書資料
     */
    public TransactionAgreementData buildTransactionAgreementDataFromRecycleOrder(RecycleOrderEntity recycleOrder,
                                                                                   InspectionFormEntity primaryInspectionForm,
                                                                                   List<InspectionFormEntity> allInspectionForms) {
        return buildTransactionAgreementData(recycleOrder, primaryInspectionForm, allInspectionForms);
    }

    /**
     * 建構買賣切結書資料
     *
     * @param recycleOrder          回收單實體
     * @param primaryInspectionForm 主要檢測單
     * @param allInspectionForms    所有檢測單列表
     * @return 買賣切結書資料
     */
    private TransactionAgreementData buildTransactionAgreementData(RecycleOrderEntity recycleOrder,
                                                                   InspectionFormEntity primaryInspectionForm,
                                                                   List<InspectionFormEntity> allInspectionForms) {
        log.info("🔧 開始建構買賣切結書資料，回收訂單ID: {}", recycleOrder.getRecycleOrderId());

        TransactionAgreementData data = new TransactionAgreementData();

        // 設置回收單資訊
        data.recycleOrderId = recycleOrder.getRecycleOrderId();
        data.recycleOrderCode = recycleOrder.getRecycleOrderCode();
        data.recycleOrderDate = recycleOrder.getRecycleOrderDate();
        data.electronicSignatureFileId = recycleOrder.getSignatureFileId();

        log.info("📋 回收訂單資訊設定完成 - 訂單編號: {}, 電子簽名檔案ID: {}",
                 data.recycleOrderCode,
                 data.electronicSignatureFileId != null ? data.electronicSignatureFileId : "無");

        // 設置賣家資訊（優先使用回收單中的資訊，因為更完整）
        data.sellerName = recycleOrder.getSellerName();
        data.sellerIdNumber = recycleOrder.getSellerIdNumber();
        data.sellerContactPhone = recycleOrder.getSellerContactPhone();
        data.sellerContactAddress = recycleOrder.getSellerContactAddress();

        // 嘗試從 SellerInformationEntity 獲取更完整的賣家資訊
        try {
            Optional<SellerInformationEntity> sellerInfo = Optional.ofNullable(
                    sellerInfomationHelper.createOrUpdateSeller(
                            data.sellerName, data.sellerIdNumber,
                            data.sellerContactPhone, data.sellerContactAddress
                    )
            ).map(sellerId -> {
                // 這裡可以添加查詢 SellerInformationEntity 的邏輯
                // 目前 SellerInfomationHelper 只返回 ID，沒有查詢方法
                return null;
            });
        } catch (Exception e) {
            log.warn("無法獲取賣家詳細資訊: {}", e.getMessage());
        }

        // 設置檢測單資訊（使用主要檢測單）
        data.inspectionFormId = primaryInspectionForm.getInspectionFormId();
        data.inspectionFormCode = primaryInspectionForm.getInspectionFormCode();
        data.partNumberId = primaryInspectionForm.getPartNumberId();
        data.imei = primaryInspectionForm.getImei();
        data.serialNumber = primaryInspectionForm.getSerialNumber();
        data.virtualSerialNumber = primaryInspectionForm.getVirtualSerialNumber();
        data.recyclePrice = primaryInspectionForm.getRecyclePrice();
        data.recycleQuote = primaryInspectionForm.getRecycleQuote();
        data.partnerStore = primaryInspectionForm.getPartnerStore();
        String inspectorName = systemAccountHelper.findUserNameByUserId(primaryInspectionForm.getPartnerInspector());
        if (StringUtils.isBlank(inspectorName)) {
            inspectorName = primaryInspectionForm.getPartnerInspector();
        }
        data.partnerInspector = inspectorName;


        // 計算總回收成交價金額（所有檢測單的成交價總和，不使用定價降級處理）
        data.totalRecyclePrice = allInspectionForms.stream()
                .map(form -> {
                    // 只使用回收成交價，不使用定價作為降級處理
                    BigDecimal price = form.getRecyclePrice();
                    if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
                        return price;
                    }

                    // 如果成交價為空或為0，記錄日誌但不使用定價
                    log.warn("⚠️ 檢測單 {} 的回收成交價為空或為0，不計入總金額，成交價: {}",
                             form.getInspectionFormId(), price);
                    return BigDecimal.ZERO;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info("💰 總回收成交價計算完成，回收訂單ID: {}, 檢測單數量: {}, 總金額: {}",
                 data.recycleOrderId, allInspectionForms.size(), data.totalRecyclePrice);

        return data;
    }

    /**
     * 從資料物件生成PDF
     *
     * @param data 買賣切結書資料
     * @return PDF字節數組
     * @throws IOException 如果PDF生成失敗
     */
    byte[] generatePdfFromData(TransactionAgreementData data) throws IOException {
        log.info("📄 開始生成買賣切結書PDF，回收訂單ID: {}", data.recycleOrderId);
        log.info("🔍 電子簽名檔案ID: {}", data.electronicSignatureFileId != null ? data.electronicSignatureFileId : "無");

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = new PdfWriter(baos);
        PdfDocument pdfDoc = new PdfDocument(writer);
        Document document = new Document(pdfDoc, PageSize.A4);

        try {
            // 設置頁面邊距
            document.setMargins(Dimensions.PAGE_MARGIN, Dimensions.PAGE_MARGIN,
                              Dimensions.PAGE_MARGIN, Dimensions.PAGE_MARGIN);
            log.info("📐 PDF頁面設定完成，頁面大小: A4，邊距: {} 像素", Dimensions.PAGE_MARGIN);

            // 設置字體 - 使用中文字型以確保中文字元正確顯示
            PdfFont font = loadChineseFont();
            document.setFont(font);
            log.info("🔤 中文字型載入完成");

            // 添加標題
            addTitle(document);
            log.info("📋 PDF標題添加完成");

            // 添加商品內容和個人申報表（使用整合後的資料）
            addMainContentFromData(document, data);
            log.info("📊 主要內容表格添加完成");

            // 添加賣方資訊（使用整合後的資料）
            addSellerInfoFromData(document, data);
            log.info("👤 賣方資訊表格添加完成");

            // 添加檢測項目表格（使用主要檢測單的ID）
            addInspectionItemsFromData(document, data);
            log.info("🔍 檢測項目表格添加完成");

            // 添加法律聲明
            addLegalTerms(document, data);
            log.info("⚖️ 法律條文和電子簽名區域添加完成");

            // 添加頁腳
            addFooter(document);
            log.info("📄 PDF頁腳添加完成");

            log.info("✅ 買賣切結書PDF生成完成，回收訂單ID: {}", data.recycleOrderId);

        } finally {
            document.close();
        }

        byte[] pdfBytes = baos.toByteArray();
        log.info("📦 PDF字節數組生成完成，大小: {} bytes ({} KB)", pdfBytes.length, pdfBytes.length / 1024);
        return pdfBytes;
    }

    /**
     * 添加主要內容（使用資料物件）
     *
     * @param document PDF文檔
     * @param data     買賣切結書資料
     */
    private void addMainContentFromData(Document document, TransactionAgreementData data) {
        Table mainTable = new Table(UnitValue.createPercentArray(TableLayouts.MAIN_TABLE_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        // 添加表格內容
        addMainTableHeaders(mainTable);
        addStoreAndDeclarationRow(mainTable, data);
        addStaffAndTaxInfoRow(mainTable, data);
        addProductAndIncomePersonRow(mainTable, data);
        addImeiAndAddressRows(mainTable, data);
        addPurchaseReasonRow(mainTable);
        addRecycleAmountAndGoodsRows(mainTable, data);

        document.add(mainTable);
    }

    /**
     * 添加主表格標題行
     */
    private void addMainTableHeaders(Table mainTable) {
        mainTable.addCell(createHeaderCell(TableHeaders.PRODUCT_CONTENT, 3));
        mainTable.addCell(createHeaderCell(TableHeaders.PERSONAL_TRADE_DECLARATION, 7));
    }

    /**
     * 添加分店名稱和申報資訊行
     */
    private void addStoreAndDeclarationRow(Table mainTable, TransactionAgreementData data) {
        mainTable.addCell(createLabelCell(TableLabels.STORE_NAME));
        mainTable.addCell(createDataCell(data.partnerStore != null ? data.partnerStore : "", 2));
        mainTable.addCell(createLabelCell(TableLabels.DECLARATION_TAX_ID, 2));
        mainTable.addCell(createLabelCell(TableLabels.INCOME_YEAR, 1));
        mainTable.addCell(createLabelCell(TableLabels.FORMAT_CODE, 2));
        mainTable.addCell(createLabelCell(TableLabels.FORM_NUMBER, 1));
        mainTable.addCell(createLabelCell(TableLabels.AUDIT_AUTHORITY, 1));
    }

    /**
     * 添加承辦人員和統編等資訊行
     */
    private void addStaffAndTaxInfoRow(Table mainTable, TransactionAgreementData data) {
        mainTable.addCell(createLabelCell(TableLabels.STAFF_MEMBER));
        mainTable.addCell(createDataCell(data.partnerInspector != null ? data.partnerInspector : "", 2));
        mainTable.addCell(createDataCell(CompanyInfo.COMPANY_TAX_ID, 2));
        mainTable.addCell(createDataCell(String.valueOf(LocalDate.now().getYear() - 1911), 1));
        mainTable.addCell(createDataCell(DefaultValues.INCOME_TYPE_CODE, 2));
        mainTable.addCell(createDataCell("", 1));
        mainTable.addCell(createDataCell("", 1));
    }

    /**
     * 添加商品名稱和所得人資訊行
     */
    private void addProductAndIncomePersonRow(Table mainTable, TransactionAgreementData data) {
        String productInfo = getBrandNameFromData(data) + "\n" + getProductNameFromData(data);
        mainTable.addCell(createLabelCell(TableLabels.PRODUCT_NAME));
        mainTable.addCell(createProductNameDataCell(productInfo, 2)); // 使用專門的商品名稱單元格
        mainTable.addCell(createLabelCell(TableLabels.INCOME_PERSON_NAME));
        mainTable.addCell(createDataCell(data.sellerName != null ? data.sellerName : "", 4));
        mainTable.addCell(createLabelCell(TableLabels.INCOME_PERSON_TAX_ID));
        mainTable.addCell(createDataCell(data.sellerIdNumber != null ? data.sellerIdNumber : ""));
    }

    /**
     * 添加IMEI和所得人地址行（使用rowspan）
     */
    private void addImeiAndAddressRows(Table mainTable, TransactionAgreementData data) {
        Cell imeiLabelCell = createLabelCellWithRowspan(TableLabels.IMEI, 1, 2);
        Cell imeiDataCell = createBarcodeCellWithRowspan(getSerialNumberFromData(data), 2, 2);
        Cell addressLabelCell = createLabelCellWithRowspan(TableLabels.INCOME_PERSON_ADDRESS, 1, 2);
        Cell addressDataCell = createDataCellWithRowspan(
                data.sellerContactAddress != null ? data.sellerContactAddress : "", 4, 2);

        mainTable.addCell(imeiLabelCell);
        mainTable.addCell(imeiDataCell);
        mainTable.addCell(addressLabelCell);
        mainTable.addCell(addressDataCell);
        mainTable.addCell(createLabelCell(TableLabels.PURCHASE_DATE));
        mainTable.addCell(createDataCell(LocalDate.now().format(DateTimeFormatter.ofPattern(DateFormats.STANDARD_DATE))));
    }

    /**
     * 添加買受原因行
     */
    private void addPurchaseReasonRow(Table mainTable) {
        mainTable.addCell(createLabelCell(TableLabels.PURCHASE_REASON));
        mainTable.addCell(createDataCell(DefaultValues.PURCHASE_REASON_VALUE, 2));
    }

    /**
     * 添加回收金額和買受貨物資訊行
     */
    private void addRecycleAmountAndGoodsRows(Table mainTable, TransactionAgreementData data) {
        // 第一行：回收金額標題和買受貨物標題
        Cell recycleAmountLabelCell = createYellowHighlightCellWithRowspan(TableLabels.RECYCLE_AMOUNT, 1, 2);
        Cell recycleAmountDataCell = createLightYellowDataCellWithRowspan(getRecyclePriceFromData(data), 2, 2);

        mainTable.addCell(recycleAmountLabelCell);
        mainTable.addCell(recycleAmountDataCell);
        mainTable.addCell(createLabelCell(TableLabels.PURCHASE_GOODS_NAME, 3));
        mainTable.addCell(createLabelCell(TableLabels.UNIT_PRICE));
        mainTable.addCell(createLabelCell(TableLabels.QUANTITY));
        mainTable.addCell(createLabelCell(TableLabels.AMOUNT));
        mainTable.addCell(createLabelCell(TableLabels.TOTAL));

        // 第二行：商品詳細資訊
        String productInfo = getProductNameFromData(data);
        String recyclePrice = getRecyclePriceFromData(data);

        mainTable.addCell(createProductNameDataCell(productInfo, 3)); // 使用專門的商品名稱單元格
        mainTable.addCell(createPriceCell(recyclePrice));
        mainTable.addCell(createPriceCell(DefaultValues.QUANTITY_VALUE));
        mainTable.addCell(createPriceCell(recyclePrice));
        mainTable.addCell(createPriceCell(recyclePrice));
    }

    /**
     * 添加賣方資訊（使用資料物件）
     *
     * @param document PDF文檔
     * @param data     買賣切結書資料
     */
    private void addSellerInfoFromData(Document document, TransactionAgreementData data) {
        Table sellerTable = new Table(UnitValue.createPercentArray(TableLayouts.SELLER_TABLE_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));

        // 添加表格內容
        addSellerTableHeaders(sellerTable);
        addSellerPersonalInfo(sellerTable, data);
        addSellerContactInfo(sellerTable, data);
        addSellerAddressAndNote(sellerTable, data);

        document.add(sellerTable);
    }

    /**
     * 添加賣方表格標題行
     */
    private void addSellerTableHeaders(Table sellerTable) {
        sellerTable.addCell(createHeaderCell(TableHeaders.SELLER_DATA, 4));
        sellerTable.addCell(createHeaderCell(TableHeaders.DECLARATION_UNIT, 4));
        sellerTable.addCell(createHeaderCell(TableHeaders.SCAN_NUMBER, 2));
    }

    /**
     * 添加賣方個人資訊行
     */
    private void addSellerPersonalInfo(Table sellerTable, TransactionAgreementData data) {
        sellerTable.addCell(createLabelCell(TableLabels.NAME));
        sellerTable.addCell(createDataCell(data.sellerName != null ? data.sellerName : "", 3));
        sellerTable.addCell(createLabelCell(TableLabels.COMPANY_NAME_LABEL));
        sellerTable.addCell(createDataCell(CompanyInfo.COMPANY_NAME, 3));
        sellerTable.addCell(createDataCellWithRowspan("", 2, 3));

        sellerTable.addCell(createLabelCell(TableLabels.ID_NUMBER));
        sellerTable.addCell(createDataCell(data.sellerIdNumber != null ? data.sellerIdNumber : "", 3));
        sellerTable.addCell(createLabelCell(TableLabels.COMPANY_ADDRESS_LABEL));
        sellerTable.addCell(createDataCell(CompanyInfo.COMPANY_ADDRESS, 3));
    }

    /**
     * 添加賣方聯絡資訊行
     */
    private void addSellerContactInfo(Table sellerTable, TransactionAgreementData data) {
        sellerTable.addCell(createLabelCell(TableLabels.CONTACT_PHONE));
        sellerTable.addCell(createDataCell(data.sellerContactPhone != null ? data.sellerContactPhone : "", 3));
        sellerTable.addCell(createLabelCell(TableLabels.REPRESENTATIVE));
        sellerTable.addCell(createDataCell(CompanyInfo.COMPANY_REPRESENTATIVE, 3));
    }

    /**
     * 添加賣方地址和備註行
     */
    private void addSellerAddressAndNote(Table sellerTable, TransactionAgreementData data) {
        sellerTable.addCell(createLabelCell(TableLabels.ADDRESS));
        sellerTable.addCell(createDataCell(data.sellerContactAddress != null ? data.sellerContactAddress : "", 3));
        sellerTable.addCell(createDataCell(DeclarationTexts.USAGE_RESTRICTION, 6));
    }

    /**
     * 添加檢測項目表格（使用資料物件）
     *
     * @param document PDF文檔
     * @param data     買賣切結書資料
     */
    private void addInspectionItemsFromData(Document document, TransactionAgreementData data) {
        document.add(new Paragraph().setMarginBottom(Margins.SECTION_BOTTOM));

        List<InspectionDetailEntity> inspectionDetails = inspectionDetailRepository
                .findByInspectionFormId(data.inspectionFormId);

        if (inspectionDetails.isEmpty()) {
            return;
        }

        Table inspectionTable = createInspectionTable();
        addInspectionTableHeaders(inspectionTable);
        addInspectionTableData(inspectionTable, inspectionDetails);

        document.add(inspectionTable);
    }

    /**
     * 創建檢測項目表格
     */
    private Table createInspectionTable() {
        return new Table(UnitValue.createPercentArray(TableLayouts.INSPECTION_TABLE_LAYOUT))
                .setWidth(UnitValue.createPercentValue(100));
    }

    /**
     * 添加檢測表格標題
     */
    private void addInspectionTableHeaders(Table inspectionTable) {
        inspectionTable.addCell(createBlueHeaderCell(TableHeaders.INSPECTION_ITEMS, 1));
        inspectionTable.addCell(createBlueHeaderCell(TableHeaders.INSPECTION_ITEMS, 1));
    }

    /**
     * 添加檢測表格資料
     */
    private void addInspectionTableData(Table inspectionTable, List<InspectionDetailEntity> inspectionDetails) {
        for (int i = 0; i < inspectionDetails.size(); i += 2) {
            InspectionDetailEntity leftDetail = inspectionDetails.get(i);
            InspectionDetailEntity rightDetail = (i + 1 < inspectionDetails.size()) ? inspectionDetails.get(i + 1) : null;

            String leftResult = getInspectionResult(leftDetail);
            String rightResult = rightDetail != null ? getInspectionResult(rightDetail) : "";

            String leftContent = leftDetail.getInspectionQuestion() + "：" + leftResult;
            String rightContent = rightResult.isEmpty() ? "" : rightDetail.getInspectionQuestion() + "：" + rightResult;

            inspectionTable.addCell(createDataCell(leftContent));
            inspectionTable.addCell(createDataCell(rightContent));
        }
    }

    /**
     * 買賣切結書資料封裝類
     */
    static class TransactionAgreementData {
        // 回收單資訊
        String recycleOrderId;
        String recycleOrderCode;
        Long recycleOrderDate;

        // 賣家資訊
        String sellerName;
        String sellerIdNumber;
        String sellerContactPhone;
        String sellerContactAddress;

        // 檢測單資訊
        String inspectionFormId;
        String inspectionFormCode;
        String partNumberId;
        String imei;
        String serialNumber;
        String virtualSerialNumber;
        BigDecimal recyclePrice;    // 回收成交價（最終交易價格）
        BigDecimal recycleQuote;    // 回收定價（初始報價）
        String partnerStore;
        String partnerInspector;


        // 計算欄位
        BigDecimal totalRecyclePrice;    // 總回收成交價（所有檢測單的成交價總和）
        String electronicSignatureFileId;
    }
}
