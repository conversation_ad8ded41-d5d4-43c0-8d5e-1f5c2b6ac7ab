package com.jgallop.api.service.mapper;

import com.jgallop.api.entity.*;
import com.jgallop.api.model.resp.RespRecycleWebsiteBrand;
import com.jgallop.api.model.resp.RespRecycleWebsiteBrandProduct;
import com.jgallop.api.model.resp.RespRecycleWebsiteProductSummary;
import com.jgallop.api.repository.PartNumberImageRepository;
import com.jgallop.api.service.RecycleWebsiteHelper;
import com.jgallop.hfs.entity.postgres.FileEntity;
import com.jgallop.hfs.repository.postgres.FileRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 回收網站映射服務
 * 負責將實體轉換為回應 DTO
 */
@Service
@RequiredArgsConstructor
public class RecycleWebsiteMapper {

    private final FileRepository fileRepository;
    private final RecycleWebsiteHelper recycleWebsiteHelper;
    private final PartNumberImageRepository partNumberImageRepository;

    /**
     * 將品牌實體列表轉換為回應 DTO 列表
     *
     * @param brands 品牌實體列表
     * @return 品牌回應 DTO 列表
     */
    public List<RespRecycleWebsiteBrand> mapToBrandList(List<PartNumberBrandEntity> brands) {
        return brands.stream()
                .map(brand -> RespRecycleWebsiteBrand.builder()
                        .brandId(brand.getBrandId())
                        .brandName(brand.getBrandName())
                        .visibleToSupplier(true)
                        .build())
                .toList();
    }

    /**
     * 構建品牌與商品巢狀結構（直接商店）
     *
     * @param brands 品牌列表
     * @param productSeriesMap 產品系列映射
     * @param partNumbers 料件列表
     * @param visibleToSupplier 是否對供應商可見
     * @return 品牌商品巢狀結構
     */
    public List<RespRecycleWebsiteBrandProduct> buildBrandProductStructure(
            List<PartNumberBrandEntity> brands,
            Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap,
            List<PartNumberEntity> partNumbers,
            boolean visibleToSupplier) {

        // 批量查詢圖片和檔案資訊，避免 N+1 查詢問題
        Map<String, RecycleWebsiteHelper.ImageInfo> imageInfoMap = batchGetPartNumberImages(partNumbers);

        List<RespRecycleWebsiteBrandProduct> result = new ArrayList<>();

        for (PartNumberBrandEntity brand : brands) {
            String brandId = brand.getBrandId();

            // 創建品牌項
            RespRecycleWebsiteBrandProduct brandItem = new RespRecycleWebsiteBrandProduct();
            brandItem.setBrandId(brandId);
            brandItem.setBrandName(brand.getBrandName());
            brandItem.setVisibleToSupplier(visibleToSupplier);

            // 查找該品牌下的所有料件
            List<RespRecycleWebsiteProductSummary> items = buildProductSummariesForBrand(
                    brandId, productSeriesMap, partNumbers, null, imageInfoMap);

            // 只有當品牌下有商品時才添加到結果中
            if (!items.isEmpty()) {
                brandItem.setItems(items);
                result.add(brandItem);
            }
        }

        return result;
    }

    /**
     * 構建品牌與商品巢狀結構（供應商）
     *
     * @param partnerBrands 供應商品牌關聯列表
     * @param brandMap 品牌映射
     * @param productSeriesMap 產品系列映射
     * @param partNumbers 料件列表
     * @param recoveryDetailMap 回收詳情映射
     * @return 品牌商品巢狀結構
     */
    public List<RespRecycleWebsiteBrandProduct> buildSupplierBrandProductStructure(
            List<PartnerBrandEntity> partnerBrands,
            Map<String, PartNumberBrandEntity> brandMap,
            Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap,
            List<PartNumberEntity> partNumbers,
            Map<String, PartNumberRecoveryDetailEntity> recoveryDetailMap) {

        // 批量查詢圖片和檔案資訊，避免 N+1 查詢問題
        Map<String, RecycleWebsiteHelper.ImageInfo> imageInfoMap = batchGetPartNumberImages(partNumbers);

        List<RespRecycleWebsiteBrandProduct> result = new ArrayList<>();

        for (PartnerBrandEntity partnerBrand : partnerBrands) {
            String brandId = partnerBrand.getId().getBrandId();
            PartNumberBrandEntity brandEntity = brandMap.get(brandId);

            if (brandEntity == null) {
                continue;
            }

            // 創建品牌項
            RespRecycleWebsiteBrandProduct brandItem = new RespRecycleWebsiteBrandProduct();
            brandItem.setBrandId(brandId);
            brandItem.setBrandName(brandEntity.getBrandName());
            brandItem.setVisibleToSupplier(partnerBrand.getVisibleToSupplier());

            // 查找該品牌下的所有料件
            List<RespRecycleWebsiteProductSummary> items = buildProductSummariesForBrand(
                    brandId, productSeriesMap, partNumbers, recoveryDetailMap, imageInfoMap);

            // 只有當品牌下有商品時才添加到結果中
            if (!items.isEmpty()) {
                brandItem.setItems(items);
                result.add(brandItem);
            }
        }

        return result;
    }

    /**
     * 為特定品牌構建商品摘要列表
     * 注意：由於查詢層已確保料件與品牌的正確關聯，此方法不再需要額外的過濾邏輯
     *
     * @param brandId 品牌ID
     * @param productSeriesMap 產品系列映射
     * @param partNumbers 料件列表（已經過正確的階層查詢過濾）
     * @param recoveryDetailMap 回收詳情映射（可為 null，用於直接商店）
     * @param imageInfoMap 圖片資訊映射
     * @return 商品摘要列表
     */
    private List<RespRecycleWebsiteProductSummary> buildProductSummariesForBrand(
            String brandId,
            Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap,
            List<PartNumberEntity> partNumbers,
            Map<String, PartNumberRecoveryDetailEntity> recoveryDetailMap,
            Map<String, RecycleWebsiteHelper.ImageInfo> imageInfoMap) {

        List<RespRecycleWebsiteProductSummary> items = new ArrayList<>();
        List<PartNumberProductSeriesEntity> seriesList = productSeriesMap.getOrDefault(brandId, List.of());

        // 獲取該品牌下所有產品系列的ID（用於驗證）
        Set<String> seriesIds = seriesList.stream()
                .map(PartNumberProductSeriesEntity::getProductSeriesId)
                .collect(Collectors.toSet());

        // 處理所有料件（已經通過階層查詢確保正確關聯）
        for (PartNumberEntity partNumber : partNumbers) {
            // 驗證料件是否屬於該品牌的產品系列
            if (partNumber.getProductSeriesId() != null &&
                seriesIds.contains(partNumber.getProductSeriesId())) {

                PartNumberRecoveryDetailEntity detail = null;
                if (recoveryDetailMap != null) {
                    detail = recoveryDetailMap.get(partNumber.getPartNumberId());
                    if (detail == null) {
                        continue; // 如果沒有回收詳情，跳過此料件
                    }
                }

                items.add(mapToRespRecycleWebsiteProductSummary(partNumber, detail, imageInfoMap));
            }
        }

        return items;
    }

    /**
     * 將實體轉換為商品摘要回應物件
     *
     * @param partNumber 料件實體
     * @param recoveryDetail 回收詳情實體
     * @param imageInfoMap 圖片資訊映射
     * @return 商品摘要回應物件
     */
    private RespRecycleWebsiteProductSummary mapToRespRecycleWebsiteProductSummary(
            PartNumberEntity partNumber,
            PartNumberRecoveryDetailEntity recoveryDetail,
            Map<String, RecycleWebsiteHelper.ImageInfo> imageInfoMap) {

        // 從快取中獲取料件圖片，避免 N+1 查詢問題
        RecycleWebsiteHelper.ImageInfo imageInfo = imageInfoMap.getOrDefault(
                partNumber.getPartNumberId(),
                new RecycleWebsiteHelper.ImageInfo("", ""));

        RespRecycleWebsiteProductSummary.RespRecycleWebsiteProductSummaryBuilder builder = RespRecycleWebsiteProductSummary.builder()
                .partNumberId(partNumber.getPartNumberId())
                .partNumberName(partNumber.getPartNumberName())
                .productName(partNumber.getProductName())
                .specification(partNumber.getSpecification())
                .price(recycleWebsiteHelper.getDefaultPrice())
                .imagePath(imageInfo.getPath())
                .imageName(imageInfo.getName());

        if (recoveryDetail != null) {
            // 一般供應商路徑：使用供應商/外部計數
            builder
                    .supplierInquiryCount(recoveryDetail.getSupplierViewCount())
                    .externalInquiryCount(recoveryDetail.getExternalViewCount())
                    .totalInquiryCount(recoveryDetail.getSupplierViewCount() + recoveryDetail.getExternalViewCount());
        } else {
            // 直營門市路徑：使用料件的總瀏覽次數 viewCount
            Integer viewCount = partNumber.getViewCount();
            builder
                    .supplierInquiryCount(0)
                    .externalInquiryCount(0)
                    .totalInquiryCount(viewCount != null ? viewCount : 0);
        }

        return builder.build();
    }

    /**
     * 批量獲取料件圖片資訊，避免 N+1 查詢問題
     *
     * @param partNumbers 料件列表
     * @return 料件ID到圖片資訊的映射
     */
    private Map<String, RecycleWebsiteHelper.ImageInfo> batchGetPartNumberImages(List<PartNumberEntity> partNumbers) {
        if (partNumbers == null || partNumbers.isEmpty()) {
            return Collections.emptyMap();
        }

        // 獲取所有料件ID
        List<String> partNumberIds = partNumbers.stream()
                .map(PartNumberEntity::getPartNumberId)
                .toList();

        // 批量查詢所有料件的圖片
        List<PartNumberImageEntity> allImages = partNumberImageRepository.findById_PartNumberIdIn(partNumberIds);

        // 獲取所有檔案ID
        List<String> fileIds = allImages.stream()
                .map(image -> image.getId().getFileId())
                .distinct()
                .toList();

        // 批量查詢檔案資訊
        Map<String, FileEntity> fileMap = fileRepository.findAllById(fileIds).stream()
                .collect(Collectors.toMap(FileEntity::getFileId, file -> file));

        // 建立料件ID到圖片資訊的映射
        Map<String, RecycleWebsiteHelper.ImageInfo> imageInfoMap = new HashMap<>();

        for (PartNumberImageEntity image : allImages) {
            String partNumberId = image.getId().getPartNumberId();
            String fileId = image.getId().getFileId();
            FileEntity fileEntity = fileMap.get(fileId);

            if (fileEntity != null) {
                RecycleWebsiteHelper.ImageInfo imageInfo = new RecycleWebsiteHelper.ImageInfo(
                        fileEntity.getFilePath(),
                        fileEntity.getFileName());
                imageInfoMap.put(partNumberId, imageInfo);
            }
        }

        return imageInfoMap;
    }
}
