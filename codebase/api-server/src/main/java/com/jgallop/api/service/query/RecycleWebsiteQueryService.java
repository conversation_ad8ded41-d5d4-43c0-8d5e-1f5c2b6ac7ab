package com.jgallop.api.service.query;

import com.jgallop.api.entity.*;
import com.jgallop.api.entity.enums.RecycleStatus;
import com.jgallop.api.repository.*;
import com.jgallop.api.service.PartnerHelper;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 回收網站查詢服務
 * 負責優化複雜的查詢邏輯，減少 N+1 查詢問題
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RecycleWebsiteQueryService {

    private final PartnerHelper partnerHelper;
    private final PartNumberBrandRepository partNumberBrandRepository;
    private final PartNumberRepository partNumberRepository;
    private final PartNumberProductSeriesRepository partNumberProductSeriesRepository;
    private final PartNumberRecoveryDetailRepository partNumberRecoveryDetailRepository;
    private final PartnerBrandRepository partnerBrandRepository;

    /**
     * 查詢直接商店的資料（支援分頁）
     * 遵循 Brand → Product Series → Part Numbers 的階層查詢順序
     *
     * @param brandId         品牌ID（可選）
     * @param productSeriesId 產品系列ID（可選）
     * @param keyword         關鍵字（可選）
     * @param pageable        分頁參數（可選，為 null 時不分頁）
     * @return 查詢結果
     */
    @Transactional(readOnly = true)
    public DirectStoreQueryResult queryDirectStoreDataWithPagination(String brandId, String productSeriesId, String keyword, Pageable pageable) {
        log.info("Querying direct store data with brandId: {}, productSeriesId: {}, keyword: {}, pageable: {}", brandId, productSeriesId, keyword, pageable);

        // 步驟 1: 查詢品牌（根據 brandId 和 keyword）
        List<PartNumberBrandEntity> brands = queryBrandsForDirectStore(brandId, keyword);
        if (brands.isEmpty()) {
            log.info("No brands found for brandId: {}, keyword: {}", brandId, keyword);
            return DirectStoreQueryResult.empty();
        }

        List<String> brandIds = brands.stream()
                .map(PartNumberBrandEntity::getBrandId)
                .toList();
        log.debug("Found {} brands: {}", brands.size(), brandIds);

        // 步驟 2: 查詢這些品牌下的產品系列（根據 productSeriesId 過濾）
        List<PartNumberProductSeriesEntity> filteredProductSeries = queryProductSeriesForBrands(brandIds, productSeriesId);
        if (filteredProductSeries.isEmpty()) {
            log.info("No product series found for brands: {}, productSeriesId: {}", brandIds, productSeriesId);
            return DirectStoreQueryResult.empty();
        }

        List<String> productSeriesIds = filteredProductSeries.stream()
                .map(PartNumberProductSeriesEntity::getProductSeriesId)
                .toList();
        log.debug("Found {} product series: {}", filteredProductSeries.size(), productSeriesIds);

        // 建立產品系列映射（按品牌分組）
        Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap = filteredProductSeries.stream()
                .collect(Collectors.groupingBy(PartNumberProductSeriesEntity::getBrandId));

        // 步驟 3: 查詢這些產品系列下的料件（支援分頁和關鍵字過濾）
        Page<PartNumberEntity> partNumberPage;
        List<PartNumberEntity> partNumbers;
        long totalElements;

        if (pageable != null) {
            partNumberPage = partNumberRepository.findRecyclablePartNumbersByProductSeriesIdsPageable(
                    RecycleStatus.RECYCLABLE, productSeriesIds, keyword, pageable);
            partNumbers = partNumberPage.getContent();
            totalElements = partNumberPage.getTotalElements();
        } else {
            partNumbers = partNumberRepository.findRecyclablePartNumbersByProductSeriesIds(
                    RecycleStatus.RECYCLABLE, productSeriesIds, keyword);
            totalElements = partNumbers.size();
        }

        log.debug("Found {} part numbers with total elements: {}", partNumbers.size(), totalElements);

        return DirectStoreQueryResult.builder()
                .brands(brands)
                .productSeriesMap(productSeriesMap)
                .partNumbers(partNumbers)
                .totalElements(totalElements)
                .build();
    }

    /**
     * 查詢供應商的資料（支援分頁）
     * 遵循 Brand → Product Series → Part Numbers 的階層查詢順序
     *
     * @param brandId         品牌ID（可選）
     * @param productSeriesId 產品系列ID（可選）
     * @param keyword         關鍵字（可選）
     * @param partnerCode     供應商代碼
     * @param pageable        分頁參數（可選，為 null 時不分頁）
     * @return 查詢結果
     */
    @Transactional(readOnly = true)
    public SupplierQueryResult querySupplierDataWithPagination(String brandId, String productSeriesId, String keyword, String partnerCode, Pageable pageable) {
        String partnerId = partnerHelper.getPartnerIdFromPartnerCode(partnerCode);
        log.info("Querying supplier data with partnerId: {}, brandId: {}, productSeriesId: {}, keyword: {}, pageable: {}", partnerId, brandId, productSeriesId, keyword, pageable);

        // 步驟 1: 查詢供應商關聯的品牌（根據 brandId 過濾）
        List<PartnerBrandEntity> partnerBrands = queryPartnerBrands(partnerId, brandId);
        if (partnerBrands.isEmpty()) {
            log.info("No partner brands found for partnerId: {}, brandId: {}", partnerId, brandId);
            return SupplierQueryResult.empty();
        }

        List<String> brandIds = partnerBrands.stream()
                .map(pb -> pb.getId().getBrandId())
                .toList();
        log.debug("Found {} partner brands: {}", partnerBrands.size(), brandIds);

        // 獲取品牌詳細資訊
        List<PartNumberBrandEntity> brands = partNumberBrandRepository.findAllById(brandIds);
        Map<String, PartNumberBrandEntity> brandMap = brands.stream()
                .collect(Collectors.toMap(PartNumberBrandEntity::getBrandId, brand -> brand));

        // 步驟 2: 查詢這些品牌下的產品系列（根據 productSeriesId 過濾）
        List<PartNumberProductSeriesEntity> filteredProductSeries = queryProductSeriesForBrands(brandIds, productSeriesId);
        if (filteredProductSeries.isEmpty()) {
            log.info("No product series found for brands: {}, productSeriesId: {}", brandIds, productSeriesId);
            return SupplierQueryResult.empty();
        }

        List<String> productSeriesIds = filteredProductSeries.stream()
                .map(PartNumberProductSeriesEntity::getProductSeriesId)
                .toList();
        log.debug("Found {} product series: {}", filteredProductSeries.size(), productSeriesIds);

        // 建立產品系列映射（按品牌分組）
        Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap = filteredProductSeries.stream()
                .collect(Collectors.groupingBy(PartNumberProductSeriesEntity::getBrandId));

        // 步驟 3: 查詢供應商可見的回收詳情
        List<PartNumberRecoveryDetailEntity> visibleRecoveryDetails = partNumberRecoveryDetailRepository.findVisibleAndEnabledByPartnerId(partnerId);
        if (visibleRecoveryDetails.isEmpty()) {
            log.info("No visible recovery details found for partnerId: {}", partnerId);
            return SupplierQueryResult.empty();
        }

        List<String> visiblePartNumberIds = visibleRecoveryDetails.stream()
                .map(detail -> detail.getId().getPartNumberId())
                .toList();
        log.debug("Found {} visible part numbers for supplier", visiblePartNumberIds.size());

        // 步驟 4: 查詢這些產品系列下且供應商可見的料件（支援分頁和關鍵字過濾）
        Page<PartNumberEntity> partNumberPage;
        List<PartNumberEntity> partNumbers;
        long totalElements;

        if (pageable != null) {
            partNumberPage = partNumberRepository.findRecyclablePartNumbersByProductSeriesIdsAndPartNumberIdsPageable(
                    RecycleStatus.RECYCLABLE, productSeriesIds, visiblePartNumberIds, keyword, pageable);
            partNumbers = partNumberPage.getContent();
            totalElements = partNumberPage.getTotalElements();
        } else {
            partNumbers = partNumberRepository.findRecyclablePartNumbersByProductSeriesIdsAndPartNumberIds(
                    RecycleStatus.RECYCLABLE, productSeriesIds, visiblePartNumberIds, keyword);
            totalElements = partNumbers.size();
        }

        log.debug("Found {} part numbers with total elements: {}", partNumbers.size(), totalElements);

        // 步驟 5: 建立回收詳情映射
        Map<String, PartNumberRecoveryDetailEntity> recoveryDetailMap = visibleRecoveryDetails.stream()
                .collect(Collectors.toMap(
                        detail -> detail.getId().getPartNumberId(),
                        detail -> detail,
                        (a, b) -> a
                ));

        // 步驟 6: 根據關鍵字過濾供應商品牌（如果有關鍵字且有結果）
        List<PartnerBrandEntity> filteredPartnerBrands = partnerBrands;
        if (keyword != null && !keyword.trim().isEmpty() && !partNumbers.isEmpty()) {
            filteredPartnerBrands = filterPartnerBrandsByKeyword(
                    partnerBrands, brandMap, partNumbers, productSeriesMap, keyword);
        }

        return SupplierQueryResult.builder()
                .partnerBrands(filteredPartnerBrands)
                .brandMap(brandMap)
                .productSeriesMap(productSeriesMap)
                .partNumbers(partNumbers)
                .recoveryDetailMap(recoveryDetailMap)
                .totalElements(totalElements)
                .build();
    }

    /**
     * 查詢指定品牌下的產品系列（根據 productSeriesId 過濾）
     *
     * @param brandIds        品牌ID列表
     * @param productSeriesId 產品系列ID（可選）
     * @return 產品系列列表
     */
    private List<PartNumberProductSeriesEntity> queryProductSeriesForBrands(List<String> brandIds, String productSeriesId) {
        if (productSeriesId != null && !productSeriesId.trim().isEmpty()) {
            // 如果指定了產品系列ID，查詢該產品系列是否屬於指定品牌
            Optional<PartNumberProductSeriesEntity> productSeries = partNumberProductSeriesRepository.findById(productSeriesId);
            if (productSeries.isPresent() && brandIds.contains(productSeries.get().getBrandId()) && Boolean.TRUE.equals(productSeries.get().getEnabled())) {
                return List.of(productSeries.get());
            } else {
                return List.of(); // 產品系列不存在或不屬於指定品牌
            }
        } else {
            // 查詢所有指定品牌下的產品系列
            return partNumberProductSeriesRepository.findByBrandIdIn(brandIds)
                    .stream()
                    .filter(ps -> Boolean.TRUE.equals(ps.getEnabled()))
                    .toList();
        }
    }

    /**
     * 查詢直接商店的品牌
     */
    private List<PartNumberBrandEntity> queryBrandsForDirectStore(String brandId, String keyword) {
        if (brandId != null) {
            Optional<PartNumberBrandEntity> brandEntity = partNumberBrandRepository.findById(brandId);
            return brandEntity.map(List::of).orElse(List.of());
        } else {
            return partNumberBrandRepository.findEnabledBrandsWithKeyword(keyword);
        }
    }

    /**
     * 查詢供應商關聯的品牌
     */
    private List<PartnerBrandEntity> queryPartnerBrands(String partnerId, String brandId) {
        if (brandId != null) {
            Optional<PartnerBrandEntity> brandEntity =
                    partnerBrandRepository.findById(new PartnerBrandId(partnerId, brandId));
            return brandEntity.map(List::of).orElse(List.of());
        } else {
            return partnerBrandRepository.findById_PartnerIdAndEnabledIsTrueAndVisibleToSupplierIsTrue(partnerId);
        }
    }

    /**
     * 根據關鍵字過濾供應商品牌
     */
    private List<PartnerBrandEntity> filterPartnerBrandsByKeyword(
            List<PartnerBrandEntity> partnerBrands,
            Map<String, PartNumberBrandEntity> brandMap,
            List<PartNumberEntity> partNumbers,
            Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap,
            String keyword) {

        String lowerKeyword = keyword.toLowerCase();
        Set<String> matchingBrandIds = new HashSet<>();

        // 1. 收集品牌名稱包含關鍵字的品牌ID
        partnerBrands.stream()
                .filter(pb -> {
                    PartNumberBrandEntity brand = brandMap.get(pb.getId().getBrandId());
                    return brand != null && brand.getBrandName() != null &&
                            brand.getBrandName().toLowerCase().contains(lowerKeyword);
                })
                .forEach(pb -> matchingBrandIds.add(pb.getId().getBrandId()));

        // 2. 收集商品名稱包含關鍵字的品牌ID
        for (PartNumberEntity partNumber : partNumbers) {
            String productSeriesId = partNumber.getProductSeriesId();
            if (productSeriesId != null) {
                // 查找該產品系列所屬的品牌
                for (Map.Entry<String, List<PartNumberProductSeriesEntity>> entry : productSeriesMap.entrySet()) {
                    String brandId = entry.getKey();
                    List<PartNumberProductSeriesEntity> seriesList = entry.getValue();

                    boolean seriesFound = seriesList.stream()
                            .anyMatch(series -> series.getProductSeriesId().equals(productSeriesId));

                    if (seriesFound) {
                        matchingBrandIds.add(brandId);
                        break;
                    }
                }
            }
        }

        return partnerBrands.stream()
                .filter(pb -> matchingBrandIds.contains(pb.getId().getBrandId()))
                .toList();
    }

    /**
     * 直接商店查詢結果
     */
    @Getter
    @Builder
    public static class DirectStoreQueryResult {
        private final List<PartNumberBrandEntity> brands;
        private final Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap;
        private final List<PartNumberEntity> partNumbers;
        private final long totalElements;

        public boolean isEmpty() {
            return brands == null || brands.isEmpty();
        }

        public static DirectStoreQueryResult empty() {
            return DirectStoreQueryResult.builder()
                    .brands(Collections.emptyList())
                    .productSeriesMap(Collections.emptyMap())
                    .partNumbers(Collections.emptyList())
                    .totalElements(0)
                    .build();
        }
    }

    /**
     * 供應商查詢結果
     */
    @Getter
    @Builder
    public static class SupplierQueryResult {
        private final List<PartnerBrandEntity> partnerBrands;
        private final Map<String, PartNumberBrandEntity> brandMap;
        private final Map<String, List<PartNumberProductSeriesEntity>> productSeriesMap;
        private final List<PartNumberEntity> partNumbers;
        private final Map<String, PartNumberRecoveryDetailEntity> recoveryDetailMap;
        private final long totalElements;

        public boolean isEmpty() {
            return partnerBrands == null || partnerBrands.isEmpty();
        }

        public static SupplierQueryResult empty() {
            return SupplierQueryResult.builder()
                    .partnerBrands(Collections.emptyList())
                    .brandMap(Collections.emptyMap())
                    .productSeriesMap(Collections.emptyMap())
                    .partNumbers(Collections.emptyList())
                    .recoveryDetailMap(Collections.emptyMap())
                    .totalElements(0)
                    .build();
        }
    }
}
