-- ========================================
-- V28: 員工銀行帳戶預設國家代碼為台灣，並將台灣銀行代碼統一為3位數(左補0)
-- 描述:
-- 1) 將 sm_employee_banking_profile.country_code_id 預設為台灣(TW)的 country_code_id
-- 2) 將 sm_bank_code 中屬於台灣(TW)的 bank_code 一律格式化為3位數(左補'0')
-- 3) 新增條件式檢查約束，限制台灣(TW)銀行代碼長度必須為3
-- ========================================

-- 台灣(TW)的 country_code_id，源自 V8 匯入資料
-- INSERT 參考: ('e8d66a03-d095-4559-9841-40b86606706a', 'TW', '臺灣', TRUE, 'import', ...)
DO $$
BEGIN
    -- 安全檢查: 若 TW 不存在則拋出例外 (避免錯誤的預設值)
    IF NOT EXISTS (
        SELECT 1 FROM sm_country_code WHERE country_code_id = 'e8d66a03-d095-4559-9841-40b86606706a' AND country_code = 'TW'
    ) THEN
        RAISE EXCEPTION 'TW country_code_id not found in sm_country_code';
    END IF;
END $$;

-- 1) 設定員工銀行資料預設國家為台灣
ALTER TABLE sm_employee_banking_profile
    ALTER COLUMN country_code_id SET DEFAULT 'e8d66a03-d095-4559-9841-40b86606706a';

COMMENT ON COLUMN sm_employee_banking_profile.country_code_id IS '國家代碼ID，預設為台灣(TW)';

-- 2) 將台灣銀行代碼左補0為3位數
UPDATE sm_bank_code
   SET bank_code = LPAD(bank_code, 3, '0')
 WHERE country_code_id = 'e8d66a03-d095-4559-9841-40b86606706a'
   AND bank_code IS NOT NULL
   AND length(bank_code) < 3;

-- 3) 新增條件式檢查約束: 台灣銀行代碼長度必須為3
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'ck_sm_bank_code_tw_len3'
    ) THEN
        ALTER TABLE sm_bank_code
            ADD CONSTRAINT ck_sm_bank_code_tw_len3
            CHECK (
                country_code_id <> 'e8d66a03-d095-4559-9841-40b86606706a' OR length(bank_code) = 3
            );
    END IF;
END $$;

COMMENT ON CONSTRAINT ck_sm_bank_code_tw_len3 ON sm_bank_code IS '台灣(TW)銀行代碼長度限制為3位';
