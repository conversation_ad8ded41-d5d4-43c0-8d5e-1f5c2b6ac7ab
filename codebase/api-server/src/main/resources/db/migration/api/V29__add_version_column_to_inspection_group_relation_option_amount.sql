-- ========================================
-- V29: 為檢測群組關聯選項金額設定表新增版本欄位
-- 描述: 修復 InspectionGroupRelationOptionAmountEntity 實體與資料庫表結構不一致的問題
-- 問題: 實體定義了 @Version 註解的 version 欄位，但資料庫表中缺少對應欄位
-- 解決: 新增 version 欄位以支援樂觀鎖機制
-- ========================================

-- 1. 新增 version 欄位
ALTER TABLE sm_inspection_group_relation_option_amount 
ADD COLUMN version BIGINT DEFAULT 0;

-- 2. 為現有資料設定預設版本號
UPDATE sm_inspection_group_relation_option_amount 
SET version = 0 
WHERE version IS NULL;

-- 3. 設定 version 欄位為 NOT NULL
ALTER TABLE sm_inspection_group_relation_option_amount 
ALTER COLUMN version SET NOT NULL;

-- 4. 新增欄位註解
COMMENT ON COLUMN sm_inspection_group_relation_option_amount.version IS '版本號，用於樂觀鎖機制，防止併發更新衝突';

-- ========================================
-- 驗證修復結果
-- ========================================
-- 此修復解決了以下錯誤：
-- JDBC exception executing SQL [...] 
-- [ERROR: column igroae1_0.version does not exist Position: 297]
-- 
-- 修復後，JPA 查詢將能正常執行，包括：
-- - InspectionGroupService.getInspectionGroup()
-- - InspectionGroupRelationOptionAmountRepository.findByGroupIdAndEnabledTrue()
-- - 檢測群組新增/編輯 API (POST/PUT /api/inspection/group)
