package com.jgallop.api.service;

import com.jgallop.api.entity.EmployeeEntity;
import com.jgallop.api.entity.EmployeeUnitEntity;
import com.jgallop.api.entity.EmployeeUnitId;
import com.jgallop.api.model.resp.RespEmployeeDetailModel;
import com.jgallop.api.model.resp.RespEmployeeQueryModel;
import com.jgallop.api.repository.EmployeeRepository;
import com.jgallop.common.service.MessageHelper;
import com.jgallop.common.service.audit.AuditLogService;
import com.jgallop.hfs.service.FileManagementHelper;
import com.jgallop.user.entity.DataScope;
import com.jgallop.user.entity.UserEntity;
import com.jgallop.user.entity.UserType;
import com.jgallop.user.enums.PermissionName;
import com.jgallop.user.service.UserHelper;
import com.jgallop.user.util.FieldMaskingUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 員工 dataScope 權限測試
 */
@ExtendWith(MockitoExtension.class)
class EmployeeDataScopeTest {

    @Mock
    private UserHelper userHelper;

    @Mock
    private EmployeeHelper employeeHelper;

    @Mock
    private EmployeeUnitHelper employeeUnitHelper;

    @Mock
    private EmployeeRepository employeeRepository;

    @Mock
    private MessageHelper messageHelper;

    @Mock
    private FieldMaskingUtils fieldMaskingUtils;

    @Mock
    private FileHelper fileHelper;

    @Mock
    private CompanyHelper companyHelper;

    @Mock
    private ExcelGenerator excelGenerator;

    @Mock
    private SystemAccountHelper systemAccountHelper;

    @Mock
    private FileManagementHelper fileManagementHelper;

    @Mock
    private AuditLogService auditLogService;

    private EmployeeService employeeService;

    @BeforeEach
    void setUp() {
        employeeService = new EmployeeService(
                userHelper, fileHelper, companyHelper, employeeHelper, excelGenerator,
                employeeUnitHelper, systemAccountHelper, fileManagementHelper,
                employeeRepository, messageHelper, fieldMaskingUtils, auditLogService
        );
    }

    @Test
    void testQueryEmployee_WithSelfScope_ShouldReturnOnlyCurrentUser() {
        // Given
        String currentEmployeeId = "emp-001";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity currentEmployee = createEmployeeEntity(currentEmployeeId, "company-001");

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdOrThrow(currentEmployeeId)).thenReturn(currentEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(DataScope.SELF);

        Page<EmployeeEntity> mockPage = new PageImpl<>(Collections.singletonList(currentEmployee));
        when(employeeRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(mockPage);

        // When
        RespEmployeeQueryModel result = employeeService.queryEmployee(
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, 0, 10
        );

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getRespEmployeeDetailModels().size());

        // Verify that the specification includes SELF scope filtering
        verify(employeeRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testQueryEmployee_WithDeptScope_ShouldReturnDepartmentEmployees() {
        // Given
        String currentEmployeeId = "emp-001";
        String unitId = "unit-001";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity currentEmployee = createEmployeeEntity(currentEmployeeId, "company-001");

        EmployeeUnitEntity employeeUnit = new EmployeeUnitEntity();
        EmployeeUnitId employeeUnitId = new EmployeeUnitId();
        employeeUnitId.setEmployeeId(currentEmployeeId);
        employeeUnitId.setUnitId(unitId);
        employeeUnit.setId(employeeUnitId);
        employeeUnit.setEnabled(true);

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdOrThrow(currentEmployeeId)).thenReturn(currentEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(DataScope.DEPT);
        when(employeeUnitHelper.getEmployeeUnitByEmployeeId(currentEmployeeId))
                .thenReturn(Collections.singletonList(employeeUnit));

        EmployeeEntity deptEmployee1 = createEmployeeEntity("emp-002", "company-001");
        EmployeeEntity deptEmployee2 = createEmployeeEntity("emp-003", "company-001");
        Page<EmployeeEntity> mockPage = new PageImpl<>(Arrays.asList(currentEmployee, deptEmployee1, deptEmployee2));
        when(employeeRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(mockPage);

        // When
        RespEmployeeQueryModel result = employeeService.queryEmployee(
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, 0, 10
        );

        // Then
        assertNotNull(result);
        assertEquals(3, result.getTotalElements());
        assertEquals(3, result.getRespEmployeeDetailModels().size());

        // Verify that the specification includes DEPT scope filtering
        verify(employeeRepository).findAll(any(Specification.class), any(Pageable.class));
        verify(employeeUnitHelper).getEmployeeUnitByEmployeeId(currentEmployeeId);
    }

    @Test
    void testQueryEmployee_WithAllScope_ShouldReturnAllEmployees() {
        // Given
        String currentEmployeeId = "emp-001";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity currentEmployee = createEmployeeEntity(currentEmployeeId, "company-001");

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdOrThrow(currentEmployeeId)).thenReturn(currentEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(DataScope.ALL);

        EmployeeEntity employee1 = createEmployeeEntity("emp-002", "company-001");
        EmployeeEntity employee2 = createEmployeeEntity("emp-003", "company-002");
        Page<EmployeeEntity> mockPage = new PageImpl<>(Arrays.asList(currentEmployee, employee1, employee2));
        when(employeeRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(mockPage);

        // When
        RespEmployeeQueryModel result = employeeService.queryEmployee(
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, 0, 10
        );

        // Then
        assertNotNull(result);
        assertEquals(3, result.getTotalElements());
        assertEquals(3, result.getRespEmployeeDetailModels().size());

        // Verify that no additional filtering is applied for ALL scope
        verify(employeeRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    void testGetEmployeeByEmployeeId_WithSelfScope_ShouldAllowAccessToOwnData() {
        // Given
        String currentEmployeeId = "emp-001";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity currentEmployee = createEmployeeEntity(currentEmployeeId, "company-001");

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdAndEnabledOrThrow(currentEmployeeId)).thenReturn(currentEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(DataScope.SELF);

        // When
        RespEmployeeDetailModel result = employeeService.getEmployeeByEmployeeId(currentEmployeeId);

        // Then
        assertNotNull(result);
        assertEquals(currentEmployeeId, result.getEmployeeId());
    }

    @Test
    void testGetEmployeeByEmployeeId_WithSelfScope_ShouldDenyAccessToOtherData() {
        // Given
        String currentEmployeeId = "emp-001";
        String targetEmployeeId = "emp-002";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity targetEmployee = createEmployeeEntity(targetEmployeeId, "company-001");

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdAndEnabledOrThrow(targetEmployeeId)).thenReturn(targetEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(DataScope.SELF);
        when(messageHelper.localizedMessage("error.employee.access.denied")).thenReturn("您沒有權限存取此員工資料");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> employeeService.getEmployeeByEmployeeId(targetEmployeeId));

        assertEquals("您沒有權限存取此員工資料", exception.getMessage());
    }

    @Test
    void testDataScope_WithNullEmployeeId_ShouldThrowException() {
        // Given
        UserEntity userWithoutEmployee = new UserEntity();
        userWithoutEmployee.setUserId("user-001");
        userWithoutEmployee.setUserType(UserType.EMPLOYEE);
        userWithoutEmployee.setEmployeeId(null); // No employee association

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(userWithoutEmployee);
        when(messageHelper.localizedMessage("error.user.no.employee.association"))
                .thenReturn("用戶未關聯員工資訊");

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () -> employeeService.queryEmployee(
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, 0, 10
        ));

        assertEquals("用戶未關聯員工資訊", exception.getMessage());
    }

    @Test
    void testDataScope_WithNullScope_ShouldThrowException() {
        // Given
        String currentEmployeeId = "emp-001";
        UserEntity currentUser = createUserEntity(currentEmployeeId);
        EmployeeEntity currentEmployee = createEmployeeEntity(currentEmployeeId, "company-001");

        when(userHelper.findUserEntityFromAuthentication()).thenReturn(currentUser);
        when(employeeHelper.findEmployeeByEmployeeIdOrThrow(currentEmployeeId)).thenReturn(currentEmployee);
        when(fieldMaskingUtils.getDataScope(PermissionName.EMPLOYEE)).thenReturn(null);
        when(messageHelper.localizedMessage("error.data.scope.null")).thenReturn("資料範圍權限未設定");

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> employeeService.queryEmployee(
                null, null, null, null, null, null, null, null, null,
                null, null, null, null, null, null, 0, 10
        ));

        assertEquals("資料範圍權限未設定", exception.getMessage());
    }

    private UserEntity createUserEntity(String employeeId) {
        UserEntity user = new UserEntity();
        user.setUserId("user-" + employeeId);
        user.setUserType(UserType.EMPLOYEE);
        user.setEmployeeId(employeeId);
        return user;
    }

    private EmployeeEntity createEmployeeEntity(String employeeId, String companyId) {
        EmployeeEntity employee = new EmployeeEntity();
        employee.setEmployeeId(employeeId);
        employee.setCompanyId(companyId);
        employee.setZhName("Employee " + employeeId);
        employee.setEmployeeNumber("EMP" + employeeId.substring(4));
        employee.setEnabled(true);
        return employee;
    }
}
