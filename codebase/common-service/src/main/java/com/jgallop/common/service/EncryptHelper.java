package com.jgallop.common.service;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.UUID;

/**
 * <code>EncryptHelper</code> provides data encryption and decryption services
 */
@Service
public class EncryptHelper {

    @Value("${encrypt.rsa.keysize}")
    private int rsaKeySize;

    @Value("${encrypt.aes.keysize}")
    private int aesKeySize;

    @Value("${encrypt.aes.key}")
    private String defaultAesKey;

    @Value("${encrypt.iv}")
    private String defaultIv;

    /**
     * 產生 RSA 公鑰與私鑰
     */
    public KeyPair generateRSAKeyPair() throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(rsaKeySize);
        return keyPairGenerator.generateKeyPair();
    }

    /**
     * 產生 AES 密鑰
     */
    public SecretKey generateAESKey() throws Exception {
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        keyGenerator.init(aesKeySize);
        return keyGenerator.generateKey();
    }

    /**
     * 從指定 key 建立 AES {@link SecretKey} 物件
     */
    public SecretKey prepareAESKey(byte[] keyBytes) {
        return new SecretKeySpec(keyBytes, "AES");
    }

    /**
     * 使用 RSA 公鑰加密 AES 密鑰
     */
    public byte[] encryptAESKeyWithRSAPublicKey(SecretKey aesKey, PublicKey publicKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        return cipher.doFinal(aesKey.getEncoded());
    }

    /**
     * 使用 RSA 私鑰解密 AES 密鑰
     */
    public SecretKey decryptAESKeyWithRSAPrivateKey(byte[] encryptedAESKey, PrivateKey privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] decryptedKeyBytes = cipher.doFinal(encryptedAESKey);
        return new SecretKeySpec(decryptedKeyBytes, "AES");
    }

    /**
     * 使用 AES 與 IV 值加密資料，再使用 Base64 編碼後回傳
     * <br>
     * 此 method 同 {@link #encryptDataWithAES(SecretKey, byte[], byte[])}，但是輸入參數改為字串型態，回傳值為 Base64 編碼後的字串
     */
    public String encryptDataWithAES(String base64AESKey, String iv, String data) throws Exception {
        SecretKey secretKey = prepareAESKey(base64Decode(base64AESKey));
        byte[] ivBytes = base64Decode(iv);
        byte[] rawDataBytes = toBytes(data);
        return base64Encode(encryptDataWithAES(secretKey, ivBytes, rawDataBytes));
    }

    /**
     * 使用 AES 與 IV 值解密資料，再使用 Base64 編碼後回傳
     * <br>
     * 此 method 同 {@link #decryptDataWithAES(SecretKey, byte[], byte[])}，但是輸入參數改為字串型態，回傳值為 Base64 編碼後的字串
     */
    public String decryptDataWithAES(String base64AESKey, String iv, String encryptedData) throws Exception {
        SecretKey secretKey = prepareAESKey(base64Decode(base64AESKey));
        byte[] ivBytes = base64Decode(iv);
        byte[] encryptedDataBytes = toBytes(encryptedData);
        return base64Encode(decryptDataWithAES(secretKey, ivBytes, encryptedDataBytes));
    }

    /**
     * 使用 AES 加密資料
     * <br>
     * 使用 <code>aesKey</code> 與 <code>iv</code> 加密 <code>data</code> 後回傳。
     * 注意：若之後要解密此回傳值，必須使用相同的 <code>aesKey</code> 與 <code>iv</code> 才能解密。
     * <br>
     * 這裡所謂相同的 <code>aesKey</code>，是指相同的 key byte array，透過 {@link #prepareAESKey(byte[])} 建立的 {@link SecretKey}。
     */
    public byte[] encryptDataWithAES(SecretKey aesKey, byte[] iv, byte[] data) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, aesKey, ivParameterSpec);
        return cipher.doFinal(data);
    }

    /**
     * 使用 AES 解密資料
     */
    public byte[] decryptDataWithAES(SecretKey aesKey, byte[] iv, byte[] encryptedData) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, aesKey, ivParameterSpec);
        return cipher.doFinal(encryptedData);
    }

    public String publicKeyToBase64String(PublicKey publicKey) {
        return base64Encode(publicKey.getEncoded());
    }

    public String privateKeyToBase64String(PrivateKey privateKey) {
        return base64Encode(privateKey.getEncoded());
    }

    public PublicKey base64StringToPublicKey(String base64PublicKeyString) throws Exception {
        byte[] bytes = base64Decode(base64PublicKeyString);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(bytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    public PrivateKey base64StringToPrivateKey(String base64PrivateKeyString) throws Exception {
        byte[] bytes = base64Decode(base64PrivateKeyString);
        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(bytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePrivate(spec);
    }

    public String base64Encode(byte[] rawBytes) {
        return Base64.getEncoder().encodeToString(rawBytes);
    }

    public byte[] base64Decode(String base64String) {
        return Base64.getDecoder().decode(base64String);
    }

    public String toString(byte[] bytes) {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    public byte[] toBytes(String stringValue) {
        return stringValue.getBytes(StandardCharsets.UTF_8);
    }

    public UUID generateUUID() {
        return UUID.randomUUID();
    }

    /**
     * 取得預設的 AES 密鑰
     */
    public SecretKey getDefaultAesKey() {
        return prepareAESKey(base64Decode(defaultAesKey));
    }

    /**
     * 使用預設 AES 密鑰與 IV 值加密非空值的字串後，經過 Base64 編碼後回傳。
     * <br>
     * 若字串為空值，包含 {@code null} 或者 {@code ""} 則直接回傳原字串
     * <br>
     * 加密方式使用預設 AES 設定
     */
    public String encryptDataWithDefaultAES(String data) throws Exception {
        if (StringUtils.isEmpty(data)) {
            return data;
        }

        byte[] ivBytes = base64Decode(defaultIv);
        byte[] rawDataBytes = toBytes(data);
        return base64Encode(encryptDataWithAES(getDefaultAesKey(), ivBytes, rawDataBytes));
    }

    /**
     * 非空值的字串經過 Base64 解碼，再使用預設 AES 密鑰與 IV 值解密後回傳。
     * <br>
     * 若字串為空值，包含 {@code null} 或者 {@code ""} 則直接回傳原字串
     * <br>
     * 解密方式使用預設 AES 設定
     */
    public String decryptDataWithDefaultAES(String base64EncryptedData) throws Exception {
        if (StringUtils.isEmpty(base64EncryptedData)) {
            return base64EncryptedData;
        }

        byte[] ivBytes = base64Decode(defaultIv);
        byte[] encryptedDataBytes = base64Decode(base64EncryptedData);
        return toString(decryptDataWithAES(getDefaultAesKey(), ivBytes, encryptedDataBytes));
    }
}
