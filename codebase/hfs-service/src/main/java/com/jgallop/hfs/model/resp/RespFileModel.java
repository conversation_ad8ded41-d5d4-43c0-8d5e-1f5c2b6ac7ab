package com.jgallop.hfs.model.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RespFileModel {

    @JsonProperty(value = "fileId")
    private String fileId;

    @JsonProperty(value = "filePath")
    private String filePath;

    @JsonProperty(value = "fileName")
    private String fileName;

}
