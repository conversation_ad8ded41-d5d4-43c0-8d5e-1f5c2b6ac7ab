package com.jgallop.log.advice;

import com.jgallop.log.annotation.NoLogToBody;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.RequestBodyAdviceAdapter;

import java.lang.reflect.Type;
import java.util.Objects;

/**
 * <code>RequestBodyLoggingHandler</code> 紀錄 request body 內容。
 * <br>
 * 由於 reqeust body，若在 <code>LoggingInterceptor</code> 中呼叫 request.getReader() 後，
 * controller 就無法再取得 request body 了，故改寫到此 advise。<br>
 * 若 class 或 method 設定 annotation {@link com.jgallop.log.annotation.NoLogToBody} 時即不紀錄 request body 的內容。
 *
 * @see NoLogToBody
 * @see com.jgallop.log.config.LoggingInterceptor
 */
@Slf4j
@ControllerAdvice
public class RequestBodyLoggingHandler extends RequestBodyAdviceAdapter {

    /**
     * Determines whether the given method parameter, target type, and converter type are supported.
     *
     * @param methodParameter  the method parameter
     * @param targetType      the target type
     * @param converterType   the converter type
     * @return true if the method parameter and method of the containing class do not have the {@link NoLogToBody} annotation; false otherwise
     */
    @Override
    public boolean supports(@NonNull MethodParameter methodParameter, @NonNull Type targetType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        return !methodParameter.getContainingClass().isAnnotationPresent(NoLogToBody.class)
                && !Objects.requireNonNull(methodParameter.getMethod()).isAnnotationPresent(NoLogToBody.class);
    }

    @Override
    public @NonNull Object afterBodyRead(@NonNull Object body, @NonNull HttpInputMessage inputMessage, @NonNull MethodParameter parameter, @NonNull Type targetType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        MDC.put("body", body.toString());

        log.info("Request body: {}", body);

        return super.afterBodyRead(body, inputMessage, parameter, targetType, converterType);
    }
}
