package com.jgallop.log.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <code>NoLogToHeader</code> 標記此註解於 request mapping 的 controller class，則前端呼叫此 class 下的所有 method 時，日誌不紀錄 request headers 的內容。<br>
 * 此標記若宣告在單一 method，則上述規則只套用在此單一 method。<br>
 * 若 request mapping 的 controller 或 method 有下列情況，應考慮加上此標記：
 * <ul>
 *     <li>含有敏感資訊：</li>如果請求包含敏感資料，如密碼、個人識別資訊（PII）、信用卡號碼等，這些資訊不應該被記錄以避免安全漏洞
 *     <li>法規遵循：</li>某些法規，如歐盟的一般資料保護規範（GDPR），對個人資料的處理和儲存有嚴格要求。記錄包含個人資料的請求可能會違反這些規範
 *     <li>性能考慮：</li>對於高頻率或大量請求的 API，記錄每個請求的所有細節可能會對系統性能造成負擔，尤其是在高流量的情況下
 *     <li>安全性政策：</li>某些組織或應用可能有嚴格的安全性政策，限制哪些類型的數據可以被記錄，以降低資料洩露的風險
 *     <li>API 的特性：</li>對於某些類型的 API（例如，健康照護、金融服務等），出於保密和合規性的原因，可能會選擇不記錄任何請求細節
 *     <li>第三方服務：</li>如果 API 交互涉及第三方服務，可能會有合約或協議條款禁止記錄特定的請求資訊
 * </ul>
 *
 * @see NoLogToHeaderAndParam
 * @see NoLogToParam
 * @see NoLogToBody
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NoLogToHeader {
}
