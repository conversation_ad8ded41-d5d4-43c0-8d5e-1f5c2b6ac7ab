package com.jgallop.mail.config;

import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.gmail.Gmail;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.UserCredentials;
import com.jgallop.mail.service.GoogleSendMailProcessor;
import com.jgallop.mail.service.JavaSendMailProcessor;
import com.jgallop.mail.service.SendMailProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.mail.javamail.JavaMailSender;

/**
 * <code>SendMailProcessorConfig</code> configures {@link SendMailProcessor}
 *
 */
@Configuration
@Slf4j
public class SendMailProcessorConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    @ConditionalOnProperty(name = "send.mail.type", havingValue = "java")
    public SendMailProcessor javaSendMailProcessor() throws Throwable {
        log.info("由於屬性 `send.mail.type` 值為 'java'，系統將使用 JavaMail 發送 mail");

        JavaMailSender mailSender = applicationContext.getBean(JavaMailSender.class);
        return new JavaSendMailProcessor(mailSender, applicationContext);
    }

    @Bean
    @ConditionalOnProperty(name = "send.mail.type", havingValue = "gmail")
    public SendMailProcessor googleSendMailProcessor() throws Throwable {
        log.info("由於屬性 `send.mail.type` 值為 'gmail'，系統將使用 Gmail API 發送 mail");

        final NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        final JsonFactory jsonFactory = GsonFactory.getDefaultInstance();

        final Environment environment = applicationContext.getEnvironment();
        final String clientId = environment.getProperty("mail.google.oauth2.client-id");
        final String clientSecret = environment.getProperty("mail.google.oauth2.client-secret");
        final String refreshToken = environment.getProperty("mail.google.oauth2.refresh-token");
        final String applicationName = environment.getProperty("mail.google.oauth2.application-name");

        GoogleCredentials googleCredentials = UserCredentials.newBuilder().setClientId(clientId).setClientSecret(clientSecret).setRefreshToken(refreshToken).build();
        HttpRequestInitializer credentials = new HttpCredentialsAdapter(googleCredentials);

        Gmail service = new Gmail.Builder(httpTransport, jsonFactory, credentials).setApplicationName(applicationName).build();

        return new GoogleSendMailProcessor(service, applicationContext);
    }
}
