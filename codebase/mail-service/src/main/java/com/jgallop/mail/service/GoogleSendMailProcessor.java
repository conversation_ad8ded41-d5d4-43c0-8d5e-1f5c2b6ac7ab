package com.jgallop.mail.service;

import com.google.api.services.gmail.Gmail;
import com.google.api.services.gmail.model.Message;
import com.jgallop.mail.dto.MailMessageDto;
import com.jgallop.mail.model.MailMessageModel;
import org.springframework.context.ApplicationContext;

/**
 * <code>GoogleSendMailProcessor</code> sends mails by using Google mail api.
 *
 */
public class GoogleSendMailProcessor implements SendMailProcessor {

    private final Gmail gmail;
    private final ApplicationContext applicationContext;

    public GoogleSendMailProcessor(Gmail gmail, ApplicationContext applicationContext) {
        this.gmail = gmail;
        this.applicationContext = applicationContext;
    }

    @Override
    public void send(MailMessageModel model) throws Throwable {
        String from = model.getFrom();
        Message message = MailMessageDto.convertToGmailMesssage(model);
        gmail.users().messages().send(from, message).execute();
    }

    @Override
    public String fromEmail() {
        return applicationContext.getEnvironment().getProperty("mail.google.oauth2.smtp.from");
    }
}
