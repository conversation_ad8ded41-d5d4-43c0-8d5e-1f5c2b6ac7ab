package com.jgallop.mail.service;

import com.jgallop.mail.dto.MailMessageDto;
import com.jgallop.mail.model.MailMessageModel;
import jakarta.mail.internet.MimeMessage;
import org.springframework.context.ApplicationContext;
import org.springframework.mail.javamail.JavaMailSender;

/**
 * <code>JavaSendMailProcessor</code> sends mails by using {@link JavaMailSender}.
 *
 */
public class JavaSendMailProcessor implements SendMailProcessor {

    private final JavaMailSender mailSender;

    private final ApplicationContext applicationContext;

    public JavaSendMailProcessor(JavaMailSender mailSender, ApplicationContext applicationContext) {
        this.mailSender = mailSender;
        this.applicationContext = applicationContext;
    }

    @Override
    public void send(MailMessageModel model) throws Throwable {
        MimeMessage mimeMessage = mailSender.createMimeMessage();
        MailMessageDto.mergeMimeMesssageWithModel(mimeMessage, model);
        mailSender.send(mimeMessage);
    }

    @Override
    public String fromEmail() {
        return applicationContext.getEnvironment().getProperty("spring.mail.properties.mail.smtp.from");
    }
}
