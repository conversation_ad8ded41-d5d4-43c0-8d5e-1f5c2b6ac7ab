package com.jgallop.mail.service;


import com.jgallop.mail.model.MailMessageModel;

/**
 * <code>SendMailProcessor</code> provides mail sending related methods.
 *
 */
public interface SendMailProcessor {

    /**
     * Send an email with the specified {@link MailMessageModel}
     */
    void send(MailMessageModel model) throws Throwable;

    /**
     * Retrieves the email address of the sender.
     */
    String fromEmail();
}
