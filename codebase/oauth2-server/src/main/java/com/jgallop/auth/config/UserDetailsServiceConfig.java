package com.jgallop.auth.config;

import com.jgallop.auth.service.JgallopUserDetailsService;
import com.jgallop.user.service.UserHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.userdetails.UserDetailsService;

/**
 * <code>UserDetailsServiceConfig</code> configures the component for {@link UserDetailsService}
 * <br>
 * {@link UserDetailsService} 元件的宣告之所以要脫離其他的 <code>@Configuration</code> 是為了避免循環依賴造成下列錯誤：
 * <pre>
 *     unresolvable circular reference
 * </pre>
 */
@Configuration
@RequiredArgsConstructor
public class UserDetailsServiceConfig {

    private final UserHelper userHelper;

    /**
     * 提供 {@link UserDetailsService} 實作，用來取得認證時需要的用戶資訊
     */
    @Bean
    public UserDetailsService userDetailsService() {
        return new JgallopUserDetailsService(userHelper);
    }
}
