<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>忘記密碼 - 創宇整合平台</title>
    <link rel="stylesheet" href="login/styles.css">
    <style>
        .back-to-login {
            color: rgba(46, 131, 196, 1);
            font-size: 13px;
            font-weight: 700;
            line-height: 24px;
            margin-top: 16px;
            display: block;
            text-decoration: none;
            text-align: center;
        }

        .success-message {
            display: none;
            background-color: #4CAF50;
            color: white;
            padding: 16px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            background-color: #f44336;
            color: white;
            padding: 16px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        /* Tab styles */
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            width: 100%;
        }

        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: none;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 0 5px;
            transition: background-color 0.3s;
        }

        .tab-button.active {
            background-color: rgba(46, 131, 196, 1);
            color: white;
        }

        .tab-content {
            display: none;
            width: 100%;
        }

        .tab-content.active {
            display: block;
        }

        .input-field {
            box-sizing: border-box;
            width: 100%;
        }
    </style>
</head>
<body>
<main class="login-page">
    <form id="forgot-password-form" class="login-form" aria-labelledby="forgot-password-title">
        <div class="form-content">
            <header class="form-header">
                <h1 class="platform-title">創宇整合平台</h1>
            </header>

            <h2 class="login-title" id="forgot-password-title">忘記密碼</h2>

            <div class="tabs">
                <button type="button" class="tab-button active" data-tab="employee">員工</button>
                <button type="button" class="tab-button" data-tab="partner">供應商</button>
            </div>

            <!-- Employee Tab -->
            <div id="employee-tab" class="tab-content active">
                <div class="input-group">
                    <div class="form-field">
                        <label for="employee-account" class="field-label">登入帳號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="text"
                                       id="employee-account"
                                       class="input-field"
                                       placeholder="請輸入您的登入帳號"
                                       required/>
                            </div>
                        </div>
                    </div>
                    <div class="form-field">
                        <label for="employee-email" class="field-label">電子郵件</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="email"
                                       id="employee-email"
                                       class="input-field"
                                       placeholder="請輸入您的電子郵件"
                                       required/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Partner Tab -->
            <div id="partner-tab" class="tab-content">
                <div class="input-group">
                    <div class="form-field">
                        <label for="partner-code" class="field-label">供應商代號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="text"
                                       id="partner-code"
                                       class="input-field"
                                       placeholder="請輸入供應商代號"
                                       required/>
                            </div>
                        </div>
                    </div>
                    <div class="form-field">
                        <label for="partner-account" class="field-label">登入帳號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="text"
                                       id="partner-account"
                                       class="input-field"
                                       placeholder="請輸入您的登入帳號"
                                       required/>
                            </div>
                        </div>
                    </div>
                    <div class="form-field">
                        <label for="partner-email" class="field-label">電子郵件</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="email"
                                       id="partner-email"
                                       class="input-field"
                                       placeholder="請輸入您的電子郵件"
                                       required/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" id="submit" class="login-button">送出</button>

            <div id="success-message" class="success-message">
                重設密碼連結已寄出，請檢查您的電子郵件
            </div>

            <div id="error-message" class="error-message">
                發送重設密碼連結時發生錯誤，請稍後再試
            </div>

            <a href="/login/signin.html" class="back-to-login">返回登入頁面</a>
        </div>
    </form>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        let activeTab = 'employee';

        tabButtons.forEach(button => {
            button.addEventListener('click', function () {
                const tab = this.getAttribute('data-tab');

                // Update active tab
                tabButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Show active tab content
                tabContents.forEach(content => content.classList.remove('active'));
                document.getElementById(tab + '-tab').classList.add('active');

                // Update active tab variable
                activeTab = tab;

                // Set required attributes based on active tab
                updateRequiredFields(activeTab);
            });
        });

        function updateRequiredFields(tab) {
            // Remove required from all fields first
            document.getElementById('employee-account').required = false;
            document.getElementById('employee-email').required = false;
            document.getElementById('partner-code').required = false;
            document.getElementById('partner-account').required = false;
            document.getElementById('partner-email').required = false;

            // Add required to the fields of the active tab
            if (tab === 'employee') {
                document.getElementById('employee-account').required = true;
                document.getElementById('employee-email').required = true;
            } else if (tab === 'partner') {
                document.getElementById('partner-code').required = true;
                document.getElementById('partner-account').required = true;
                document.getElementById('partner-email').required = true;
            }
        }

        // Initial call to set required fields for the default tab
        updateRequiredFields(activeTab);

        const form = document.getElementById('forgot-password-form');
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');

        form.addEventListener('submit', function(event) {
            event.preventDefault();

            // 隱藏之前的訊息
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';

            let requestBody = {};

            if (activeTab === 'employee') {
                requestBody = {
                    userType: 'EMPLOYEE',
                    account: document.getElementById('employee-account').value,
                    email: document.getElementById('employee-email').value
                };
            } else {
                requestBody = {
                    userType: 'PARTNER',
                    partnerCode: document.getElementById('partner-code').value,
                    account: document.getElementById('partner-account').value,
                    email: document.getElementById('partner-email').value
                };
            }

            // 發送請求到忘記密碼API
            fetch('/api/auth/password/forgot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => {
                if (response.ok) {
                    // 顯示成功訊息
                    successMessage.style.display = 'block';
                    // 清空表單
                    form.reset();
                } else {
                    // 顯示錯誤訊息
                    errorMessage.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // 顯示錯誤訊息
                errorMessage.style.display = 'block';
            });
        });
    });
</script>
</body>
</html>
