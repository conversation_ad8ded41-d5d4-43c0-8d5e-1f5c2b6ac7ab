<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登入 - 創宇整合平台</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Tab styles */
        .tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
            width: 100%;
        }

        .tab-button {
            padding: 10px 20px;
            background-color: #f1f1f1;
            border: none;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 0 5px;
            transition: background-color 0.3s;
        }

        .tab-button.active {
            background-color: rgba(46, 131, 196, 1);
            color: white;
        }

        .tab-content {
            display: none;
            width: 100%;
        }

        .tab-content.active {
            display: block;
        }

        .input-field {
            box-sizing: border-box;
            width: 100%;
        }

    </style>
</head>
<body>
<main class="login-page">
    <form class="login-form" aria-labelledby="login-title" action="/login" method="post">
        <div class="form-content">
            <header class="form-header">
                <h1 class="platform-title">創宇整合平台</h1>
            </header>

            <div class="tabs">
                <button type="button" class="tab-button active" data-tab="employee">員工登入</button>
                <button type="button" class="tab-button" data-tab="partner">供應商登入</button>
            </div>

            <!-- Employee Login Tab -->
            <div id="employee-tab" class="tab-content active">
                <h2 class="login-title" id="employee-login-title">員工登入</h2>

                <div class="input-group">

                    <div class="form-field">
                        <label for="employee-account" class="field-label">登入帳號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="text"
                                       id="employee-account"
                                       class="input-field"
                                       value="admin"/>
                            </div>
                        </div>
                    </div>

                    <div class="form-field">
                        <label for="employee-password" class="field-label">登入密碼</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input type="password"
                                       id="employee-password"
                                       class="input-field"
                                       value="admin"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Partner Login Tab -->
            <div id="partner-tab" class="tab-content">
                <h2 class="login-title" id="partner-login-title">供應商登入</h2>

                <div class="input-group">
                    <div class="form-field">
                        <label for="supplier-code" class="field-label">供應商代號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input
                                        type="text"
                                        id="supplier-code"
                                        class="input-field"
                                        value="asus"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="form-field">
                        <label for="supplier-account" class="field-label">登入帳號</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input
                                        type="text"
                                        id="supplier-account"
                                        class="input-field"
                                        value="partner"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="form-field">
                        <label for="supplier-password" class="field-label">登入密碼</label>
                        <div class="input-wrapper">
                            <div class="input-container">
                                <input
                                        type="password"
                                        id="supplier-password"
                                        class="input-field"
                                        value="partner"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hidden input for username that combines code + "+" + account -->
            <input type="hidden" id="username" name="username" value="">

            <!-- Hidden input for password -->
            <input type="hidden" id="password" name="password" value="">

            <a href="/forgot-password.html" class="forgot-password">忘記密碼</a>
            <button type="submit" id="submit" class="login-button">登入</button>
        </div>
    </form>
</main>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        let activeTab = 'employee';

        tabButtons.forEach(button => {
            button.addEventListener('click', function () {
                const tab = this.getAttribute('data-tab');

                // Update active tab
                tabButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Show active tab content
                tabContents.forEach(content => content.classList.remove('active'));
                document.getElementById(tab + '-tab').classList.add('active');

                // Update active tab variable
                activeTab = tab;
            });
        });

        // Form submission
        const form = document.querySelector('.login-form');
        form.addEventListener('submit', function (event) {
            if (activeTab === 'employee') {
                const account = document.getElementById('employee-account').value;
                const password = document.getElementById('employee-password').value;

                document.getElementById('username').value = '10' + account;
                document.getElementById('password').value = password;
            } else {
                const supplierCode = document.getElementById('supplier-code').value;
                const account = document.getElementById('supplier-account').value;
                const password = document.getElementById('supplier-password').value;

                document.getElementById('username').value = '20' + supplierCode + '+' + account;
                document.getElementById('password').value = password;
            }
        });
    });
</script>
<div id="notification" class="notification" style="display: none;">
    <div class="notification-content">
        <span id="notification-message"></span>
        <button id="close-notification" class="close-button">&times;</button>
    </div>
</div>

<style>
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #f44336;
        color: white;
        padding: 15px;
        border-radius: 4px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.5s;
    }

    .notification-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .close-button {
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        margin-left: 15px;
    }

    @keyframes slideIn {
        from {transform: translateX(100%); opacity: 0;}
        to {transform: translateX(0); opacity: 1;}
    }
</style>

<script>
    // Check for error parameters in URL
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    function showNotification(message) {
        const notification = document.getElementById('notification');
        const notificationMessage = document.getElementById('notification-message');

        notificationMessage.textContent = message;
        notification.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }

    // Close notification when clicking the close button
    document.getElementById('close-notification').addEventListener('click', function() {
        document.getElementById('notification').style.display = 'none';
    });

    // Check for error parameter when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const error = getUrlParameter('error');
        if (error) {
            let errorMessage = '登入失敗';

            // Map error codes to user-friendly messages
            if (error === 'invalid_credentials') {
                errorMessage = '帳號或密碼錯誤，請重新輸入';
            } else if (error === 'account_disabled') {
                errorMessage = '帳號已被停用，請聯絡管理員';
            } else if (error === 'account_locked') {
                errorMessage = '帳號已被鎖定，請聯絡管理員';
            }

            showNotification(errorMessage);
        }
    });
</script>
</body>
</html>
