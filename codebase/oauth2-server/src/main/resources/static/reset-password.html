<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重設密碼 - 創宇整合平台</title>
    <link rel="stylesheet" href="login/styles.css">
    <style>
        .back-to-login {
            color: rgba(46, 131, 196, 1);
            font-size: 13px;
            font-weight: 700;
            line-height: 24px;
            margin-top: 16px;
            display: block;
            text-decoration: none;
            text-align: center;
        }

        .success-message {
            display: none;
            background-color: #4CAF50;
            color: white;
            padding: 16px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .error-message {
            display: none;
            background-color: #f44336;
            color: white;
            padding: 16px;
            border-radius: 4px;
            margin-top: 20px;
            text-align: center;
        }

        .password-requirements {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }
    </style>
</head>
<body>
<main class="login-page">
    <form id="reset-password-form" class="login-form" aria-labelledby="reset-password-title">
        <div class="form-content">
            <header class="form-header">
                <h1 class="platform-title">創宇整合平台</h1>
            </header>

            <h2 class="login-title" id="reset-password-title">重設密碼</h2>

            <div class="input-group">
                <div class="form-field">
                    <label for="new-password" class="field-label">新密碼</label>
                    <div class="input-wrapper">
                        <div class="input-container">
                            <input type="password" 
                                   id="new-password" 
                                   class="input-field" 
                                   placeholder="請輸入新密碼" 
                                   required/>
                        </div>
                    </div>
                    <p class="password-requirements">密碼不能與最近三次使用的密碼相同</p>
                </div>

                <div class="form-field">
                    <label for="confirm-password" class="field-label">確認密碼</label>
                    <div class="input-wrapper">
                        <div class="input-container">
                            <input type="password" 
                                   id="confirm-password" 
                                   class="input-field" 
                                   placeholder="請再次輸入新密碼" 
                                   required/>
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" id="submit" class="login-button">重設密碼</button>

            <div id="success-message" class="success-message">
                密碼已成功重設，請使用新密碼登入
            </div>

            <div id="error-message" class="error-message">
                重設密碼時發生錯誤
            </div>

            <a href="/login/signin.html" class="back-to-login">返回登入頁面</a>
        </div>
    </form>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('reset-password-form');
        const successMessage = document.getElementById('success-message');
        const errorMessage = document.getElementById('error-message');

        // 從URL獲取token
        const urlParams = new URLSearchParams(window.location.search);
        const token = urlParams.get('token');

        // 如果沒有token，顯示錯誤
        if (!token) {
            errorMessage.textContent = '無效的重設密碼連結';
            errorMessage.style.display = 'block';
            form.style.display = 'none';
            return;
        }

        form.addEventListener('submit', function(event) {
            event.preventDefault();

            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // 隱藏之前的訊息
            successMessage.style.display = 'none';
            errorMessage.style.display = 'none';

            // 檢查密碼是否匹配
            if (newPassword !== confirmPassword) {
                errorMessage.textContent = '兩次輸入的密碼不一致';
                errorMessage.style.display = 'block';
                return;
            }

            // 發送請求到重設密碼API
            fetch('/api/auth/password/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    token: token,
                    newPassword: newPassword
                })
            })
            .then(response => {
                if (response.ok) {
                    // 顯示成功訊息
                    successMessage.style.display = 'block';
                    // 清空表單
                    form.reset();
                    // 3秒後跳轉到登入頁面
                    setTimeout(function() {
                        window.location.href = '/login/signin.html';
                    }, 3000);
                } else {
                    return response.json().then(data => {
                        // 顯示具體錯誤訊息
                        if (data && data.detail) {
                            errorMessage.textContent = data.detail;
                        } else if (data && data.message) {
                            errorMessage.textContent = data.message;
                        } else {
                            errorMessage.textContent = '重設密碼失敗，可能是令牌已過期或密碼不符合要求';
                        }
                        errorMessage.style.display = 'block';
                    }).catch(() => {
                        errorMessage.textContent = '重設密碼失敗，可能是令牌已過期或密碼不符合要求';
                        errorMessage.style.display = 'block';
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorMessage.textContent = '發生網絡錯誤，請稍後再試';
                errorMessage.style.display = 'block';
            });
        });
    });
</script>
</body>
</html>
