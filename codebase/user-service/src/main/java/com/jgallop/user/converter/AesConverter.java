package com.jgallop.user.converter;

import com.jgallop.common.service.EncryptHelper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.springframework.stereotype.Component;

@Component
@Converter
public class AesConverter implements AttributeConverter<String, String> {

    private final EncryptHelper encryptHelper;

    public AesConverter(EncryptHelper encryptHelper) {
        this.encryptHelper = encryptHelper;
    }

    /**
     * 物件屬性儲存到資料庫前，先加密資料後再儲存
     */
    @Override
    public String convertToDatabaseColumn(String attribute) {
        try {
            return encryptHelper.encryptDataWithDefaultAES(attribute);
        } catch (Exception e) {
            throw new RuntimeException("Failed to encrypt data with AES!", e);
        }
    }

    /**
     * 從資料庫取出後，先解密在設定到物件屬性
     */
    @Override
    public String convertToEntityAttribute(String dbData) {
        try {
            return encryptHelper.decryptDataWithDefaultAES(dbData);
        } catch (Exception e) {
            throw new RuntimeException("Failed to decrypt data with AES", e);
        }
    }
}
