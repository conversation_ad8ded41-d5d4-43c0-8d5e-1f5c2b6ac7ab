package com.jgallop.user.converter;

import com.jgallop.user.entity.SigninType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.jgallop.user.entity.SigninType.UNKNOWN;

/**
 * <code>SigninTypeConverter</code> 轉換資料庫中的字串與物件類型 {@link SigninType}
 */
@Converter
public class SigninTypeConverter implements AttributeConverter<SigninType, String> {

    @Override
    public String convertToDatabaseColumn(SigninType signinType) {
        return Objects.nonNull(signinType) ? signinType.name() : UNKNOWN.name();
    }

    @Override
    public SigninType convertToEntityAttribute(String dbData) {
        return StringUtils.isBlank(dbData) ? UNKNOWN : SigninType.fromName(dbData);
    }
}
