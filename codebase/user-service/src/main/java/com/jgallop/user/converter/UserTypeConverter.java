package com.jgallop.user.converter;

import com.jgallop.user.entity.UserType;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import static com.jgallop.user.entity.UserType.UNKNOWN;

/**
 * <code>UserTypeConverter</code> 轉換資料庫中的字串與物件類型 {@link UserType}
 */
@Converter
public class UserTypeConverter implements AttributeConverter<UserType, String> {

    @Override
    public String convertToDatabaseColumn(UserType userType) {
        return Objects.nonNull(userType) ? userType.name() : UNKNOWN.name();
    }

    @Override
    public UserType convertToEntityAttribute(String dbData) {
        return StringUtils.isBlank(dbData) ? UNKNOWN : UserType.fromName(dbData);
    }
}
