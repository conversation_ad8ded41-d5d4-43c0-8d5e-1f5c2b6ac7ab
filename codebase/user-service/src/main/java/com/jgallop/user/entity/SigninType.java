package com.jgallop.user.entity;

import java.util.Arrays;

/**
 * 第三方驗證支援類型
 */
public enum SigninType {

    /**
     * OAuth 2.0 service provided by Google
     */
    GOOGLE,

    /**
     * OAuth 2.0 service provided by Facebook
     */
    FACEBOOK,

    /**
     * OAuth 2.0 service provided by Line
     */
    LINE,

    /**
     * OAuth 2.0 service provided by Apple (Sign in with Apple)
     */
    APPLE,

    /**
     * OAuth 2.0 service provided by oauth2-server
     */
    DEFAULT,

    /**
     * Windows Active Directory
     */
    AD,

    /**
     * LDAP server
     */
    LDAP,

    /**
     * Azure Active Directory
     */
    AAD,

    UNKNOWN;

    public static SigninType fromName(String typeName) {
        return Arrays.stream(values())
                .filter(type -> type.name().equalsIgnoreCase(typeName))
                .findFirst()
                .orElse(UNKNOWN);
    }
}
