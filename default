##
# You should look at the following URL's in order to grasp a solid understanding
# of Nginx configuration files in order to fully unleash the power of Nginx.
# https://www.nginx.com/resources/wiki/start/
# https://www.nginx.com/resources/wiki/start/topics/tutorials/config_pitfalls/
# https://wiki.debian.org/Nginx/DirectoryStructure
#
# In most cases, administrators will remove this file from sites-enabled/ and
# leave it as reference inside of sites-available where it will continue to be
# updated by the nginx packaging team.
#
# This file will automatically load configuration files provided by other
# applications, such as Drupal or Wordpress. These applications will be made
# available underneath a path with that package name, such as /drupal8.
#
# Please see /usr/share/doc/nginx-doc/examples/ for more detailed examples.
##

# Default server configuration
#
client_max_body_size 20m; # 限制上傳大小為 20MB
include mime.types;

# To increase the max URL length since SonarQube requests can have URLs longer than 2048
# The max size of one header set to 16k
# The max number of headers set to 4
large_client_header_buffers 4 16k;

upstream ferrite_server {
        server 127.0.0.1:8181;
        keepalive 256;
}

upstream nexusserver {
        server 127.0.0.1:8568;
        keepalive 256;
}

upstream vlandserver {
	server 127.0.0.1:8888;
	keepalive 256;
}

upstream jgbaseserver {
        server 127.0.0.1:8168;
        keepalive 256;
}

upstream jgbase-backend {
	server 127.0.0.1:8083;
	keepalive 256;
}

# ======= 創宇官網 =======
upstream smartmobile-sit {
        server 127.0.0.1:9901;
        keepalive 256;
}

upstream smartmobile-admin-sit {
        server 127.0.0.1:9902;
        keepalive 256;
}

upstream smartmobile-backend {
        server 127.0.0.1:8090;
        keepalive 256;
}

# ======= 中台禪寺 =======
upstream ctcm-backend {
	server 127.0.0.1:8787;
        keepalive 256;
}

upstream ctcm-vms-sit {
        server 127.0.0.1:8788;
        keepalive 256;
}

# ======= 東方不敗 =======
upstream eastking-backend {
        server 127.0.0.1:8084;
        keepalive 256;
}

upstream eastking-sit {
        server 127.0.0.1:9907;
        keepalive 256;
}

upstream eastking-uat {
        server 127.0.0.1:9906;
        keepalive 256;
}

upstream eastking-test {
        server 127.0.0.1:9700;
        keepalive 256;
}

# ======= 創宇（經銷商回收網站 & 企業管理平台） =======
upstream smcrm-auth {
        server 127.0.0.1:9000;
        keepalive 256;
}

upstream smcrm-backend {
        server 127.0.0.1:8085;
        keepalive 256;
}

upstream smcrm-backend-uat {
        server 127.0.0.1:8091;
        keepalive 256;
}

upstream smcrm-sit {
        server 127.0.0.1:8086;
        keepalive 256;
}

upstream smcrm-uat {
        server 127.0.0.1:8089;
        keepalive 256;
}

upstream dealer-reclaim-sit {
        server 127.0.0.1:8087;
        keepalive 256;
}

upstream dealer-reclaim-uat {
        server 127.0.0.1:8088;
        keepalive 256;
}

upstream smcrm-hfs {
        server 127.0.0.1:24031;
        keepalive 256;
}

upstream smcrm-hfs-uat {
        server 127.0.0.1:24032;
        keepalive 256;
}

upstream smcrm-blancco {
        server 127.0.0.1:3333;
        keepalive 256;
}

# Required for Jenkins websocket agents
map $http_upgrade $connection_upgrade {
  default upgrade;
  '' close;
}

server {
	listen 80 default_server;
#	listen 443 ssl default_server;
	
#	server_name jgdev2.jgallop.com;

        #ssl on;
#        ssl_certificate /etc/letsencrypt/live/jgdev2.jgallop.com/fullchain.pem;
#        ssl_certificate_key /etc/letsencrypt/live/jgdev2.jgallop.com/privkey.pem;

#        ssl_session_timeout 5m;
#        ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
#        ssl_protocols TLSv1.2 TLSv1.3;

        # HSTS (ngx_http_headers_module is required) (63072000 seconds)
#        add_header Strict-Transport-Security max-age=31536000;

        # OCSP stapling
#        ssl_stapling on;
#        ssl_stapling_verify on;

#        add_header X-Frame-Options "SAMEORIGIN";

        client_max_body_size 500M;
        client_header_buffer_size 512k;
        large_client_header_buffers 4 512k;	

	# SSL configuration
	#
	# listen 443 ssl default_server;
	# listen [::]:443 ssl default_server;
	#
	# Note: You should disable gzip for SSL traffic.
	# See: https://bugs.debian.org/773332
	#
	# Read up on ssl_ciphers to ensure a secure configuration.
	# See: https://bugs.debian.org/765782
	#
	# Self signed certs generated by the ssl-cert package
	# Don't use them in a production server!
	#
	# include snippets/snakeoil.conf;

	root /var/www/html;
	
	# Add index.php to the list if you are using PHP
	index index.html index.htm index.nginx-debian.html;

	# server_name _;
	# server_name jgdev2.jgallop.com;

	#location / {
		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		# try_files $uri $uri/ =404;
		
                # 嘗試按照 $uri 變量指定的路徑找到對應的檔案。如果該檔案不存在，則返回 /index.html。
                # 這樣做的目的是當應用嘗試訪問路由時，而這些路由實際上並不存在於服務器上（因為它們是前端路由），
                # Nginx 會回退到 index.html，讓前端框架處理路由。
	#	try_files $uri /index.html;
	#}

	# 創宇 OAuth Server
        location / {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-auth/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	location /jg-ftp {
		alias /var/ftp/feib/img/;
	}

        location /jg-static {
                alias /var/www/html/;
        }
	
        location /jglab {
                try_files $uri $uri/ =404;
        }

        # ferrite listen to 127.0.0.1:8181/ferritedev
        location /ferritedev/ {
                proxy_pass http://ferrite_server/ferrite/;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        	proxy_send_timeout 300s;
		send_timeout 300s;
		keepalive_timeout 120s;
	}

	location /feib-mkt {
		alias /var/www/html/feib-mkt;
	}


	location ~ ^/feib-mkt/banner {
                try_files $uri $uri/ /feib-mkt/banner.html last;
        }

        location ~ ^/feib-mkt/redirect {
                try_files $uri $uri/ /feib-mkt/redirect.html last;
        }

	location /feib-mbr {
		try_files $uri $uri/ /feib-mbr/index.html last;
	}


	# vland sit frontend
	location /vland {
	        try_files $uri $uri/ /vland/index.html last;
	}

	# jgbase frontend
	location /jgbase {
                try_files $uri $uri/ /jgbase/index.html last;
        }

	# vland sit backend
        location /vland-dev/ {
                proxy_http_version 1.1;
                proxy_pass http://vlandserver/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # jgbase backend
        location /jgbase-backend/ {
                proxy_http_version 1.1;
                proxy_pass http://jgbase-backend/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 創宇官網前台- 前端 SIT
	location /smartmobile-sit {
                proxy_http_version 1.1;
                proxy_pass http://smartmobile-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 創宇官網前台- 前端靜態資料 SIT
        location /smartmobile-sit/_next/static {
                proxy_http_version 1.1;
                proxy_pass http://smartmobile-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                expires 30d;
        }

	# 創宇官網後台- 前端 SIT
	location /smartmobile-admin-sit {
                proxy_http_version 1.1;
                proxy_pass http://smartmobile-admin-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
    		proxy_set_header Host $host;
    		proxy_set_header X-Forwarded-Host $host;
    		proxy_set_header X-Real-IP $remote_addr;
    		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 創宇官網後台- 前端靜態資料 SIT 
        location /smartmobile-admin-sit/_next/static {
                proxy_http_version 1.1;
                proxy_pass http://smartmobile-admin-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                expires 30d;
        }
	
	# 創宇官網- 後端 SIT
        location /smartmobile-backend/ {
                proxy_http_version 1.1;
                proxy_pass http://smartmobile-backend/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 中台禪寺- 前端 SIT
        location /ctcm-vms-sit {
                proxy_http_version 1.1;
                proxy_pass http://ctcm-vms-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 中台禪寺- 後端 SIT
	location /ctcm-backend/ {
                proxy_http_version 1.1;
                proxy_pass http://ctcm-backend/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }


        # 東方不敗- 前端 UAT
        location /eastking-uat {
                proxy_http_version 1.1;
                proxy_pass http://eastking-uat;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	# 東方不敗- 前端 SIT
	location /eastking-sit {
                proxy_http_version 1.1;
                proxy_pass http://eastking-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	}

        # 東方不敗- 後端 SIT
        location /eastking-backend/ {
                proxy_http_version 1.1;
                proxy_pass http://eastking-backend/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

	location /eastking-test/ {
    		proxy_http_version 1.1;
    		proxy_set_header Host $host;
    		proxy_set_header X-Real-IP $remote_addr;
    		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    		proxy_set_header X-Forwarded-Proto $scheme;

    		proxy_read_timeout 600;
    		proxy_connect_timeout 600;
		proxy_send_timeout 600;

	   	# 結尾斜線很關鍵：/eastking-test/xxx -> /xxx
    		proxy_pass http://eastking-test/;
	}

	# API：前端打 /api/...，後端也要看到 /api/...
	location ^~ /api/ {
    		# 注意：這裡「沒有」結尾斜線 → URI 不被改寫，/api 保留
    		proxy_pass http://eastking-test;

    		proxy_http_version 1.1;
    		proxy_connect_timeout 600;
    		proxy_send_timeout 600;
    		proxy_read_timeout 600;

    		proxy_set_header Host $host;
    		proxy_set_header X-Real-IP $remote_addr;
    		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    		proxy_set_header X-Forwarded-Proto $scheme;

    		# 若後端回 302 之類絕對網址到本機，改寫成對外域名，並保留 /api 前綴
    		proxy_redirect ~^http://[^/]+(/api/.*)$ $scheme://$host$1;
	}	
	
	location = /eastking-test { return 301 /eastking-test/; }

        # 創宇- 後端 SIT
        location /smcrm-backend/ {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-backend/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 後端 UAT
        location /smcrm-backend-uat/ {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-backend-uat/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 後端 HFS SIT
        location /smcrm-hfs/ {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-hfs/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
		proxy_set_header X-Forwarded-Host $host;
        }

        # 創宇- 後端 HFS SIT
        location /smcrm-hfs-uat/ {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-hfs-uat/;
                proxy_connect_timeout 600;
                proxy_send_timeout 600;
                proxy_read_timeout 600;
                send_timeout 600;
                proxy_set_header Host $host;
                proxy_set_header Origin "*";
                proxy_set_header X-Real-IP $remote_addr;
                #proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Forwarded-Host $host;
        }

        # 創宇- blancco測試
        location /smcrm-blancco/ {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-blancco/;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 企業管理平台- 前端 SIT
        location /smcrm-sit {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 企業管理平台- 前端 UAT
        location /smcrm-uat {
                proxy_http_version 1.1;
                proxy_pass http://smcrm-uat;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 經銷商回收網站- 前端 SIT
        location /dealer-reclaim-sit {
                proxy_http_version 1.1;
                proxy_pass http://dealer-reclaim-sit;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 創宇- 經銷商回收網站- 前端 UAT
        location /dealer-reclaim-uat {
                proxy_http_version 1.1;
                proxy_pass http://dealer-reclaim-uat;
                proxy_buffer_size 64k;
                proxy_buffers 32 32k;
                proxy_busy_buffers_size 128k;
                proxy_set_header Host $host;
                proxy_set_header X-Forwarded-Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

    	location /hello {
        	default_type text/plain;
        	return 200 'hello world';
    	}

        # For Let's Encrypt
        location ~ /.well-known {
                allow all;
        }
     }
	# pass PHP scripts to FastCGI server
	#
	#location ~ \.php$ {
	#	include snippets/fastcgi-php.conf;
	#
	#	# With php-fpm (or other unix sockets):
	#	fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
	#	# With php-cgi (or other tcp sockets):
	#	fastcgi_pass 127.0.0.1:9000;
	#
	#}

	# deny access to .htaccess files, if Apache's document root
	# concurs with nginx's one
	#
	#location ~ /\.ht {
	#	deny all;
	#}


# bad script below this line  not work
  #  server {
   #     listen 80;
    #    listen [::]:80;
     #   server_name **************;

#      location /jg-static {
       #		allow all;
	#	alias /var/ftp;
	#	add_header Cache-control "public";
#	}
#	location /meow {
#		return 200 "Hello user"; 
#	}
 #   	location ^~ /_next/static/ { 
#		alias /home/<USER>/arf-mkt/feib/_next/static/;
#		try_files $uri $uri/ = 404;
#	}
        # test
        # location /swiftui {
        #         alias /var/www/swiftui;
        #        index index.html index.htm;
        #         try_files $uri $uri/ =404;
        # }
#    }
	

# Virtual Host configuration for example.com
#
# You can move that to a different file under sites-available/ and symlink that
# to sites-enabled/ to enable it.
#
