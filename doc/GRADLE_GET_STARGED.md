# Gradle 常用指令

## 初始化專案

```shell

./gradlew init

```


## 執行專案

```shell

./gradlew run

```

## 打包專案

```shell

./gradlew build

```

## 清理快取資料
清理專案的快取與建置目錄。

``` shell

./gradlew clean

```

## 清除專案再重新打包

```shell

./gradlew clean build

```

## 查看專案結構

```shell

./gradlew projects

```

## 查看專案依賴

```shell

./gradlew dependencies

```

## 查看專案版本

```shell

./gradlew -v

```

## 查看專案設定

```shell

./gradlew properties

```

## 查看專案設定檔

```shell

./gradlew settings

```

## Gradle 設定檔被修改後需要重新載入

```shell

./gradlew --refresh-dependencies

```

## 啟用串列模式顯示執行詳情
顯示詳細的執行內容，通常用於排查問題或記錄日誌。

``` shell

./gradlew --console=plain

```
## 僅執行特定任務 (避免依賴執行)
指定執行某一個模組內的任務。

``` shell

./gradlew :moduleName:taskName

```
## 顯示更多除錯資訊
啟用除錯模式，會顯示更詳細的執行日誌，幫助診斷問題。

``` shell

./gradlew build --debug


```

## 啟用平行構建模式
利用多執行緒提高構建速度。

``` shell

./gradlew build --parallel

```
## 禁用守護進程
臨時禁用守護進程，避免背景執行問題。

``` shell

./gradlew build --no-daemon

```
## 啟用離線模式
強制使用本地緩存的依賴庫資料（適用於斷網或節省下載）。

``` shell

./gradlew build --offline

```

## 執行單元測試
執行專案中的單元測試。

``` shell

./gradlew test

```

## 生成測試報告
執行測試並生成詳細的報告（測試結果等）。

``` shell

./gradlew testReport

```
## 進行靜態程式碼掃描
檢查代碼的質量，包括測試、依賴檢查和其他的驗證（如註解、格式檢查）。

``` shell

./gradlew check

```

## 生成 Javadoc 文件
自動生成 Java 註解文件。

``` shell

./gradlew javadoc

```
## 生成 Jar 文件
打包專案成標準的 Jar 文件格式。

``` shell

./gradlew jar

```

## 解決版本衝突
查詢特定配置中的依賴樹，幫助發現並解決版本衝突。

``` shell

./gradlew dependencies --configuration compileClasspath

```
