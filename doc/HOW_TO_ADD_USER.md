# 說明如何新增帳號

新增帳號分為兩個 API：

1. 新增使用者帳號
2. 設定使用者密碼

## 新增使用者帳號

前提：

### 前端呼叫系統 API `使用者登入`，詳見 [使用者登入](HOW_TO_LOGIN.md)，取得回傳值中的 access token

步驟如下：

### 步驟 1. 新增使用者帳號：

```http request
  
 HTTP POST 
 /api/user/create
  
```

輸入的 reques body 如下，為 JSON 格式，其中：
* `username` 是使用者登入帳號
* `displayName` 是顯示在畫面用的使用者名稱
* `userType` 使用者類型，用來決定關聯到此使用者的 table。例如，使用者類型為 `EMPLOYEE`，表示此帳號是員工登入用，預設有效值為 `EMPLOYEE`, `SUPPLIER`, `CUSTOMER`, `VISITOR`
* `roleIds` 使用者所屬角色編號，可以零到多個。若不知道可以不提供

```json
  {
    "username": "mason",
    "displayName": "<PERSON> Hsieh",
    "userType": "EMPLOYEE", 
    "roleIds": [
      "c8be9b5b-341f-4466-bee1-083d2245aea8"
    ]
  }  

```

取得回傳值範例：

```json
    
  {
    "userId": "2cf53c8c-a6b7-4a92-abbf-457a518a30b8",
    "username": "mason",
    "displayName": "Mason Hsieh",
    "userType": "EMPLOYEE",
    "enabled": true,
    "createdAt": *************,
    "createdBy": "d9d3d4e2-ec90-4cf0-b984-c131d901e38e",
    "modifiedAt": null,
    "modifiedBy": null,
    "roleNames": [
      "ACCOUNT_EDITOR"
    ],
    "permissionNames": [
      "ACCOUNT_MODIFY",
      "ACCOUNT_READ",
      "ACCOUNT_DELETE",
      "ACCOUNT_CREATE"
    ]
  }
    
```

## 步驟 2. 設定使用者密碼`

步驟如下：

### 1. 呼叫系統 API `取得指定使用者登入帳號的公鑰`

此步驟同 [使用者登入](HOW_TO_LOGIN.md) 中的 `登入步驟 1`

### 2. 隨機產生一組 AES 密鑰

此步驟同 [使用者登入](HOW_TO_LOGIN.md) 中的 `登入步驟 2`

### 3. 加密密碼

此步驟同 [使用者登入](HOW_TO_LOGIN.md) 中的 `登入步驟 3`

### 4. 加密 AES 密鑰

此步驟同 [使用者登入](HOW_TO_LOGIN.md) 中的 `登入步驟 4`

### 5. 前端呼叫系統 API `建立使用者密碼`：

```http request
  
 HTTP POST 
 /api/user/password/create
  
```

此服務僅適用於使用者密碼尚未建立時，若使用者密碼已經建立，應呼叫 `/api/user/password/update` 變更使用者密碼
輸入的 reques body 如下，為 JSON 格式，其中：
* `username` 是使用者登入帳號
* `password` 是加密後的密碼，即步驟 3 的回傳值
* `nonce` 是步驟 1 取得的一次性標誌(nonce)，同時作為步驟 3 加密密碼時需要的 initialization vector (IV)
* `encryption` 是加密後的 AES 密鑰，即步驟 4 的回傳值

```json
  {
    "username": "mason",
    "password": "",
    "nonce": "1J65SvUBMsRfymGWaFGKjw==",
    "encryption": "WOWVh6LyjaNViMHF+MbiZTMCd3CX5CBlnJllz9CWxPsrzVVe7jdxA3wodXozPRbXz7v3SeD43WCl1fDCEb3KM/oXofQmIuejvJKKQAETEjo3+vHpARRILB/QbIL43hl2juStYloKBDvHwni9hqwVDrWbKBG1aKCBWoVnpa89fD+xTUbh/s7uaP22NmZw3uLIKf5xS0vvhkvHuJlVol4wodwrxobLQ/taS8bLMPC0BS9eA0hQ02T48pHCul9sAzxa+llNEWtQk8myFySzRMw/jl6I+4wFzcMJ6nIapEBlOBnzwsXE2Lv09CjbjwpebKiwVcdBpQD/eFsLFvTO2nfLSQ=="
  }  

```

取得回傳值範例：

回傳值與 `步驟 1. 新增使用者帳號` 的回傳值相同，但欄位 `modifiedAt` 與 `modifiedBy` 有值

```json
    
  {
    "userId": "2cf53c8c-a6b7-4a92-abbf-457a518a30b8",
    "username": "mason",
    "displayName": "Mason Hsieh",
    "userType": "EMPLOYEE",
    "enabled": true,
    "createdAt": *************,
    "createdBy": "d9d3d4e2-ec90-4cf0-b984-c131d901e38e",
    "modifiedAt": *************,
    "modifiedBy": "d9d3d4e2-ec90-4cf0-b984-c131d901e38e",
    "roleNames": [
      "ACCOUNT_EDITOR"
    ],
    "permissionNames": [
      "ACCOUNT_MODIFY",
      "ACCOUNT_READ",
      "ACCOUNT_DELETE",
      "ACCOUNT_CREATE"
    ]
  }
    
``` 


