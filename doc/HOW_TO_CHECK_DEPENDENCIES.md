# 第三方函式庫弱點掃描

**下面說明假設專案根目錄為 `${CODEBASE}`**

## 第三方函式庫弱點掃描是什麼

第三方函式庫弱點掃描的目的是透過自動化工具來檢測應用程式所依賴的第三方函式庫中可能存在的安全弱點。
這些弱點可能會被惡意攻擊者利用，進而危害應用程式的安全性和完整性。

使用 [OWASP Dependency-check](https://jeremylong.github.io/DependencyCheck/index.html) 的主要目的如下：

1. **識別已知弱點**：
   Dependency-check 會檢查應用程式所依賴的第三方函式庫，並將其與已知的弱點資料庫（如 NVD, National Vulnerability Database）進行比對，找出已知的安全弱點。

2. **減少安全風險**：
   通過及早發現和修補弱點，可以有效減少應用程式在運行過程中面臨的安全風險，避免被攻擊者利用這些弱點進行攻擊。

3. **遵從法規和標準**：
   許多法規和安全標準要求企業定期進行弱點掃描，以確保所使用的軟體和第三方函式庫的安全性。使用 Dependency-check 可以幫助企業滿足這些法規和標準的要求。

4. **提高開發效率**：
   自動化的弱點掃描工具可以大幅減少手動檢查的工作量，使開發人員能更專注於應用程式的功能開發，同時確保程式碼的安全性。

5. **提升信任度**：
   對於使用第三方函式庫的應用程式，能夠證明其安全性和穩定性，提升用戶和客戶對於應用程式的信任度。

OWASP Dependency-check 提供了一個簡單易用的解決方案，使開發團隊能夠輕鬆地整合到持續整合（CI）流程中，自動化地進行弱點檢測，確保應用程式的安全性。

## 設定本機第三方函式庫弱點掃描

在 ${CODEBASE}/build.gradle 中加入 dependency check library，
其中 configuration 中的設定請參考下列文件：

- <https://jeremylong.github.io/DependencyCheck/dependency-check-gradle/index.html>
- <https://github.com/dependency-check/dependency-check-gradle>
- <https://jeremylong.github.io/DependencyCheck/dependency-check-gradle/configuration.html>  

```groovy

plugins {
    id 'org.owasp.dependencycheck' version '12.0.2'
}

// 在這裡處理想在所有子專案都啟用的設定
subprojects {
    apply plugin: 'org.owasp.dependencycheck'
}

// 當執行 gradle check 命令時，系統會自動執行第三方函式庫弱點掃描並產出分析報告
check.dependsOn dependencyCheckAnalyze

dependencyCheck {
   autoUpdate=false
   
   format='ALL'

   // 使用 NVD API Key 以加速更新
   nvd.apiKey="d97f2dc9-400a-4320-9786-4ca3270f02ec"

   // 當 CVSS 分數大於或等於 8 時，使建置失敗
   failBuildOnCVSS=8.0

   // 指定 Suppression File
   suppressionFiles=["dependency-check-suppression.xml"]
}

```

## 第三方函式庫弱點掃描步驟

### Step 1. 執行掃描

第一次執行需要載入弱點資料庫，約需要 10 分鐘，之後執行則不需要載入。

```shell

cd ${CODEBASE}
./gradlew check --info

```

若要針對指定模組執行掃描，則執行 `./gradlew <module>:check`，例如：

```shell

./gradlew common-service:check --info

```

### Step 2. 檢查掃描報告

掃描完成後，掃描結果位於各個模組目錄下的資料夾 `reports` 檔案 'dependency-check-report.html'，
使用瀏覽器開啟即可查看掃描報告如下圖：

![檢視報告與處理方式](dependency-check-report.jpg)

#### 2-1. 取得函式庫版本資訊與弱點

在掃描報告中，點選 `2-1.取得函式庫版本資訊與弱點` 連結以開啟如下資訊，可取得函式庫版本與弱點說明

![函式庫版本與弱點說明](dependency-check-search-results.jpg)

### Step 3. 升級第三方函式庫

#### 3-1. 從 maven repository 找出可升級的版本

依據函式庫版本，從 maven repository 找到函式庫版本的訊息，包含弱點資訊，
確認此弱點資訊 `CVE-XXXX-XXXX` 就是上 `2-1 取得函式庫版本資訊與弱點` 中的弱點。

![Maven repo函式庫說明](dependency-check-mvn-repo.jpg)

回到 maven repository 網站上一層，如下圖，找出合適的新版本。
通常是找同一個次版號的最新版即可，但若有其他考量，例如此第三方函式庫有更新的主版號版本被其他函式庫用到，
則改用此更新的主版號版本中沒有弱點的版本。

![找出合適的新版本](dependency-check-update-version.jpg)

#### 3-2. 加入新版本 dependency

加入新版本 dependency 到 ${CODEBASE}/build.gradle，或者各模組的 build.gradle 檔案中的 dependency 區塊。

### Step 4. 加入排除名單

#### 4-1. 取得 suppress XML 資訊

如下圖，點選已更新版本的 identifiers 旁的按鍵 `suppress`，
取得 xml 資訊後貼到檔案 ${CODEBASE}/dependency-check-suppression.xml

![取得suppress資訊](dependency-check-suppress.jpg)

```xml

<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
   <suppress>
      <notes><![CDATA[
      file name: logback-core-db-1.2.11.1.jar
      ]]></notes>
      <packageUrl regex="true">^pkg:maven/ch\.qos\.logback\.db/logback-core-db@.*$</packageUrl>
      <cpe>cpe:/a:qos:logback</cpe>
   </suppress>
   
    <!-- 之後其他的 suppress 加在這裡 -->
</suppressions>

```

### Step 5. 重新執行掃描

```shell

cd ${CODEBASE}
./gradlew check --info

```
