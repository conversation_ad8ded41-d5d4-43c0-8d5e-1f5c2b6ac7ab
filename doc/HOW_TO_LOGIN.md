# 說明如何登入本系統

## 前情提要

* 專案預設管理者的登入帳號為 `admin`，密碼為 `admin`。管理者預設具有帳號資訊（包含使用者、角色與權限）的讀寫權限
* 專案預設參觀者的登入帳號為 `passerby`，密碼為 `passerby`。參觀者預設具有帳號資訊（包含使用者、角色與權限）的唯獨權限
* 本文件以管理者 `admin` 說明如何登入。
* 文件包含 Java 與 Typescript 的範例程式：
  * Java：會呼叫現行提供的 API，若有需要可以自行查詢原始碼
  * Typescript：以範例程式說明。因為我不是 Typescript 專家，若有錯誤請自行調整。

## 登入步驟大綱

1. 前端呼叫系統 API `取得指定使用者登入帳號的公鑰`，取得指定登入帳號的公鑰(public key)與一次性標誌(nonce)，這兩個值都是 Base64 編碼後的值
2. 前端隨機產生一組 AES 密鑰
3. 前端使用步驟 2 產生的 AES 密鑰，以及步驟 1 取得的一次性標誌(nonce)作為 IV 值，加密使用者輸入的密碼，完成後使用 Base64 編碼後待用
4. 前端使用步驟 1 取得的公鑰(public key)，加密步驟 2 產生的 AES 密鑰，完成後使用 Base64 編碼後待用
5. 前端呼叫系統 API `使用者登入`，提供 `使用者帳號`、`步驟 3 的待用值`、`步驟 1 的一次性標誌(nonce)`、以及 `步驟 4 的待用值` 這四個資料作為 request body，取得登入資訊。 登入資訊包含使用者編號、顯示名稱、所屬角色、具有的權限、access token、access token 逾期時間、以及 refresh token。
6. (承上) access token 值作為後續呼叫系統 API 時，作為 Header `Authorization` 的值，前綴字串為 `Bearer `；而 refresh token 則在 access token 逾期前，呼叫系統 API `更新 access token`，避免每次 access token 逾期後，前端使用者都要進入登入畫面重新登入。

## 登入步驟細節

### 登入步驟 1

* 前端呼叫系統 API `取得指定使用者登入帳號的公鑰`，取得指定登入帳號的公鑰(public key)與一次性標誌(nonce)，這兩個值都是 Base64 編碼後的值
* API `取得指定使用者登入帳號的公鑰` 路徑：
  
  ```http request
  
  HTTP GET 
  /api/public/publickey/{username}
  
  ```
  其中 `{username}` 是指使用者登入帳號，即 `admin`

* 回傳值範例如下，公鑰(public key)與一次性標誌(nonce)，這兩個值都是 Base64 編碼後的值

  ```json
  
  {
    "username": "admin",
    "publicKey": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0J7Oca41QIv22URV826K6jkewFcm4bkI77stP2W/caejnA4plu3+hGsJE07kyP94wcAMaCNsifxZhidNJgq/MX0+L84jobezsHKlT2ahHmtHHaW7Kskoelw4FI7xZQEiEnXJVcd9eI++GYhOQ9cLzRPs5o9XsuNzYiA1Jd+VMVBkL2DdkqBi4ossh9EwA7Oju7sO9x5TSUK8IQuwOA8auAzenw34lXDnCjVvqSOqZI/t2K8cpES/8ye2R34VscOWs23Jb61ioX/mNez8IfvJuCRfkLQXJRN8Hc2DXyobmQ3+KyeOslMRzhbTnCK92cRtZ9j04ZMUrkTsMF3WiuFvtwIDAQAB",
    "nonce": "1J65SvUBMsRfymGWaFGKjw=="
  }
  
  ```

### 登入步驟 2

* 前端隨機產生一組 AES 密鑰
* 產生 AES 密鑰時，algorithm 須為 `AES`，key sieze 為 `128` 
* 若是使用 Postman 測試，可以直接呼叫下列 API 取得一個隨機產生的 AES 密鑰，並經過 Base64編碼的值：

  ```http request
  
  HTTP GET 
  /api/internal/aeskey
  
  ```
  取得回傳值範例：

  ```text
  
  x0VrcCo5DwAT/7t9uV8Mrw==
  
  ```
  
* 若為 Typescript，則可以參考下列程式

  ```typescript
  
  // 要在 TypeScript 中產生 AES 密鑰並將其 Base64 編碼，你可以使用 Node.js 的內建模組 crypto。
  // 以下是一個簡單的程式碼範例，用於產生 AES 密鑰並進行 Base64 編碼
  // 執行下述指令後，控制台會顯示 Base64 編碼的 AES 密鑰：
  
  import crypto from 'crypto';

  function generateAESKeyBase64(): string {
      const key = crypto.randomBytes(16);  // 128 bits 密鑰
      return key.toString('base64');
  } 
  
  const base64Key = generateAESKeyBase64();
  console.log(`產生的 AES 密鑰 (Base64 編碼): ${base64Key}`);

  ``` 

### 登入步驟 3

* 前端使用步驟 2 產生的 AES 密鑰，以及步驟 1 取得的一次性標誌(nonce)作為 IV 值，加密使用者輸入的密碼，完成後使用 Base64 編碼後待用
* 步驟 2 產生的 AES 密鑰，若是 Base64 編碼過的值，要先使用 Base64 解碼
* 步驟 1 取得的一次性標誌(nonce)，是 Base64 編碼過的，要先使用 Base64 解碼
* 加密使用者輸入的密碼時，使用的 transformation 為 `AES/CBC/PKCS5Padding`，加密需要的 initialization vector (IV)就是步驟 1 取得的一次性標誌(nonce)
* 若是使用 Postman 測試，可以直接呼叫下列 API，輸入密碼（此密碼為明碼字串）、IV 值（Base64 編碼過）、與 AES 密鑰（Base64 編碼過），即可取得加密後，再經過 Base64 編碼後的值：

  ```http request
  
  HTTP GET 
  /api/internal/aesencrypt
  ?rawData=admin
  &iv=1J65SvUBMsRfymGWaFGKjw%3D%3D
  &base64AESKey=x0VrcCo5DwAT%2F7t9uV8Mrw%3D%3D
  
  ```
  輸入參數如下，注意這些參數都需要經過 url encode。URL Encode 的方式詳見 [使用 Postman URL Encode 功能](POSTMAN_URL_ENCODE.md)

  | 參數名稱         | 參數值範例(url encode前)       | 參數值範例(url encode後)             |
  |--------------|--------------------------|--------------------------------|
  | rawData      | admin                    | admin                          |
  | iv           | 1J65SvUBMsRfymGWaFGKjw== | 1J65SvUBMsRfymGWaFGKjw%3D%3D   |
  | base64AESKey | x0VrcCo5DwAT/7t9uV8Mrw== | x0VrcCo5DwAT%2F7t9uV8Mrw%3D%3D | 

  取得回傳值範例：

  ```text
  
  VokhiTN9ooLRBbWLLeHEEA==
  
  ```

* 若為 Typescript，則可以參考下列程式

  ```typescript
  
  // 加密方式說明如下：
  // * 使用 Node.js 的 crypto 模組
  // * 根據所給的 AES 密鑰和初始化向量 (IV)，transformation 為 `AES/CBC/PKCS5Padding` 的 AES 加密方式，對指定的字串進行加密，並返回 Base64 編碼的加密後的字串
  // 注意：
  // 1. 請確保提供的 AES 密鑰是 128 位（即 16 位元組）
  // 2. 初始化向量 (IV) 也必須是 16 位元組

  import crypto from 'crypto';
  
  function encrypt(input: string, keyBase64: string, ivBase64: string): string {
      // 從 Base64 解碼密鑰和 IV
      const key = Buffer.from(keyBase64, 'base64');
      const iv = Buffer.from(ivBase64, 'base64');
      
      // 使用指定的 AES 密鑰、IV 和 transformation 進行加密
      const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
      let encrypted = cipher.update(input, 'utf8', 'base64');
      encrypted += cipher.final('base64');
        
      return encrypted;
  }

  const keyBase64 = "YOUR_AES_KEY_IN_BASE64"; // 這裡放置你的 Base64 編碼的 AES 密鑰（必須是 128 位，即 16 位元組）
  const ivBase64 = "YOUR_IV_IN_BASE64";       // 這裡放置你的 Base64 編碼的 IV（必須是 16 位元組）
  
  const plaintext = "這是要加密的字串";
  const encryptedText = encrypt(plaintext, keyBase64, ivBase64);
  
  console.log(`加密後的字串 (Base64): ${encryptedText}`);
    

  ``` 

### 登入步驟 4

* 前端使用步驟 1 取得的公鑰(public key)，加密步驟 2 產生的 AES 密鑰，完成後使用 Base64 編碼後待用
* 步驟 1 取得的公鑰(public key)，是 Base64 編碼過的，要先使用 Base64 解碼。
* 公鑰(public key)須符合 ASN.1 編碼規格、X.509 標準 `X509EncodedKeySpec` 與 `RSA` 加密機制
* 若 AES 密鑰是 Base64 編碼過的，要先使用 Base64 解碼
* AES 密鑰的 transformation 為 `AES/CBC/PKCS5Padding`
* 若是使用 Postman 測試，可以直接呼叫下列 API，輸入AES 密鑰（Base64 編碼過）、與公鑰(public key)（Base64 編碼過），即可取得加密後，再經過 Base64 編碼後的值：

  ```http request
  
  HTTP GET 
  /api/internal/encrypt/aeskey
  ?base64AESKey=x0VrcCo5DwAT%2F7t9uV8Mrw%3D%3D
  &publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0J7Oca41QIv22URV826K6jkewFcm4bkI77stP2W%2FcaejnA4plu3%2BhGsJE07kyP94wcAMaCNsifxZhidNJgq%2FMX0%2BL84jobezsHKlT2ahHmtHHaW7Kskoelw4FI7xZQEiEnXJVcd9eI%2B%2BGYhOQ9cLzRPs5o9XsuNzYiA1Jd%2BVMVBkL2DdkqBi4ossh9EwA7Oju7sO9x5TSUK8IQuwOA8auAzenw34lXDnCjVvqSOqZI%2Ft2K8cpES%2F8ye2R34VscOWs23Jb61ioX%2FmNez8IfvJuCRfkLQXJRN8Hc2DXyobmQ3%2BKyeOslMRzhbTnCK92cRtZ9j04ZMUrkTsMF3WiuFvtwIDAQAB
  
  ```
  輸入參數如下，注意這些參數都需要經過 url encode。URL Encode 的方式詳見 [使用 Postman URL Encode 功能](POSTMAN_URL_ENCODE.md)
  
   | 參數名稱         | 參數值範例(url encode前)       | 參數值範例(url encode後)           |
   |--------------|--------------------------|------------------------------|
   | base64AESKey | x0VrcCo5DwAT/7t9uV8Mrw== | x0VrcCo5DwAT%2F7t9uV8Mrw%3D%3D |
   | publicKey | MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0J7Oca41QIv22URV826K6jkewFcm4bkI77stP2W/caejnA4plu3+hGsJE07kyP94wcAMaCNsifxZhidNJgq/MX0+L84jobezsHKlT2ahHmtHHaW7Kskoelw4FI7xZQEiEnXJVcd9eI++GYhOQ9cLzRPs5o9XsuNzYiA1Jd+VMVBkL2DdkqBi4ossh9EwA7Oju7sO9x5TSUK8IQuwOA8auAzenw34lXDnCjVvqSOqZI/t2K8cpES/8ye2R34VscOWs23Jb61ioX/mNez8IfvJuCRfkLQXJRN8Hc2DXyobmQ3+KyeOslMRzhbTnCK92cRtZ9j04ZMUrkTsMF3WiuFvtwIDAQAB | MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0J7Oca41QIv22URV826K6jkewFcm4bkI77stP2W%2FcaejnA4plu3%2BhGsJE07kyP94wcAMaCNsifxZhidNJgq%2FMX0%2BL84jobezsHKlT2ahHmtHHaW7Kskoelw4FI7xZQEiEnXJVcd9eI%2B%2BGYhOQ9cLzRPs5o9XsuNzYiA1Jd%2BVMVBkL2DdkqBi4ossh9EwA7Oju7sO9x5TSUK8IQuwOA8auAzenw34lXDnCjVvqSOqZI%2Ft2K8cpES%2F8ye2R34VscOWs23Jb61ioX%2FmNez8IfvJuCRfkLQXJRN8Hc2DXyobmQ3%2BKyeOslMRzhbTnCK92cRtZ9j04ZMUrkTsMF3WiuFvtwIDAQAB |

  取得回傳值範例：

  ```text
  
  WOWVh6LyjaNViMHF+MbiZTMCd3CX5CBlnJllz9CWxPsrzVVe7jdxA3wodXozPRbXz7v3SeD43WCl1fDCEb3KM/oXofQmIuejvJKKQAETEjo3+vHpARRILB/QbIL43hl2juStYloKBDvHwni9hqwVDrWbKBG1aKCBWoVnpa89fD+xTUbh/s7uaP22NmZw3uLIKf5xS0vvhkvHuJlVol4wodwrxobLQ/taS8bLMPC0BS9eA0hQ02T48pHCul9sAzxa+llNEWtQk8myFySzRMw/jl6I+4wFzcMJ6nIapEBlOBnzwsXE2Lv09CjbjwpebKiwVcdBpQD/eFsLFvTO2nfLSQ==

  ```

* 若為 Typescript，則可以參考下列程式

  ```typescript
  
  // 使用指定公鑰(public key)，加密指定的 AES 密鑰，再使用 Base64 編碼後回傳，加密機制說明如下：
  // * 指定的公鑰(public key)，是 Base64 編碼過的
  // * 公鑰(public key)須符合 ASN.1 編碼規格、X.509 標準 `X509EncodedKeySpec` 與 `RSA` 加密機制
  // * 指定的 AES 密鑰是 Base64 編碼過的，要先使用 Base64 解碼
  // * AES 密鑰的 transformation 為 `AES/CBC/PKCS5Padding`

  import crypto from 'crypto';

  function encryptWithPublicKey(publicKeyBase64: string, aesKeyBase64: string): string {
      // 從 Base64 解碼公鑰
      const publicKeyBuffer = Buffer.from(publicKeyBase64, 'base64');
      const publicKey = publicKeyBuffer.toString('utf-8');
      
      // 從 Base64 解碼 AES 密鑰
      const aesKeyBuffer = Buffer.from(aesKeyBase64, 'base64');

      // 使用公鑰加密 AES 密鑰
      const encryptedBuffer = crypto.publicEncrypt({
          key: publicKey,
          padding: crypto.constants.RSA_PKCS1_PADDING
      }, aesKeyBuffer);

      // 將加密後的結果轉為 Base64 編碼
      return encryptedBuffer.toString('base64');
  }

  const publicKeyBase64 = "YOUR_PUBLIC_KEY_IN_BASE64"; // 這裡放置你的 Base64 編碼的公鑰
  const aesKeyBase64 = "YOUR_AES_KEY_IN_BASE64";       // 這裡放置你的 Base64 編碼的 AES 密鑰

  const encryptedAesKey = encryptWithPublicKey(publicKeyBase64, aesKeyBase64);
  console.log(`加密後的 AES 密鑰 (Base64): ${encryptedAesKey}`); 

  ```  

### 登入步驟 5

* 前端呼叫系統 API `使用者登入`，提供 `使用者帳號`、`步驟 3 的待用值`、`步驟 1 的一次性標誌(nonce)`、以及 `步驟 4 的待用值` 這四個資料作為 request body，取得登入資訊。 登入資訊包含使用者編號、顯示名稱、所屬角色、具有的權限、access token、access token 逾期時間、以及 refresh token。
* 若是使用 Postman 測試，呼叫下列 API 執行登入：

  ```http request
  
  HTTP POST 
  /api/public/login
  
  ```
  輸入的 reques body 如下，為 JSON 格式，其中：
  * `username` 是使用者登入帳號
  * `password` 是加密後的密碼，即步驟 3 的回傳值
  * `nonce` 是步驟 1 取得的一次性標誌(nonce)，同時作為步驟 3 加密密碼時需要的 initialization vector (IV)
  * `encryption` 是加密後的 AES 密鑰，即步驟 4 的回傳值

  ```json
  
  {
      "username": "admin",
      "password": "VokhiTN9ooLRBbWLLeHEEA==",
      "nonce": "1J65SvUBMsRfymGWaFGKjw==",
      "encryption": "WOWVh6LyjaNViMHF+MbiZTMCd3CX5CBlnJllz9CWxPsrzVVe7jdxA3wodXozPRbXz7v3SeD43WCl1fDCEb3KM/oXofQmIuejvJKKQAETEjo3+vHpARRILB/QbIL43hl2juStYloKBDvHwni9hqwVDrWbKBG1aKCBWoVnpa89fD+xTUbh/s7uaP22NmZw3uLIKf5xS0vvhkvHuJlVol4wodwrxobLQ/taS8bLMPC0BS9eA0hQ02T48pHCul9sAzxa+llNEWtQk8myFySzRMw/jl6I+4wFzcMJ6nIapEBlOBnzwsXE2Lv09CjbjwpebKiwVcdBpQD/eFsLFvTO2nfLSQ=="
  }

  ```

  取得回傳值範例：

  ```json

  {
    "userId": "d9d3d4e2-ec90-4cf0-b984-c131d901e38e",
    "username": "admin",
    "displayname": "系統管理員",
    "userType": "EMPLOYEE",
    "enabled": true,
    "createdAt": *************,
    "createdBy": "d9d3d4e2-ec90-4cf0-b984-c131d901e38e",
    "modifiedAt": null,
    "modifiedBy": null,
    "roleNames": [
        "ACCOUNT_EDITOR",
        "SYS_ADMIN"
    ],
    "permissionNames": [
        "ACCOUNT_MODIFY",
        "ACCOUNT_READ",
        "ACCOUNT_DELETE",
        "ACCOUNT_CREATE"
    ],
    "accessToken": "eyJhbGciOiJIUzI1NiJ9.******************************************************************.gndUxG1ACLg3wBPGKwuh_yNNSNHaIVHAhBiuqufyzA8",
    "expiredAt": *************,
    "refreshToken": "eyJhbGciOiJIUzI1NiJ9.******************************************************************.b0I6l5-c3vIYoAZCdzl_miYezgP32F3nqKT3uiUgKPA"
  }
  
  ```

* 若為 Typescript，則可以參考下列程式發送登入請求，並取得回傳值中的 `accessToken`、`refreshToken`、`roleNames`、與 `permissionNames` 的值

  ```typescript

  import axios from 'axios';
  
  async function loginUser(): Promise<void> {
  const url = 'http://127.0.0.1:8080/api/public/login';
  const requestBody = {
  username: "admin",
  password: "VokhiTN9ooLRBbWLLeHEEA==",
  nonce: "1J65SvUBMsRfymGWaFGKjw==",
  encryption: "WOWVh6LyjaNViMHF+MbiZTMCd3CX5CBlnJllz9CWxPsrzVVe7jdxA3wodXozPRbXz7v3SeD43WCl1fDCEb3KM/oXofQmIuejvJKKQAETEjo3+vHpARRILB/QbIL43hl2juStYloKBDvHwni9hqwVDrWbKBG1aKCBWoVnpa89fD+xTUbh/s7uaP22NmZw3uLIKf5xS0vvhkvHuJlVol4wodwrxobLQ/taS8bLMPC0BS9eA0hQ02T48pHCul9sAzxa+llNEWtQk8myFySzRMw/jl6I+4wFzcMJ6nIapEBlOBnzwsXE2Lv09CjbjwpebKiwVcdBpQD/eFsLFvTO2nfLSQ=="
  };
  
      try {
          const response = await axios.post(url, requestBody);
  
          if (response.data) {
              const {
                  accessToken,
                  refreshToken,
                  roleNames,
                  permissionNames
              } = response.data;
  
              console.log('accessToken:', accessToken);
              console.log('refreshToken:', refreshToken);
              console.log('roleNames:', roleNames);
              console.log('permissionNames:', permissionNames);
          }
      } catch (error) {
          console.error("Error logging in:", error);
      }
  }
  
  // Call the function to login and get the values
  loginUser();

  ``` 


### 快速取得權限

* 使用 Postman 測試，呼叫下列 API 取得accessToken：
* 預設為 `admin`，如要登入其他使用者，請輸入帳號密碼

  ```http request
  
  HTTP GET 
  /api/internal/getAccessToken
  
  ```

*  access token 值作為後續呼叫系統 API 時，作為 Header `Authorization` 的值，前綴字串為 `Bearer `

取得回傳值範例：

```text

eyJhbGciOiJIUzI1NiJ9.******************************************************************.gndUxG1ACLg3wBPGKwuh_yNNSNHaIVHAhBiuqufyzA8
```

  






