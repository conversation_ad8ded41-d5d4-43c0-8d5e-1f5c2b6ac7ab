# 使用 Postman URL Encode 功能

## 為什麼要 URL encode

URL encoding，又稱為百分比編碼（percent encoding），是因為網址（URL）中只允許包含某些字符。當我們想在URL中加入非標準的字符時，這些字符必須經過編碼，以避免混淆或造成不必要的問題。
以下是為什麼需要URL encode的幾個原因：

1. 保留字符：

    URL中有許多保留字符，如?, &, =, # 等。這些字符在URL中有特定的意義。例如，? 用於開始查詢字符串，& 用於分隔查詢參數。如果我們想在URL中作為普通字符使用它們（例如作為查詢參數的一部分），我們必須編碼它們，以避免混淆。

2. 非ASCII字符：

    URL原始規範是基於ASCII字符集的。因此，非ASCII字符（例如許多非英文字符）必須經過編碼才能安全地放在URL中。

3. 不安全的字符：

    有些字符可能會因為它們在某些上下文中的含義而被認為是不安全的。例如，空格可能會被視為URL中的多個詞組之間的分隔符。為了避免混淆，空格和其他不安全的字符應該被編碼。

4. 瀏覽器兼容性：

    不同的瀏覽器和客戶端可能對非標準或特殊字符的處理方式有所不同。URL編碼確保了統一的解釋和處理方式。

5. 數據完整性：

    在某些情境下，如提交表單，URL編碼確保數據的完整性和一致性，避免數據在傳輸過程中被錯誤解釋或修改。


總的來說，URL encoding是為了確保URL的安全傳輸和一致解釋，並使其適應網路標準。

## Swagger UI 的參數為什麼不用 URL encode

* 因為 Swagger UI 的 API 參數會自動 URL encode

## Postman 上執行 URL encode 方式

* 如下圖所示，選取參數值，按右鍵點選 `EncodeURIComponent` 即可將參數轉成 URL encode 後的值

![Postman URL Encode](nap_009.jpg)