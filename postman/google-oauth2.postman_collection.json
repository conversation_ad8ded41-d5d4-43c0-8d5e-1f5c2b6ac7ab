{"info": {"_postman_id": "af77605b-04f8-4025-9f96-71a03a815b1c", "name": "google-oauth2", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "10533107"}, "item": [{"name": "g01.登入", "item": [{"name": "g101.開啟瀏覽器，執行此網址登入，登入後從網址列中取回參數code的值", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"followRedirects": false, "followOriginalHttpMethod": false, "disableCookies": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{auth_uri}}?response_type=code&client_id={{auth_client_id}}&redirect_uri={{redirect_uri}}&scope={{auth_scope}}&access_type=offline&prompt=consent", "host": ["{{auth_uri}}"], "query": [{"key": "response_type", "value": "code"}, {"key": "client_id", "value": "{{auth_client_id}}"}, {"key": "redirect_uri", "value": "{{redirect_uri}}"}, {"key": "scope", "value": "{{auth_scope}}"}, {"key": "access_type", "value": "offline", "description": "回應時才能取得 refresh_token"}, {"key": "prompt", "value": "consent", "description": "回應時才能取得 refresh_token"}]}}, "response": []}, {"name": "g102.拿code換access token", "event": [{"listen": "test", "script": {"exec": ["var body = pm.response.json();", "", "// persist access token", "pm.globals.set(\"accessToken\",body.access_token);", "", "// persist refresh token", "pm.globals.set(\"refreshToken\",body.refresh_token);"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"followRedirects": true, "disableCookies": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "authorization_code", "type": "text"}, {"key": "redirect_uri", "value": "{{redirect_uri}}", "type": "text"}, {"key": "code", "value": "4/0Ab_5qlnpl9zSgygIESmKAWUHg7q7jsGeF8O6kcKvnH-bTM-zWz5ByIo2POZ1mVfHH54-cQ", "description": "上一步登入後，瀏覽器上的網址列中的參數 code 的值", "type": "text"}]}, "url": {"raw": "{{token_uri}}", "host": ["{{token_uri}}"]}}, "response": []}, {"name": "g103.拿refresh token換新的access token", "event": [{"listen": "test", "script": {"exec": ["var body = pm.response.json();", "", "// persist access token", "pm.globals.set(\"accessToken\",body.access_token);", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"followRedirects": true, "disableCookies": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "refresh_token", "type": "text"}, {"key": "refresh_token", "value": "{{refreshToken}}", "type": "text"}]}, "url": {"raw": "{{token_uri}}", "host": ["{{token_uri}}"]}}, "response": []}, {"name": "g104.登出(註銷access token, 實際上連refresh_token也會失效)", "event": [{"listen": "test", "script": {"exec": ["// clear authorization", "pm.globals.unset(\"accessToken\");"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"followRedirects": true, "disableCookies": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "token", "value": "{{accessToken}}", "type": "text"}]}, "url": {"raw": "{{revoke_uri}}", "host": ["{{revoke_uri}}"]}}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "{{auth_client_secret}}", "type": "string"}, {"key": "username", "value": "{{auth_client_id}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}, {"name": "g02.oauth2資訊", "item": [{"name": "g201.列出Google OAuth 2.0/OIDC服務的所有相關端點和設定資訊", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "{{openid_conf_uri}}", "host": ["{{openid_conf_uri}}"]}}, "response": []}, {"name": "g202.支援OpenID Connect取得ID Token解密方式", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded; charset=utf-8", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": []}, "url": {"raw": "https://www.googleapis.com/oauth2/v3/certs", "protocol": "https", "host": ["www", "googlea<PERSON>", "com"], "path": ["oauth2", "v3", "certs"]}}, "response": []}, {"name": "g203.支援OpenID Connect取得驗證 ID Token的公鑰", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{x509_cert_uri}}", "host": ["{{x509_cert_uri}}"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "auth_uri", "value": "https://accounts.google.com/o/oauth2/auth", "type": "string"}, {"key": "token_uri", "value": "https://oauth2.googleapis.com/token", "type": "string"}, {"key": "revoke_uri", "value": "https://oauth2.googleapis.com/revoke", "type": "string"}, {"key": "auth_client_id", "value": "************-ig1b5vhesvtp8o3vtfa9bjqc2a0mc5jp.apps.googleusercontent.com", "type": "string"}, {"key": "auth_client_secret", "value": "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", "type": "string"}, {"key": "redirect_uri", "value": "https://www.jgallop.com", "type": "string"}, {"key": "auth_scope", "value": "https://mail.google.com/", "type": "string"}, {"key": "introspect_uri", "value": "https://accounts.google.com/o/oauth2/introspect", "type": "string"}, {"key": "openid_conf_uri", "value": "https://accounts.google.com/.well-known/openid-configuration", "type": "string"}, {"key": "x509_cert_uri", "value": "https://www.googleapis.com/oauth2/v1/certs", "type": "string"}]}